// Test script to verify better-sqlite3 works in Electron environment
const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // Test better-sqlite3 loading
  try {
    console.log('🔍 Testing better-sqlite3 loading...');
    const Database = require('better-sqlite3');
    console.log('✅ better-sqlite3 loaded successfully');
    
    // Test database creation
    const testDbPath = path.join(__dirname, 'test.db');
    console.log('📁 Creating test database at:', testDbPath);
    
    const db = new Database(testDbPath);
    console.log('✅ Database created successfully');
    
    // Test table creation
    db.exec(`
      CREATE TABLE IF NOT EXISTS test_users (
        id INTEGER PRIMARY KEY,
        username TEXT,
        password TEXT
      )
    `);
    console.log('✅ Table created successfully');
    
    // Test data insertion
    const stmt = db.prepare('INSERT INTO test_users (username, password) VALUES (?, ?)');
    stmt.run('testuser', 'testpass');
    console.log('✅ Data inserted successfully');
    
    // Test data retrieval
    const user = db.prepare('SELECT * FROM test_users WHERE username = ?').get('testuser');
    console.log('✅ Data retrieved:', user);
    
    db.close();
    console.log('✅ Database test completed successfully');
    
  } catch (error) {
    console.error('❌ better-sqlite3 test failed:', error);
    console.error('❌ Error stack:', error.stack);
  }

  mainWindow.loadURL('data:text/html,<h1>Check console for better-sqlite3 test results</h1>');
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

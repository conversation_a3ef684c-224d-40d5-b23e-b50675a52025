import React, { useState, useEffect } from 'react';
import './App.css';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DatabaseProvider, useDatabase } from './contexts/DatabaseContext';
import DatabaseLoader from './components/DatabaseLoader';
import { getPermissions } from './types/auth';
import Login from './components/Login';
import LivreForm from './components/LivreForm';
import LivreList from './components/LivreList';
import Categories from './components/Categories';
import CategoryManagement from './components/CategoryManagement';
import UtilisateurList from './components/UtilisateurList';
import UserManagement from './components/UserManagement';
import MemberManagement from './components/MemberManagement';
import SuiviPrets from './components/SuiviPrets';
import BulkImport from './components/BulkImport';
import AuditLogs from './components/AuditLogs';
import type { Utilisateur } from './components/UtilisateurList';
import type { Book, Category, BookImportData, ImportResult } from './types/library';
import type { LibraryMember, CreateMemberData, UpdateMemberData } from './types/member';
import { generateMembershipNumber } from './types/member';
import { sanitizeBookData } from './types/library';
import AssignBookModal from './components/AssignBookModal';
import ToastContainer from './components/ToastContainer';
import ChangePasswordModal from './components/ChangePasswordModal';
import { useToast } from './hooks/useToast';
import {
  HomeIcon,
  BookIcon,
  CategoryIcon,
  UsersIcon,
  ClipboardIcon,
  UploadIcon,
  SettingsIcon,
  ShieldIcon,
  LogoutIcon,
  LockIcon
} from './components/Icons';



// Main App Component with Authentication
const AppContent: React.FC = () => {
  const { user, logout, logAction, changePassword } = useAuth();
  const {
    books: livres,
    categories,
    members,
    memberProfiles,

    addBook,
    updateBook,
    deleteBook,
    addCategory,
    updateCategory,
    deleteCategory,
    addMember,
    updateMember,
    deleteMember,
    createLoan,
    returnBook,
    extendLoan,
    getStats
  } = useDatabase();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // Convert members to Utilisateur format for compatibility with UtilisateurList component
  const convertMembersToUtilisateurs = (members: LibraryMember[]): Utilisateur[] => {
    return members.map(member => ({
      id: member.id,
      nom: `${member.prenom} ${member.nom}`,
      photo: member.photo || `https://ui-avatars.com/api/?name=${encodeURIComponent(member.prenom + ' ' + member.nom)}&background=0ea5e9&color=fff`
    }));
  };

  // Get dynamic user list from actual members data
  const utilisateurs = convertMembersToUtilisateurs(members);
  const [page, setPage] = useState('accueil');
  const [selectedCategory, setSelectedCategory] = useState(categories[0]?.name || '');
  const [searchTerm, setSearchTerm] = useState('');
  const [authorSearchTerm, setAuthorSearchTerm] = useState('');
  const [showBulkImport, setShowBulkImport] = useState(false);
  const [bookFilter, setBookFilter] = useState<'all' | 'available' | 'borrowed' | 'overdue' | 'due-soon'>('all');
  const [loanTrackingFilter, setLoanTrackingFilter] = useState<'all' | 'overdue' | 'due-soon'>('all');
  const [assignBookModal, setAssignBookModal] = useState<{
    isOpen: boolean;
    book: Book | null;
  }>({
    isOpen: false,
    book: null
  });
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);

  const permissions = user ? getPermissions(user.role) : null;

  // Navigation items based on permissions
  const getNavItems = () => {
    const baseNavs = [
      { key: 'accueil', label: 'Tableau de bord', icon: HomeIcon },
    ];

    if (permissions?.canManageBooks) {
      baseNavs.push({ key: 'livres', label: 'Gestion des livres', icon: BookIcon });
      baseNavs.push({ key: 'categories', label: 'Catégories', icon: CategoryIcon });
    }

    if (permissions?.canManageUsers) {
      baseNavs.push({ key: 'profils', label: 'Profils utilisateurs', icon: UsersIcon });
      baseNavs.push({ key: 'gestion-membres', label: 'Gestion des membres', icon: UsersIcon });
    }

    if (permissions?.canManageBooks) {
      baseNavs.push({ key: 'prets', label: 'Suivi des prêts', icon: ClipboardIcon });
    }

    if (permissions?.canManageCategories) {
      baseNavs.push({ key: 'gestion-categories', label: 'Gestion catégories', icon: SettingsIcon });
    }

    if (permissions?.canManageAdministrators) {
      baseNavs.push({ key: 'gestion-utilisateurs', label: 'Gestion admin', icon: ShieldIcon });
    }

    if (permissions?.canViewAuditLogs) {
      baseNavs.push({ key: 'audit', label: 'Journaux d\'audit', icon: ClipboardIcon });
    }

    return baseNavs;
  };

  const navItems = getNavItems();

  // Navigation functions for dashboard stat cards
  const navigateToBooks = (filter: 'all' | 'available' | 'borrowed' | 'overdue' | 'due-soon' = 'all') => {
    setBookFilter(filter);
    setPage('livres');
    // Clear search terms when navigating from dashboard
    setSearchTerm('');
    setAuthorSearchTerm('');
  };

  const navigateToLoanTracking = (filter: 'all' | 'overdue' | 'due-soon' = 'all') => {
    setLoanTrackingFilter(filter);
    setPage('prets');
  };

  const navigateToMembers = () => {
    setPage('gestion-membres');
  };

  // Password change handler
  const handlePasswordChange = async (currentPassword: string, newPassword: string): Promise<boolean> => {
    try {
      const success = await changePassword(currentPassword, newPassword);
      if (success) {
        showSuccess('Mot de passe modifié', 'Votre mot de passe a été modifié avec succès.');
      } else {
        showError('Erreur', 'Impossible de modifier le mot de passe. Vérifiez votre mot de passe actuel.');
      }
      return success;
    } catch (error) {
      console.error('Password change error:', error);
      showError('Erreur', 'Une erreur inattendue s\'est produite.');
      return false;
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        const navKeys = navItems.map((_, index) => (index + 1).toString());
        const keyIndex = navKeys.indexOf(e.key);

        if (keyIndex !== -1) {
          e.preventDefault();
          setPage(navItems[keyIndex].key);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [navItems]);

  // Helper function to convert BookImportData to Book format
  const convertBookImportDataToBook = (bookData: BookImportData): Omit<Book, 'id' | 'createdAt' | 'updatedAt' | 'disponible'> => {
    const quantity = bookData.quantite || 1;
    return {
      titre: bookData.titre,
      auteur: bookData.auteur,
      categorie: bookData.categorie,
      genre: bookData.genre,
      isbn: bookData.isbn,
      anneePublication: bookData.anneePublication,
      description: bookData.description,
      quantiteTotale: quantity,
      quantiteDisponible: quantity
    };
  };

  // Book management functions
  const handleAddLivre = async (livre: BookImportData) => {
    try {
      const bookForDatabase = convertBookImportDataToBook(livre);
      const newBook = await addBook(bookForDatabase);
      if (user) {
        await logAction('CREATE', 'BOOK', newBook.id, `Livre créé: ${livre.titre} (${livre.quantite || 1} exemplaire${(livre.quantite || 1) > 1 ? 's' : ''})`);
      }
      showSuccess(`Le livre "${livre.titre}" a été ajouté avec succès avec ${newBook.quantiteTotale} exemplaire${newBook.quantiteTotale > 1 ? 's' : ''}`);
    } catch (error) {
      console.error('Erreur lors de l\'ajout du livre:', error);
      showSuccess(`Erreur lors de l'ajout du livre: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
    }
  };

  const handleUpdateLivre = async (id: number, updates: Partial<Book>) => {
    try {
      const book = livres.find(l => l.id === id);
      if (!book) return;

      const success = await updateBook(id, updates);
      if (success) {
        if (user) {
          await logAction('UPDATE', 'BOOK', id, `Livre modifié: ${updates.titre || book.titre}`);
        }
        showSuccess(`Le livre "${updates.titre || book.titre}" a été modifié avec succès`);
      }
    } catch (error) {
      console.error('Erreur lors de la modification du livre:', error);
      showSuccess(`Erreur lors de la modification: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
    }
  };

  const handleDeleteLivre = async (id: number) => {
    try {
      const book = livres.find(l => l.id === id);
      const success = await deleteBook(id);
      if (success && book) {
        if (user) {
          await logAction('DELETE', 'BOOK', id, `Livre supprimé: ${book.titre}`);
        }
        showSuccess(`Le livre "${book.titre}" a été supprimé avec succès`);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du livre:', error);
      showSuccess(`Erreur lors de la suppression: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
    }
  };

  const handleToggleAvailability = async (id: number, memberId?: number) => {
    try {
      const book = livres.find(l => l.id === id);
      if (!book) return;

      if (book.disponible && memberId) {
        try {
          // Créer un emprunt - mark book as borrowed
          const today = new Date();
          const dueDate = new Date();
          dueDate.setDate(today.getDate() + 14); // Default 14 days loan period

          const success = await createLoan(id, memberId, today.toISOString().split('T')[0], dueDate.toISOString().split('T')[0]);
          if (success) {
            const member = members.find(m => m.id === memberId);
            const memberName = member ? `${member.prenom} ${member.nom}` : 'Membre inconnu';

            if (user) {
              await logAction('UPDATE', 'BOOK', id, `Livre emprunté par ${memberName}`);
            }
            showSuccess(`Le livre "${book.titre}" a été emprunté par ${memberName}`);
          }
        } catch (error) {
          console.error('Erreur lors de l\'emprunt du livre:', error);
          const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue lors de l\'emprunt du livre';

          // Enhanced error handling for duplicate book borrowing
          if (errorMessage.includes('déjà emprunté ce livre')) {
            showError(
              'Emprunt impossible',
              errorMessage,
              8000 // Longer duration for important error
            );
          } else {
            showError(
              'Erreur d\'emprunt',
              errorMessage
            );
          }
        }
      } else if (!book.disponible) {
        // Retourner le livre - mark book as available
        await handleReturnBook(id);
      } else if (book.disponible && !memberId) {
        // Manual toggle to mark book as unavailable (borrowed without specific member)
        const success = await updateBook(id, { disponible: false });
        if (success) {
          if (user) {
            await logAction('UPDATE', 'BOOK', id, `Livre marqué comme emprunté: ${book.titre}`);
          }
          showSuccess(`Le livre "${book.titre}" a été marqué comme emprunté`);
        }
      }
    } catch (error) {
      console.error('Erreur lors du changement de statut:', error);
      showSuccess(`Erreur: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
    }
  };

  // Category management functions
  const handleAddCategory = async (name: string, description?: string): Promise<boolean> => {
    try {
      await addCategory(name, description);
      if (user) {
        await logAction('CREATE', 'CATEGORY', undefined, `Catégorie créée: ${name}`);
      }
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout de la catégorie:', error);
      // Relancer l'erreur pour que le composant puisse l'afficher correctement
      throw error;
    }
  };

  const handleUpdateCategory = async (id: number, name: string, description?: string): Promise<boolean> => {
    try {
      const success = await updateCategory(id, name, description);
      if (success && user) {
        await logAction('UPDATE', 'CATEGORY', id, `Catégorie modifiée: ${name}`);
      }
      return success;
    } catch (error) {
      console.error('Erreur lors de la modification de la catégorie:', error);
      // Relancer l'erreur pour que le composant puisse l'afficher correctement
      throw error;
    }
  };

  const handleDeleteCategory = async (id: number): Promise<boolean> => {
    try {
      const category = categories.find(c => c.id === id);
      const success = await deleteCategory(id);
      if (success && category && user) {
        await logAction('DELETE', 'CATEGORY', id, `Catégorie supprimée: ${category.name}`);
      }
      return success;
    } catch (error) {
      console.error('Erreur lors de la suppression de la catégorie:', error);
      return false;
    }
  };

  const handleReorderCategories = async (reorderedCategories: Category[]): Promise<boolean> => {
    try {
      // Note: reorderCategories method not implemented in current database context
      // This would need to be added to the database service
      console.log('Reorder categories not yet implemented:', reorderedCategories);
      return true;
    } catch (error) {
      console.error('Erreur lors de la réorganisation des catégories:', error);
      return false;
    }
  };

  // Member management functions
  const handleAddMember = async (memberData: CreateMemberData & { numeroMembre?: string }): Promise<boolean> => {
    try {
      // Générer un numéro de membre si non fourni
      const numeroMembre = memberData.numeroMembre || generateMembershipNumber(Date.now());

      // Générer une photo par défaut si non fournie
      const photo = memberData.photo || `https://ui-avatars.com/api/?name=${encodeURIComponent(memberData.prenom + ' ' + memberData.nom)}&background=0ea5e9&color=fff`;

      const memberToAdd: Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'> = {
        ...memberData,
        numeroMembre,
        photo,
        dateInscription: new Date().toISOString().split('T')[0],
        isActive: true
      };

      const newMember = await addMember(memberToAdd);

      if (user) {
        await logAction('CREATE', 'MEMBER', newMember.id, `Membre créé: ${memberData.prenom} ${memberData.nom}`);
      }

      showSuccess(`Le membre "${memberData.prenom} ${memberData.nom}" a été ajouté avec succès`);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'ajout du membre:', error);
      showSuccess(`Erreur lors de l'ajout du membre: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
      return false;
    }
  };

  const handleUpdateMember = async (id: number, updates: UpdateMemberData): Promise<boolean> => {
    try {
      const success = await updateMember(id, updates);
      if (success && user) {
        await logAction('UPDATE', 'MEMBER', id, `Membre modifié`);
      }
      return success;
    } catch (error) {
      console.error('Erreur lors de la modification du membre:', error);
      return false;
    }
  };

  const handleDeleteMember = async (id: number): Promise<boolean> => {
    try {
      const member = members.find(m => m.id === id);
      const success = await deleteMember(id);
      if (success && member && user) {
        await logAction('DELETE', 'MEMBER', id, `Membre supprimé: ${member.prenom} ${member.nom}`);
      }
      return success;
    } catch (error) {
      console.error('Erreur lors de la suppression du membre:', error);
      return false;
    }
  };

  const handleReturnBook = async (bookId: number | string) => {
    try {
      const id = typeof bookId === 'string' ? parseInt(bookId) : bookId;
      const book = livres.find(l => l.id === id);

      const success = await returnBook(id);
      if (success) {
        showSuccess(
          'Livre retourné avec succès',
          `Le livre "${book?.titre || 'Livre inconnu'}" a été marqué comme retourné et est maintenant disponible`,
          5000
        );

        if (user) {
          await logAction('UPDATE', 'LOAN', id, `Livre retourné: ${book?.titre || 'Livre inconnu'}`);
        }
      }
    } catch (error) {
      console.error('Erreur lors du retour du livre:', error);
      showSuccess(`Erreur lors du retour: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
    }
  };

  const handleExtendLoan = async (bookId: number | string, newDueDate: string) => {
    try {
      const id = typeof bookId === 'string' ? parseInt(bookId) : bookId;
      const book = livres.find(l => l.id === id);

      const success = await extendLoan(id, newDueDate);
      if (success) {
        const currentDate = new Date(book?.dateRetour || Date.now());
        const newDate = new Date(newDueDate);
        const daysDiff = Math.ceil((newDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));

        const formattedDate = new Date(newDueDate).toLocaleDateString('fr-FR', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        showSuccess(
          'Prêt prolongé avec succès',
          `Le prêt de "${book?.titre || 'Livre inconnu'}" a été prolongé jusqu'au ${formattedDate}`,
          6000
        );

        if (user) {
          await logAction('UPDATE', 'LOAN', id,
            `Prêt prolongé jusqu'au ${new Date(newDueDate).toLocaleDateString('fr-FR')} (+${daysDiff} jours): ${book?.titre || 'Livre inconnu'}`
          );
        }
      }
    } catch (error) {
      console.error('Erreur lors de la prolongation du prêt:', error);
      showSuccess(`Erreur lors de la prolongation: ${error instanceof Error ? error.message : 'Erreur inconnue'}`, '', 5000);
    }
  };

  // Note: All components now use the enhanced handleExtendLoan with date picker

  // Book assignment functions
  const handleAssignBook = (livre: any) => {
    // Convert Livre to Book format
    const book: Book = {
      ...livre,
      createdAt: livre.createdAt || new Date().toISOString(),
      updatedAt: livre.updatedAt || new Date().toISOString()
    };

    setAssignBookModal({
      isOpen: true,
      book: book
    });
  };

  const handleAssignBookConfirm = async (memberId: number, startDate: string, dueDate: string): Promise<void> => {
    if (!assignBookModal.book) return;

    const bookId = assignBookModal.book.id;
    const member = members.find(m => m.id === memberId);

    const success = await createLoan(bookId, memberId, startDate, dueDate);
    if (success && member) {
      showSuccess(
        'Livre assigné avec succès',
        `Le livre "${assignBookModal.book.titre}" a été assigné à ${member.prenom} ${member.nom}`,
        5000
      );

      if (user) {
        await logAction('CREATE', 'LOAN', bookId,
          `Livre "${assignBookModal.book.titre}" assigné à ${member.prenom} ${member.nom} (du ${new Date(startDate).toLocaleDateString('fr-FR')} au ${new Date(dueDate).toLocaleDateString('fr-FR')})`
        );
      }

      // Close modal only on success
      setAssignBookModal({
        isOpen: false,
        book: null
      });
    }
  };

  const handleAssignBookClose = () => {
    setAssignBookModal({
      isOpen: false,
      book: null
    });
  };

  // Bulk import function
  const handleBulkImport = async (books: BookImportData[]): Promise<ImportResult> => {
    let success = 0;
    let errors = 0;
    let duplicates = 0;
    const errorDetails: any[] = [];

    for (const bookData of books) {
      // Check for duplicates
      const isDuplicate = livres.some(existing =>
        existing.titre.toLowerCase() === bookData.titre.toLowerCase() &&
        existing.auteur.toLowerCase() === bookData.auteur.toLowerCase()
      );

      if (isDuplicate) {
        duplicates++;
        continue;
      }

      try {
        const sanitizedData = sanitizeBookData(bookData);
        const bookForDatabase = convertBookImportDataToBook(sanitizedData);
        await addBook(bookForDatabase);
        success++;
      } catch (error) {
        errors++;
        errorDetails.push({ row: 0, message: 'Erreur lors de l\'ajout', data: bookData });
      }
    }

    if (user && success > 0) {
      await logAction('BULK_IMPORT', 'BOOK', undefined, `Import en lot: ${success} livres ajoutés`);
    }

    return { success, errors, duplicates, total: books.length, errorDetails };
  };

  // Enhanced statistics for dashboard with real-time database synchronization
  // Calculate statistics from actual data to ensure accuracy
  const [dbStats, setDbStats] = useState({
    totalBooks: 0,
    borrowedBooks: 0,
    totalMembers: 0,
    overdueBooks: 0,
    dueSoonBooks: 0,
  });

  // Calculate derived data for alerts and statistics (still needed for alert display)
  const borrowedBooks = livres.filter(l => !l.disponible && l.emprunteurId);

  const overdueBooks = borrowedBooks.filter(book => {
    if (!book.dateRetour) return false;
    const dateRetour = new Date(book.dateRetour);
    const today = new Date();
    return dateRetour < today;
  });

  const dueSoonBooks = borrowedBooks.filter(book => {
    if (!book.dateRetour) return false;
    const dueDate = new Date(book.dateRetour);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 3 && diffDays >= 0;
  });

  // Update database statistics using database queries for consistency
  useEffect(() => {
    const updateStats = async () => {
      try {
        // Get statistics from database context to ensure consistency with filtered views
        const stats = await getStats();
        setDbStats(stats);
      } catch (error) {
        console.error('Error fetching database statistics:', error);
        // Fallback to frontend calculations if database query fails
        setDbStats({
          totalBooks: livres.length,
          borrowedBooks: borrowedBooks.length,
          totalMembers: members.length,
          overdueBooks: overdueBooks.length,
          dueSoonBooks: dueSoonBooks.length,
        });
      }
    };

    updateStats();
  }, [livres, members, borrowedBooks.length, overdueBooks.length, dueSoonBooks.length, getStats]);

  const stats = dbStats;

  const currentPageConfig = navItems.find(nav => nav.key === page);
  const categoryNames = categories.map(c => c.name);

  return (
    <div className="app">
      <div className="app-layout">
        {/* Sidebar Navigation */}
        <aside className="sidebar">
          <div className="sidebar-header">
            <h1 className="sidebar-title">BiblioTech</h1>
            <p className="sidebar-subtitle">Système de gestion moderne</p>
          </div>

          <nav>
            <ul className="nav-menu">
              {navItems.map(nav => {
                const IconComponent = nav.icon;
                return (
                  <li key={nav.key} className="nav-item">
                    <a
                      href="#"
                      className={`nav-link ${page === nav.key ? 'active' : ''}`}
                      onClick={(e) => {
                        e.preventDefault();
                        setPage(nav.key);
                      }}
                    >
                      <IconComponent className="nav-icon" />
                      {nav.label}
                    </a>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* User info and logout */}
          <div className="mt-auto pt-6 border-t border-neutral-700">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-semibold">
                {user?.username.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="text-sm font-medium text-neutral-100">{user?.username}</div>
                <div className="text-xs text-neutral-400">{user?.role === 'super_admin' ? 'Super Admin' : 'Administrateur'}</div>
              </div>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => {
                  console.log('🔐 Password change button clicked');
                  console.log('🔐 Current showChangePasswordModal state:', showChangePasswordModal);
                  setShowChangePasswordModal(true);
                  console.log('🔐 Password modal should now be open');
                }}
                className="flex items-center gap-2 w-full p-2 text-neutral-300 hover:text-neutral-100 hover:bg-neutral-800 rounded-lg transition-colors"
              >
                <LockIcon size={16} />
                Changer le mot de passe
              </button>
              <button
                onClick={logout}
                className="flex items-center gap-2 w-full p-2 text-neutral-300 hover:text-neutral-100 hover:bg-neutral-800 rounded-lg transition-colors"
              >
                <LogoutIcon size={16} />
                Déconnexion
              </button>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="main-content">
          <div className="page-header">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center gap-3">
                  <h1 className="page-title">{currentPageConfig?.label}</h1>
                  {page === 'livres' && bookFilter !== 'all' && (
                    <div className="flex items-center gap-2">
                      <span className="filter-badge">
                        {bookFilter === 'available' && 'Disponibles'}
                        {bookFilter === 'borrowed' && 'Empruntés'}
                        {bookFilter === 'overdue' && 'En retard'}
                        {bookFilter === 'due-soon' && 'À rendre bientôt'}
                      </span>
                      <button
                        className="clear-filter-btn"
                        onClick={() => setBookFilter('all')}
                        title="Afficher tous les livres"
                      >
                        ×
                      </button>
                    </div>
                  )}
                </div>
                <p className="page-description">
                  {page === 'accueil' && 'Vue d\'ensemble de votre bibliothèque'}
                  {page === 'livres' && (
                    bookFilter === 'all' ? 'Gérez votre collection de livres avec recherche par auteur' :
                    bookFilter === 'available' ? 'Livres disponibles pour emprunt' :
                    bookFilter === 'borrowed' ? 'Livres actuellement empruntés' :
                    bookFilter === 'overdue' ? 'Livres en retard - Action requise' :
                    bookFilter === 'due-soon' ? 'Livres à rendre bientôt (3 jours)' :
                    'Gérez votre collection de livres avec recherche par auteur'
                  )}
                  {page === 'categories' && 'Explorez les livres par catégorie'}
                  {page === 'profils' && 'Consultez les profils détaillés des utilisateurs'}
                  {page === 'gestion-membres' && 'Gérez les membres de la bibliothèque'}
                  {page === 'prets' && 'Suivez les prêts en cours'}
                  {page === 'gestion-categories' && 'Gérez les catégories de livres'}
                  {page === 'gestion-utilisateurs' && 'Gérez les comptes administrateurs'}
                  {page === 'audit' && 'Consultez les journaux d\'audit'}
                </p>
              </div>
              {page === 'livres' && permissions?.canImportBooks && (
                <button
                  onClick={() => setShowBulkImport(true)}
                  className="btn btn-primary"
                >
                  <UploadIcon size={16} />
                  Import en lot
                </button>
              )}
            </div>
          </div>

          {page === 'accueil' && (
            <div>
              <div className="stats-grid">
                <div className="stat-card stat-card--clickable" onClick={() => navigateToBooks('all')}>
                  <div className="stat-value">{stats.totalBooks}</div>
                  <div className="stat-label">Nombre total de livres</div>
                </div>
                <div className="stat-card stat-card--clickable" onClick={() => navigateToBooks('borrowed')}>
                  <div className="stat-value">{stats.borrowedBooks}</div>
                  <div className="stat-label">Livres empruntés</div>
                </div>
                <div className={`stat-card stat-card--clickable ${stats.overdueBooks > 0 ? 'stat-card--alert' : ''}`} onClick={() => navigateToLoanTracking('overdue')}>
                  <div className={`stat-value ${stats.overdueBooks > 0 ? 'text-error' : ''}`}>{stats.overdueBooks}</div>
                  <div className="stat-label">Livres en retard</div>
                </div>
                <div className="stat-card stat-card--clickable" onClick={() => navigateToMembers()}>
                  <div className="stat-value">{stats.totalMembers}</div>
                  <div className="stat-label">Membres</div>
                </div>
                {stats.dueSoonBooks > 0 && (
                  <div className="stat-card stat-card--warning stat-card--clickable" onClick={() => navigateToBooks('due-soon')}>
                    <div className="stat-value text-warning">{stats.dueSoonBooks}</div>
                    <div className="stat-label">À rendre bientôt</div>
                  </div>
                )}
              </div>

              {/* Overdue Alerts Section */}
              {(overdueBooks.length > 0 || dueSoonBooks.length > 0) && (
                <div className="card mb-6">
                  <div className="card-header">
                    <h3 className="card-title text-error">⚠️ Alertes de prêts</h3>
                    <p className="card-subtitle">Livres nécessitant votre attention</p>
                  </div>
                  <div className="card-content">
                    {overdueBooks.length > 0 && (
                      <div className="alert alert-error mb-4">
                        <h4 className="font-semibold mb-2">Livres en retard ({overdueBooks.length})</h4>
                        <div className="space-y-2">
                          {overdueBooks.slice(0, 5).map(book => {
                            const member = members.find(m => m.id === book.emprunteurId);
                            const daysOverdue = Math.floor((new Date().getTime() - new Date(book.dateRetour!).getTime()) / (1000 * 60 * 60 * 24));
                            return (
                              <div key={book.id} className="flex justify-between items-center text-sm">
                                <span>"{book.titre}" - {member ? `${member.prenom} ${member.nom}` : 'Membre inconnu'}</span>
                                <span className="font-semibold">{daysOverdue} jour(s) de retard</span>
                              </div>
                            );
                          })}
                          {overdueBooks.length > 5 && (
                            <div className="text-sm text-neutral">
                              ... et {overdueBooks.length - 5} autre(s)
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {dueSoonBooks.length > 0 && (
                      <div className="alert alert-warning">
                        <h4 className="font-semibold mb-2">À rendre dans les 3 prochains jours ({dueSoonBooks.length})</h4>
                        <div className="space-y-2">
                          {dueSoonBooks.slice(0, 5).map(book => {
                            const member = members.find(m => m.id === book.emprunteurId);
                            const daysUntilDue = Math.ceil((new Date(book.dateRetour!).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                            return (
                              <div key={book.id} className="flex justify-between items-center text-sm">
                                <span>"{book.titre}" - {member ? `${member.prenom} ${member.nom}` : 'Membre inconnu'}</span>
                                <span className="font-semibold">Dans {daysUntilDue} jour(s)</span>
                              </div>
                            );
                          })}
                          {dueSoonBooks.length > 5 && (
                            <div className="text-sm text-neutral">
                              ... et {dueSoonBooks.length - 5} autre(s)
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Bienvenue dans BiblioTech</h3>
                  <p className="card-subtitle">Votre système de gestion de bibliothèque moderne</p>
                </div>
                <div className="card-content">
                  <p>
                    BiblioTech vous permet de gérer efficacement votre collection de livres,
                    de suivre les prêts et d'organiser vos utilisateurs. Utilisez la navigation
                    de gauche pour accéder aux différentes fonctionnalités.
                  </p>
                  <div className="mt-6">
                    <h4 className="font-semibold mb-4">Raccourcis clavier :</h4>
                    <ul className="text-sm text-neutral space-y-1">
                      <li>
                        <kbd>Ctrl + 1</kbd> - Tableau de bord
                      </li>
                      <li>
                        <kbd>Ctrl + 2</kbd> - Gestion des livres
                      </li>
                      <li>
                        <kbd>Ctrl + 5</kbd> - Gestion des membres
                      </li>
                    </ul>
                  </div>
                  <div className="mt-6">
                    <h4 className="font-semibold mb-4">Vos permissions :</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className={permissions?.canManageBooks ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageBooks ? '✓' : '✗'} Gestion des livres
                      </div>
                      <div className={permissions?.canManageCategories ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageCategories ? '✓' : '✗'} Gestion des catégories
                      </div>
                      <div className={permissions?.canManageUsers ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageUsers ? '✓' : '✗'} Gestion des utilisateurs
                      </div>
                      <div className={permissions?.canManageAdministrators ? 'text-success' : 'text-neutral'}>
                        {permissions?.canManageAdministrators ? '✓' : '✗'} Gestion des admins
                      </div>
                      <div className={permissions?.canImportBooks ? 'text-success' : 'text-neutral'}>
                        {permissions?.canImportBooks ? '✓' : '✗'} Import en lot
                      </div>
                      <div className={permissions?.canViewAuditLogs ? 'text-success' : 'text-neutral'}>
                        {permissions?.canViewAuditLogs ? '✓' : '✗'} Journaux d'audit
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {page === 'livres' && (
            <>
              <LivreForm onAdd={handleAddLivre} categories={categoryNames} />
              <LivreList
                livres={livres}
                onDelete={handleDeleteLivre}
                onUpdate={handleUpdateLivre}
                onToggleAvailability={handleToggleAvailability}
                onAssignBook={handleAssignBook}
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                authorSearchTerm={authorSearchTerm}
                onAuthorSearchChange={setAuthorSearchTerm}
                initialFilter={bookFilter}
                onFilterChange={setBookFilter}
                categories={categoryNames}
              />
            </>
          )}

          {page === 'categories' && (
            <>
              <Categories categories={categoryNames} selected={selectedCategory} onSelect={setSelectedCategory} />
              <LivreList
                livres={livres.filter((l: Book) => l.categorie === selectedCategory)}
                onDelete={handleDeleteLivre}
                onUpdate={handleUpdateLivre}
                onToggleAvailability={handleToggleAvailability}
                onAssignBook={handleAssignBook}
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                authorSearchTerm={authorSearchTerm}
                onAuthorSearchChange={setAuthorSearchTerm}
                categories={categoryNames}
              />
            </>
          )}

          {page === 'gestion-categories' && (
            <CategoryManagement
              categories={categories}
              onAddCategory={handleAddCategory}
              onUpdateCategory={handleUpdateCategory}
              onDeleteCategory={handleDeleteCategory}
              onReorderCategories={handleReorderCategories}
              booksInCategory={async (categoryName: string) => {
                // Count books in category from current data
                return livres.filter(book => book.categorie === categoryName).length;
              }}
            />
          )}

          {page === 'profils' && (
            <UtilisateurList
              utilisateurs={utilisateurs}
              memberProfiles={memberProfiles}
            />
          )}

          {page === 'gestion-membres' && (
            <MemberManagement
              members={members}
              onAddMember={handleAddMember}
              onUpdateMember={handleUpdateMember}
              onDeleteMember={handleDeleteMember}
              borrowedBooks={livres.filter(book => !book.disponible)}
              onReturnBook={(bookId) => handleReturnBook(parseInt(bookId))}
              onExtendLoan={(bookId, newDueDate) => handleExtendLoan(parseInt(bookId), newDueDate)}
            />
          )}

          {page === 'gestion-utilisateurs' && (
            <UserManagement />
          )}

          {page === 'prets' && (
            <SuiviPrets
              livres={livres}
              utilisateurs={utilisateurs}
              onReturnBook={handleReturnBook}
              onExtendLoan={handleExtendLoan}
              initialFilter={loanTrackingFilter}
            />
          )}

          {page === 'audit' && (
            <AuditLogs />
          )}
        </main>
      </div>

      {/* Bulk Import Modal */}
      {showBulkImport && (
        <BulkImport
          isOpen={showBulkImport}
          onClose={() => setShowBulkImport(false)}
          onImport={handleBulkImport}
          categories={categoryNames}
        />
      )}

      {/* Assign Book Modal */}
      <AssignBookModal
        isOpen={assignBookModal.isOpen}
        onClose={handleAssignBookClose}
        onConfirm={handleAssignBookConfirm}
        book={assignBookModal.book}
        members={members}
        memberProfiles={memberProfiles}
      />

      {/* Change Password Modal */}
      <ChangePasswordModal
        isOpen={showChangePasswordModal}
        onClose={() => {
          console.log('🔐 Closing password modal');
          setShowChangePasswordModal(false);
        }}
        onPasswordChange={handlePasswordChange}
        username={user?.username || ''}
      />

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </div>
  );
};

// Main App wrapper with authentication
const App: React.FC = () => {
  return (
    <DatabaseProvider>
      <AuthProvider>
        <AppWrapper />
      </AuthProvider>
    </DatabaseProvider>
  );
};

// App wrapper to handle authentication state
const AppWrapper: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        backgroundColor: 'white',
        fontSize: '18px'
      }}>
        <div>Chargement de BiblioTech...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Login />;
  }

  return <DatabaseAppContent />;
};

// Composant qui utilise la base de données
const DatabaseAppContent: React.FC = () => {
  const { isLoading, error } = useDatabase();

  return (
    <DatabaseLoader isLoading={isLoading} error={error}>
      <AppContent />
    </DatabaseLoader>
  );
};

export default App;

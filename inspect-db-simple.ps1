# Script PowerShell pour inspecter la base de donnees BiblioTech
# PowerShell script to inspect BiblioTech database

# Chemin vers la base de donnees
$dbPath = "$env:APPDATA\bibliotech\bibliotech.db"

Write-Host "Inspection de la base de donnees BiblioTech" -ForegroundColor Cyan
Write-Host "Chemin: $dbPath" -ForegroundColor Yellow
Write-Host ("=" * 60) -ForegroundColor Gray

# Verifier si le fichier existe
if (-not (Test-Path $dbPath)) {
    Write-Host "ERREUR: Le fichier de base de donnees n'existe pas!" -ForegroundColor Red
    Write-Host "Assurez-vous que BiblioTech a ete lance au moins une fois." -ForegroundColor Yellow
    exit 1
}

# Afficher les informations du fichier
$fileInfo = Get-Item $dbPath
Write-Host "Taille du fichier: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green
Write-Host "Derniere modification: $($fileInfo.LastWriteTime)" -ForegroundColor Green
Write-Host ""

Write-Host "STRUCTURE DE LA BASE DE DONNEES" -ForegroundColor Cyan
Write-Host ("=" * 40) -ForegroundColor Gray

$tables = @(
    @{Name="livres"; Description="Catalogue des livres"},
    @{Name="categories"; Description="Categories de livres"},
    @{Name="membres"; Description="Membres de la bibliotheque"},
    @{Name="emprunts"; Description="Gestion des emprunts"},
    @{Name="utilisateurs"; Description="Comptes administrateurs"},
    @{Name="journaux_audit"; Description="Journaux d'audit"}
)

foreach ($table in $tables) {
    Write-Host "Table: $($table.Name)" -ForegroundColor Yellow
    Write-Host "  Description: $($table.Description)" -ForegroundColor White
    Write-Host ""
}

Write-Host "OUTILS RECOMMANDES" -ForegroundColor Cyan
Write-Host ("=" * 25) -ForegroundColor Gray
Write-Host ""

Write-Host "1. DB Browser for SQLite (Gratuit)" -ForegroundColor Yellow
Write-Host "   Telechargement: https://sqlitebrowser.org/dl/" -ForegroundColor White
Write-Host "   Interface graphique intuitive" -ForegroundColor Green
Write-Host "   Visualisation des donnees" -ForegroundColor Green
Write-Host "   Execution de requetes SQL" -ForegroundColor Green
Write-Host ""

Write-Host "2. SQLite Viewer Online" -ForegroundColor Yellow
Write-Host "   Site: https://sqliteviewer.app/" -ForegroundColor White
Write-Host "   ATTENTION: Ne pas telecharger de donnees sensibles!" -ForegroundColor Red
Write-Host ""

Write-Host "REQUETES SQL UTILES" -ForegroundColor Cyan
Write-Host ("=" * 25) -ForegroundColor Gray
Write-Host ""

Write-Host "Statistiques generales:" -ForegroundColor Yellow
Write-Host "SELECT COUNT(*) as total_livres FROM livres;" -ForegroundColor Cyan
Write-Host "SELECT COUNT(*) as livres_disponibles FROM livres WHERE disponible = 1;" -ForegroundColor Cyan
Write-Host "SELECT COUNT(*) as membres_actifs FROM membres WHERE is_active = 1;" -ForegroundColor Cyan
Write-Host ""

Write-Host "Livres par categorie:" -ForegroundColor Yellow
Write-Host "SELECT c.nom, COUNT(l.id) as nb_livres" -ForegroundColor Cyan
Write-Host "FROM categories c LEFT JOIN livres l ON c.nom = l.categorie" -ForegroundColor Cyan
Write-Host "GROUP BY c.nom ORDER BY nb_livres DESC;" -ForegroundColor Cyan
Write-Host ""

Write-Host "PROCHAINES ETAPES:" -ForegroundColor Cyan
Write-Host "1. Telechargez DB Browser for SQLite" -ForegroundColor White
Write-Host "2. Ouvrez le fichier: $dbPath" -ForegroundColor White
Write-Host "3. Explorez vos donnees en toute securite!" -ForegroundColor White
Write-Host ""
Write-Host "IMPORTANT: Fermez BiblioTech avant d'ouvrir la base avec un autre outil!" -ForegroundColor Red

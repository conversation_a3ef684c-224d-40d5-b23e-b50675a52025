// Enhanced library management types

export interface Book {
  id: number;
  titre: string;
  auteur: string;
  categorie: string;
  genre: string;
  isbn?: string;
  anneePublication?: number;
  description?: string;
  disponible: boolean; // Kept for backward compatibility - true if quantiteDisponible > 0
  quantiteTotale: number; // Total number of copies
  quantiteDisponible: number; // Number of available copies
  emprunteurId?: number;
  dateEmprunt?: string;
  dateRetour?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface ImportResult {
  success: number;
  errors: number;
  duplicates: number;
  total: number;
  errorDetails: ImportError[];
}

export interface ImportError {
  row: number;
  field?: string;
  message: string;
  data: any;
}

// Interface for book data used in forms and imports
export interface BookImportData {
  titre: string;
  auteur: string;
  categorie: string;
  genre: string;
  isbn?: string;
  anneePublication?: number;
  description?: string;
  quantite?: number; // New field for quantity - defaults to 1 if not provided
}

export interface ImportPreview {
  headers: string[];
  data: any[][];
  validRows: number;
  invalidRows: number;
  errors: ImportError[];
}

// Validation functions
export const validateBookData = (data: Partial<BookImportData>): ImportError[] => {
  const errors: ImportError[] = [];

  if (!data.titre || data.titre.trim().length === 0) {
    errors.push({ row: 0, field: 'titre', message: 'Le titre est requis', data });
  }

  if (!data.auteur || data.auteur.trim().length === 0) {
    errors.push({ row: 0, field: 'auteur', message: 'L\'auteur est requis', data });
  }

  if (!data.categorie || data.categorie.trim().length === 0) {
    errors.push({ row: 0, field: 'categorie', message: 'La catégorie est requise', data });
  }

  if (!data.genre || data.genre.trim().length === 0) {
    errors.push({ row: 0, field: 'genre', message: 'Le genre est requis', data });
  }

  // Quantity validation - must be positive integer if provided
  if (data.quantite !== undefined) {
    if (!Number.isInteger(data.quantite) || data.quantite < 1) {
      errors.push({ row: 0, field: 'quantite', message: 'La quantité doit être un nombre entier positif', data });
    } else if (data.quantite > 1000) {
      errors.push({ row: 0, field: 'quantite', message: 'La quantité ne peut pas dépasser 1000', data });
    }
  }

  if (data.isbn && !/^(?:\d{10}|\d{13})$/.test(data.isbn.replace(/[-\s]/g, ''))) {
    errors.push({ row: 0, field: 'isbn', message: 'Format ISBN invalide', data });
  }

  if (data.anneePublication && (data.anneePublication < 1000 || data.anneePublication > new Date().getFullYear())) {
    errors.push({ row: 0, field: 'anneePublication', message: 'Année de publication invalide', data });
  }

  return errors;
};

export const sanitizeString = (str: string): string => {
  return str.trim().replace(/[<>]/g, '');
};

export const sanitizeBookData = (data: BookImportData): BookImportData => {
  return {
    titre: sanitizeString(data.titre),
    auteur: sanitizeString(data.auteur),
    categorie: sanitizeString(data.categorie),
    genre: sanitizeString(data.genre),
    isbn: data.isbn ? sanitizeString(data.isbn) : undefined,
    anneePublication: data.anneePublication,
    description: data.description ? sanitizeString(data.description) : undefined,
    quantite: data.quantite || 1, // Default to 1 if not provided
  };
};

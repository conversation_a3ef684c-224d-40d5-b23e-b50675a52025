@echo off
echo ========================================
echo BiblioTech Production Installer Builder
echo ========================================
echo.

echo Checking prerequisites...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js not found. Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: npm not found. Please install npm
    pause
    exit /b 1
)

echo Node.js and npm found!
echo.

echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: npm install failed
    pause
    exit /b 1
)

echo.
echo Rebuilding native dependencies...
call npx electron-rebuild

echo.
echo Building React application...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: React build failed
    pause
    exit /b 1
)

echo.
echo Packaging Electron application...
call npm run pack-win
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Electron packaging failed
    pause
    exit /b 1
)

echo.
echo Creating NSIS installer...
call npx electron-builder --prepackaged out/BiblioTech-win32-x64 --win
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Installer creation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Installer location: dist-electron\BiblioTech-Setup-1.0.2.exe
echo.
echo Next steps:
echo 1. Test the installer on a clean Windows system
echo 2. Verify all features work correctly
echo 3. Distribute to end users
echo.
echo Security Note:
echo The installer is unsigned and will show Windows security warnings.
echo Users should click 'More info' then 'Run anyway' to install.
echo.
pause

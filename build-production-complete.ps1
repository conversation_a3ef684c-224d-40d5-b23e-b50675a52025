# BiblioTech Production Build Script
# Creates both installer and portable versions with comprehensive error handling

param(
    [switch]$CleanBuild = $false,
    [switch]$SkipTests = $false,
    [string]$OutputDir = "production-builds"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-Prerequisites {
    Write-ColorOutput "🔍 Checking prerequisites..." $Blue
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-ColorOutput "✅ Node.js version: $nodeVersion" $Green
    } catch {
        Write-ColorOutput "❌ Node.js not found. Please install Node.js." $Red
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version
        Write-ColorOutput "✅ npm version: $npmVersion" $Green
    } catch {
        Write-ColorOutput "❌ npm not found." $Red
        exit 1
    }
    
    # Check if package.json exists
    if (-not (Test-Path "package.json")) {
        Write-ColorOutput "❌ package.json not found. Run this script from the project root." $Red
        exit 1
    }
    
    Write-ColorOutput "✅ All prerequisites met" $Green
}

function Clean-BuildArtifacts {
    Write-ColorOutput "🧹 Cleaning build artifacts..." $Blue
    
    $dirsToClean = @("dist", "dist-electron", "out", "node_modules\.cache", $OutputDir)
    
    foreach ($dir in $dirsToClean) {
        if (Test-Path $dir) {
            Write-ColorOutput "  Removing $dir..." $Yellow
            Remove-Item $dir -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-ColorOutput "✅ Build artifacts cleaned" $Green
}

function Install-Dependencies {
    Write-ColorOutput "📦 Installing dependencies..." $Blue
    
    try {
        npm install
        Write-ColorOutput "✅ Dependencies installed successfully" $Green
    } catch {
        Write-ColorOutput "❌ Failed to install dependencies: $_" $Red
        exit 1
    }
}

function Build-Application {
    Write-ColorOutput "🔨 Building React application..." $Blue
    
    try {
        npm run build
        Write-ColorOutput "✅ React application built successfully" $Green
    } catch {
        Write-ColorOutput "❌ Failed to build React application: $_" $Red
        exit 1
    }
}

function Test-Application {
    if ($SkipTests) {
        Write-ColorOutput "⏭️ Skipping tests (--SkipTests flag used)" $Yellow
        return
    }
    
    Write-ColorOutput "🧪 Running application tests..." $Blue
    
    # Add test commands here when available
    # For now, just verify the build output exists
    if (-not (Test-Path "dist\index.html")) {
        Write-ColorOutput "❌ Build verification failed: dist/index.html not found" $Red
        exit 1
    }
    
    Write-ColorOutput "✅ Application tests passed" $Green
}

function Build-PortableVersion {
    Write-ColorOutput "📱 Building portable version..." $Blue
    
    try {
        npm run pack-win
        
        if (-not (Test-Path "out\BiblioTech-win32-x64\BiblioTech.exe")) {
            throw "Portable executable not found"
        }
        
        Write-ColorOutput "✅ Portable version built successfully" $Green
        return $true
    } catch {
        Write-ColorOutput "❌ Failed to build portable version: $_" $Red
        return $false
    }
}

function Build-InstallerVersion {
    Write-ColorOutput "💿 Building installer version..." $Blue
    
    try {
        npx electron-builder --prepackaged out/BiblioTech-win32-x64 --win
        
        $installerPath = Get-ChildItem "dist-electron\*.exe" | Select-Object -First 1
        if (-not $installerPath) {
            throw "Installer executable not found"
        }
        
        Write-ColorOutput "✅ Installer version built successfully" $Green
        Write-ColorOutput "  Installer: $($installerPath.Name)" $Green
        return $true
    } catch {
        Write-ColorOutput "❌ Failed to build installer version: $_" $Red
        return $false
    }
}

function Copy-BuildOutputs {
    Write-ColorOutput "📋 Organizing build outputs..." $Blue
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir | Out-Null
    }
    
    # Copy portable version
    if (Test-Path "out\BiblioTech-win32-x64") {
        $portableDir = "$OutputDir\BiblioTech-Portable"
        if (Test-Path $portableDir) {
            Remove-Item $portableDir -Recurse -Force
        }
        Copy-Item "out\BiblioTech-win32-x64" $portableDir -Recurse
        Write-ColorOutput "  ✅ Portable version copied to: $portableDir" $Green
    }
    
    # Copy installer
    $installerFiles = Get-ChildItem "dist-electron\*.exe"
    foreach ($installer in $installerFiles) {
        Copy-Item $installer.FullName "$OutputDir\$($installer.Name)"
        Write-ColorOutput "  ✅ Installer copied to: $OutputDir\$($installer.Name)" $Green
    }
    
    # Create README for the builds
    $readmeContent = @"
# BiblioTech Production Builds

This directory contains the production builds of BiblioTech.

## Files:

### Installer Version
- **BiblioTech-Setup-*.exe**: Complete installer that installs BiblioTech to the system
  - Creates desktop and Start Menu shortcuts
  - Installs to Program Files
  - Creates user data directory in AppData
  - Includes uninstaller

### Portable Version
- **BiblioTech-Portable/**: Self-contained portable application
  - Run BiblioTech.exe directly without installation
  - Can be run from USB drives or network locations
  - Creates data files in the application directory
  - No system installation required

## System Requirements:
- Windows 10/11 (64-bit)
- Minimum 4GB RAM (8GB recommended)
- 500MB free disk space
- Minimum resolution: 1280x720 (1920x1080 recommended)

## Default Login Accounts:
- **Super Admin**: username `superadmin`, password `admin123`
- **Admin**: username `admin1`, password `admin123`

Built on: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
    
    Set-Content -Path "$OutputDir\README.txt" -Value $readmeContent
    Write-ColorOutput "  ✅ README.txt created" $Green
}

function Show-BuildSummary {
    Write-ColorOutput "`n🎉 Build Summary" $Blue
    Write-ColorOutput "=================" $Blue
    
    if (Test-Path "$OutputDir\BiblioTech-Portable\BiblioTech.exe") {
        $portableSize = (Get-ChildItem "$OutputDir\BiblioTech-Portable" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
        Write-ColorOutput "✅ Portable Version: $([math]::Round($portableSize, 1)) MB" $Green
    }
    
    $installerFiles = Get-ChildItem "$OutputDir\*.exe" -ErrorAction SilentlyContinue
    foreach ($installer in $installerFiles) {
        $installerSize = $installer.Length / 1MB
        Write-ColorOutput "✅ Installer: $($installer.Name) - $([math]::Round($installerSize, 1)) MB" $Green
    }
    
    Write-ColorOutput "`nOutput directory: $OutputDir" $Blue
    Write-ColorOutput "Build completed successfully! 🚀" $Green
}

# Main execution
try {
    Write-ColorOutput "🚀 BiblioTech Production Build Script" $Blue
    Write-ColorOutput "=====================================" $Blue
    
    Test-Prerequisites
    
    if ($CleanBuild) {
        Clean-BuildArtifacts
        Install-Dependencies
    }
    
    Build-Application
    Test-Application
    
    $portableSuccess = Build-PortableVersion
    $installerSuccess = $false
    
    if ($portableSuccess) {
        $installerSuccess = Build-InstallerVersion
    }
    
    if ($portableSuccess -or $installerSuccess) {
        Copy-BuildOutputs
        Show-BuildSummary
    } else {
        Write-ColorOutput "❌ Build failed - no outputs generated" $Red
        exit 1
    }
    
} catch {
    Write-ColorOutput "❌ Build script failed: $_" $Red
    exit 1
}

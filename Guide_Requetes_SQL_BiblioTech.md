# 🚀 Guide de Référence Rapide - Requêtes SQL BiblioTech

## 📋 Requêtes Essentielles du Quotidien

### 📊 **1. STATISTIQUES RAPIDES**

```sql
-- Nombre total de livres / Total number of books
SELECT COUNT(*) as total_livres FROM livres;

-- Livres disponibles / Available books
SELECT COUNT(*) as disponibles FROM livres WHERE disponible = 1;

-- Livres empruntés / Borrowed books  
SELECT COUNT(*) as empruntes FROM livres WHERE disponible = 0;

-- Membres actifs / Active members
SELECT COUNT(*) as membres_actifs FROM membres WHERE is_active = 1;

-- Emprunts en cours / Active loans
SELECT COUNT(*) as emprunts_actifs FROM emprunts WHERE statut = 'actif';
```

### 🔍 **2. RECHERCHES COURANTES**

```sql
-- Rechercher un livre par titre / Search book by title
SELECT titre, auteur, categorie, disponible 
FROM livres 
WHERE titre LIKE '%[TITRE]%';

-- Rechercher un membre / Search member
SELECT nom, prenom, numero_membre, telephone 
FROM membres 
WHERE nom LIKE '%[NOM]%' OR prenom LIKE '%[PRENOM]%';

-- Livres d'un auteur / Books by author
SELECT titre, categorie, annee_publication 
FROM livres 
WHERE auteur LIKE '%[AUTEUR]%';

-- Livres d'une catégorie / Books in category
SELECT titre, auteur, disponible 
FROM livres 
WHERE categorie = '[CATEGORIE]' 
ORDER BY titre;
```

### 📋 **3. GESTION DES EMPRUNTS**

```sql
-- Emprunts actifs avec détails / Active loans with details
SELECT 
    l.titre as livre,
    m.prenom || ' ' || m.nom as emprunteur,
    m.telephone,
    e.date_emprunt,
    e.date_retour_prevue
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif'
ORDER BY e.date_retour_prevue;

-- Livres en retard / Overdue books
SELECT 
    l.titre,
    m.prenom || ' ' || m.nom as emprunteur,
    m.telephone,
    e.date_retour_prevue,
    (julianday('now') - julianday(e.date_retour_prevue)) as jours_retard
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif' 
AND date('now') > e.date_retour_prevue
ORDER BY jours_retard DESC;

-- Emprunts à rendre bientôt / Due soon
SELECT 
    l.titre,
    m.prenom || ' ' || m.nom as emprunteur,
    e.date_retour_prevue
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif' 
AND date('now') <= e.date_retour_prevue
AND date('now', '+3 days') >= e.date_retour_prevue;
```

### 📈 **4. ANALYSES ET RAPPORTS**

```sql
-- Top 10 livres les plus empruntés / Top 10 most borrowed books
SELECT 
    l.titre,
    l.auteur,
    COUNT(e.id) as nombre_emprunts
FROM livres l
LEFT JOIN emprunts e ON l.id = e.livre_id
GROUP BY l.id, l.titre, l.auteur
HAVING COUNT(e.id) > 0
ORDER BY nombre_emprunts DESC
LIMIT 10;

-- Membres les plus actifs / Most active members
SELECT 
    m.prenom || ' ' || m.nom as membre,
    COUNT(e.id) as nombre_emprunts
FROM membres m
LEFT JOIN emprunts e ON m.id = e.membre_id
GROUP BY m.id, m.nom, m.prenom
HAVING COUNT(e.id) > 0
ORDER BY nombre_emprunts DESC
LIMIT 10;

-- Répartition par catégorie / Distribution by category
SELECT 
    c.nom as categorie,
    COUNT(l.id) as nombre_livres,
    ROUND(COUNT(l.id) * 100.0 / (SELECT COUNT(*) FROM livres), 1) as pourcentage
FROM categories c
LEFT JOIN livres l ON c.nom = l.categorie
GROUP BY c.nom
ORDER BY nombre_livres DESC;
```

---

## 🛠️ Requêtes de Maintenance

### 🧹 **NETTOYAGE ET OPTIMISATION**

```sql
-- Optimiser la base de données / Optimize database
VACUUM;
ANALYZE;

-- Vérifier l'intégrité / Check integrity
PRAGMA integrity_check;

-- Informations sur la base / Database info
PRAGMA database_list;
PRAGMA table_info(livres);
```

### 🔍 **DÉTECTION DE PROBLÈMES**

```sql
-- Livres jamais empruntés / Never borrowed books
SELECT 
    l.titre,
    l.auteur,
    l.created_at as date_ajout
FROM livres l
LEFT JOIN emprunts e ON l.id = e.livre_id
WHERE e.id IS NULL
ORDER BY l.created_at DESC;

-- Membres sans emprunts / Members without loans
SELECT 
    m.nom,
    m.prenom,
    m.date_inscription
FROM membres m
LEFT JOIN emprunts e ON m.id = e.membre_id
WHERE e.id IS NULL AND m.is_active = 1
ORDER BY m.date_inscription DESC;

-- Doublons potentiels de livres / Potential book duplicates
SELECT 
    titre,
    auteur,
    COUNT(*) as occurrences
FROM livres
GROUP BY LOWER(titre), LOWER(auteur)
HAVING COUNT(*) > 1;
```

---

## 📊 Tableaux de Bord Prêts à l'Emploi

### 🎯 **TABLEAU DE BORD PRINCIPAL**

```sql
-- Vue d'ensemble complète / Complete overview
SELECT 
    'LIVRES' as categorie,
    (SELECT COUNT(*) FROM livres) as total,
    (SELECT COUNT(*) FROM livres WHERE disponible = 1) as disponibles,
    (SELECT COUNT(*) FROM livres WHERE disponible = 0) as empruntes,
    NULL as en_retard

UNION ALL

SELECT 
    'MEMBRES' as categorie,
    (SELECT COUNT(*) FROM membres WHERE is_active = 1) as total,
    (SELECT COUNT(DISTINCT membre_id) FROM emprunts WHERE statut = 'actif') as actifs,
    (SELECT COUNT(*) FROM membres WHERE is_active = 1) - 
    (SELECT COUNT(DISTINCT membre_id) FROM emprunts WHERE statut = 'actif') as inactifs,
    NULL as en_retard

UNION ALL

SELECT 
    'EMPRUNTS' as categorie,
    (SELECT COUNT(*) FROM emprunts WHERE statut = 'actif') as total,
    (SELECT COUNT(*) FROM emprunts WHERE statut = 'actif' AND date('now') <= date_retour_prevue) as dans_les_temps,
    (SELECT COUNT(*) FROM emprunts WHERE statut = 'actif' AND date('now') > date_retour_prevue) as en_retard,
    (SELECT COUNT(*) FROM emprunts WHERE statut = 'actif' AND date('now') > date_retour_prevue) as en_retard;
```

### 📅 **RAPPORT MENSUEL**

```sql
-- Activité du mois en cours / Current month activity
SELECT 
    'Nouveaux livres' as type,
    COUNT(*) as nombre
FROM livres 
WHERE strftime('%Y-%m', created_at) = strftime('%Y-%m', 'now')

UNION ALL

SELECT 
    'Nouveaux membres' as type,
    COUNT(*) as nombre
FROM membres 
WHERE strftime('%Y-%m', date_inscription) = strftime('%Y-%m', 'now')

UNION ALL

SELECT 
    'Emprunts du mois' as type,
    COUNT(*) as nombre
FROM emprunts 
WHERE strftime('%Y-%m', date_emprunt) = strftime('%Y-%m', 'now');
```

---

## 🎨 Personnalisation des Requêtes

### 🔧 **MODÈLES À ADAPTER**

```sql
-- Template: Recherche avancée de livres
-- Template: Advanced book search
SELECT 
    titre,
    auteur,
    categorie,
    genre,
    annee_publication,
    CASE WHEN disponible = 1 THEN 'Disponible' ELSE 'Emprunté' END as statut
FROM livres 
WHERE 1=1
    -- Décommentez les lignes selon vos besoins / Uncomment lines as needed
    -- AND categorie = '[CATEGORIE]'
    -- AND auteur LIKE '%[AUTEUR]%'
    -- AND annee_publication >= [ANNEE]
    -- AND disponible = 1
ORDER BY titre;

-- Template: Rapport personnalisé d'emprunts
-- Template: Custom loan report
SELECT 
    e.date_emprunt,
    l.titre,
    l.auteur,
    m.prenom || ' ' || m.nom as emprunteur,
    e.date_retour_prevue,
    e.statut
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE 1=1
    -- Filtres optionnels / Optional filters
    -- AND e.date_emprunt >= '[DATE_DEBUT]'
    -- AND e.date_emprunt <= '[DATE_FIN]'
    -- AND e.statut = '[STATUT]'
    -- AND m.nom LIKE '%[NOM_MEMBRE]%'
ORDER BY e.date_emprunt DESC;
```

---

## 💡 Conseils et Astuces

### ✅ **BONNES PRATIQUES**

1. **Toujours tester** sur une copie avant les requêtes de modification
2. **Utiliser LIMIT** pour les grandes tables : `LIMIT 100`
3. **Sauvegarder** avant les opérations importantes
4. **Commenter** vos requêtes complexes
5. **Utiliser des alias** pour la lisibilité : `AS nom_colonne`

### 🚀 **OPTIMISATION**

```sql
-- Utiliser des index pour accélérer les recherches
-- Use indexes to speed up searches
CREATE INDEX IF NOT EXISTS idx_livres_titre ON livres(titre);
CREATE INDEX IF NOT EXISTS idx_livres_auteur ON livres(auteur);
CREATE INDEX IF NOT EXISTS idx_emprunts_statut ON emprunts(statut);
```

### 🔍 **DÉBOGAGE**

```sql
-- Compter les enregistrements avant modification
-- Count records before modification
SELECT COUNT(*) FROM table_name WHERE condition;

-- Voir la structure d'une table
-- View table structure
PRAGMA table_info(nom_table);

-- Lister toutes les tables
-- List all tables
SELECT name FROM sqlite_master WHERE type='table';
```

---

**📚 Utilisation :** Copiez-collez ces requêtes dans DB Browser for SQLite  
**🔧 Personnalisation :** Remplacez les valeurs entre [CROCHETS] par vos données  
**⚠️ Important :** Fermez BiblioTech avant d'exécuter ces requêtes

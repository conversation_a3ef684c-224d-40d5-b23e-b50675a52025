// Composant pour afficher le suivi des prêts de livres
import React, { useMemo, useState, useEffect } from 'react';
import type { Livre } from './LivreList';
import type { Utilisateur } from './UtilisateurList';
import { ClipboardIcon, CheckIcon, DownloadIcon } from './Icons';
import ExtendLoanModal from './ExtendLoanModal';
import ConfirmReturnModal from './ConfirmReturnModal';
import { exportLoansToCSV } from '../services/csv-utils';

interface Props {
  livres: Livre[];
  utilisateurs: Utilisateur[];
  onReturnBook?: (bookId: number) => void;
  onExtendLoan?: (bookId: number, newDueDate: string) => void;
  initialFilter?: 'all' | 'overdue' | 'due-soon';
}

const SuiviPrets: React.FC<Props> = ({ livres, utilisateurs, onReturnBook, onExtendLoan, initialFilter = 'all' }) => {
  const emprunts = livres.filter(l => !l.disponible && l.emprunteurId);

  // Filter state for loan tracking
  const [loanFilter, setLoanFilter] = useState<'all' | 'overdue' | 'due-soon'>(initialFilter);

  // Browser detection state
  const [isElectron, setIsElectron] = useState(false);

  // Detect if running in Electron vs browser
  useEffect(() => {
    const checkElectron = () => {
      // Check for Electron-specific APIs
      return !!(window as any).electronAPI ||
             !!(window as any).require ||
             navigator.userAgent.toLowerCase().includes('electron');
    };
    setIsElectron(checkElectron());
  }, []);

  // Update filter when initialFilter prop changes
  useEffect(() => {
    setLoanFilter(initialFilter);
  }, [initialFilter]);

  // Keyboard shortcuts for filter switching
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            setLoanFilter('all');
            break;
          case '2':
            e.preventDefault();
            setLoanFilter('overdue');
            break;
          case '3':
            e.preventDefault();
            setLoanFilter('due-soon');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Modal states
  const [extendLoanModal, setExtendLoanModal] = useState<{
    isOpen: boolean;
    bookId: number;
    bookTitle: string;
    memberName: string;
    currentDueDate: string;
  }>({
    isOpen: false,
    bookId: 0,
    bookTitle: '',
    memberName: '',
    currentDueDate: ''
  });

  const [confirmReturnModal, setConfirmReturnModal] = useState<{
    isOpen: boolean;
    bookId: number;
    bookTitle: string;
    bookAuthor: string;
    memberName: string;
    loanStartDate: string;
    originalDueDate: string;
  }>({
    isOpen: false,
    bookId: 0,
    bookTitle: '',
    bookAuthor: '',
    memberName: '',
    loanStartDate: '',
    originalDueDate: ''
  });

  const empruntsWithDetails = useMemo(() => {
    let processedEmprunts = emprunts.map(livre => {
      const user = utilisateurs.find(u => u.id === livre.emprunteurId);
      const dateRetour = livre.dateRetour ? new Date(livre.dateRetour) : null;
      const today = new Date();
      const isOverdue = dateRetour && dateRetour < today;
      const daysUntilDue = dateRetour ? Math.ceil((dateRetour.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : null;

      return {
        ...livre,
        user,
        isOverdue,
        daysUntilDue,
        dateRetourFormatted: dateRetour ? dateRetour.toLocaleDateString('fr-FR') : '—',
        dateEmpruntFormatted: livre.dateEmprunt ? new Date(livre.dateEmprunt).toLocaleDateString('fr-FR') : '—'
      };
    });

    // Apply filter based on current selection
    if (loanFilter === 'overdue') {
      processedEmprunts = processedEmprunts.filter(emprunt => emprunt.isOverdue);
    } else if (loanFilter === 'due-soon') {
      processedEmprunts = processedEmprunts.filter(emprunt =>
        emprunt.daysUntilDue !== null && emprunt.daysUntilDue <= 3 && emprunt.daysUntilDue >= 0
      );
    }

    return processedEmprunts;
  }, [emprunts, utilisateurs, loanFilter]);

  // Calculate stats from all emprunts (not filtered)
  const allEmpruntsWithDetails = useMemo(() => {
    return emprunts.map(livre => {
      const user = utilisateurs.find(u => u.id === livre.emprunteurId);
      const dateRetour = livre.dateRetour ? new Date(livre.dateRetour) : null;
      const today = new Date();
      const isOverdue = dateRetour && dateRetour < today;
      const daysUntilDue = dateRetour ? Math.ceil((dateRetour.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)) : null;

      return {
        ...livre,
        user,
        isOverdue,
        daysUntilDue,
        dateRetourFormatted: dateRetour ? dateRetour.toLocaleDateString('fr-FR') : '—',
        dateEmpruntFormatted: livre.dateEmprunt ? new Date(livre.dateEmprunt).toLocaleDateString('fr-FR') : '—'
      };
    });
  }, [emprunts, utilisateurs]);

  const stats = {
    totalEmprunts: emprunts.length,
    empruntsEnRetard: allEmpruntsWithDetails.filter(e => e.isOverdue).length,
    empruntsAEcheance: allEmpruntsWithDetails.filter(e => e.daysUntilDue !== null && e.daysUntilDue <= 3 && e.daysUntilDue >= 0).length
  };

  // Click handlers for statistics cards
  const handleStatCardClick = (filter: 'all' | 'overdue' | 'due-soon') => {
    setLoanFilter(filter);
  };

  // Modal handlers
  const handleExtendLoanClick = (emprunt: any) => {
    const memberName = emprunt.user ? emprunt.user.nom : 'Utilisateur inconnu';
    const currentDueDate = emprunt.dateRetour || new Date().toISOString().split('T')[0];

    setExtendLoanModal({
      isOpen: true,
      bookId: emprunt.id,
      bookTitle: emprunt.titre,
      memberName: memberName,
      currentDueDate: currentDueDate
    });
  };

  const handleExtendLoanConfirm = (newDueDate: string) => {
    if (onExtendLoan) {
      onExtendLoan(extendLoanModal.bookId, newDueDate);
    }
    setExtendLoanModal({
      isOpen: false,
      bookId: 0,
      bookTitle: '',
      memberName: '',
      currentDueDate: ''
    });
  };

  const handleReturnBookClick = (emprunt: any) => {
    const memberName = emprunt.user ? emprunt.user.nom : 'Utilisateur inconnu';
    const loanStartDate = emprunt.dateEmprunt || new Date().toISOString().split('T')[0];
    const originalDueDate = emprunt.dateRetour || new Date().toISOString().split('T')[0];

    setConfirmReturnModal({
      isOpen: true,
      bookId: emprunt.id,
      bookTitle: emprunt.titre,
      bookAuthor: emprunt.auteur,
      memberName: memberName,
      loanStartDate: loanStartDate,
      originalDueDate: originalDueDate
    });
  };

  const handleReturnBookConfirm = () => {
    if (onReturnBook) {
      onReturnBook(confirmReturnModal.bookId);
    }
    setConfirmReturnModal({
      isOpen: false,
      bookId: 0,
      bookTitle: '',
      bookAuthor: '',
      memberName: '',
      loanStartDate: '',
      originalDueDate: ''
    });
  };

  return (
    <div>
      {/* Statistics Cards */}
      <div className="stats-grid mb-8">
        <div
          className={`stat-card stat-card--clickable ${loanFilter === 'all' ? 'stat-card--active' : ''}`}
          onClick={() => handleStatCardClick('all')}
        >
          <div className="stat-value">{stats.totalEmprunts}</div>
          <div className="stat-label">Prêts actifs</div>
        </div>
        <div
          className={`stat-card stat-card--clickable ${loanFilter === 'overdue' ? 'stat-card--active' : ''}`}
          onClick={() => handleStatCardClick('overdue')}
        >
          <div className="stat-value text-error">{stats.empruntsEnRetard}</div>
          <div className="stat-label">En retard</div>
        </div>
        <div
          className={`stat-card stat-card--clickable ${loanFilter === 'due-soon' ? 'stat-card--active' : ''}`}
          onClick={() => handleStatCardClick('due-soon')}
        >
          <div className="stat-value text-warning">{stats.empruntsAEcheance}</div>
          <div className="stat-label">À échéance (3j)</div>
        </div>
      </div>

      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-3">
                <h2 className="card-title flex items-center gap-2">
                  <ClipboardIcon size={24} />
                  Suivi des prêts
                </h2>
                {loanFilter !== 'all' && (
                  <div className="flex items-center gap-2">
                    <span className="filter-badge">
                      {loanFilter === 'overdue' && 'En retard'}
                      {loanFilter === 'due-soon' && 'À échéance (3j)'}
                    </span>
                    <button
                      className="clear-filter-btn"
                      onClick={() => setLoanFilter('all')}
                      title="Afficher tous les prêts"
                    >
                      ×
                    </button>
                  </div>
                )}
              </div>
              <p className="card-subtitle">
                {loanFilter === 'all' && 'Gérez les prêts de livres en cours'}
                {loanFilter === 'overdue' && 'Prêts en retard nécessitant une action'}
                {loanFilter === 'due-soon' && 'Prêts à échéance dans les 3 prochains jours'}
                <span className="text-xs text-neutral-400 ml-2">
                  • Raccourcis: <kbd>Ctrl+1</kbd> Tous, <kbd>Ctrl+2</kbd> Retard, <kbd>Ctrl+3</kbd> Échéance
                </span>
              </p>
            </div>
            <button
              className="btn btn-secondary"
              onClick={() => exportLoansToCSV(empruntsWithDetails, 'prets.csv')}
              title="Exporter la liste des prêts en CSV"
            >
              <DownloadIcon size={16} />
              Exporter CSV
            </button>
          </div>
        </div>

        <div className="card-content">
          {/* Results count indicator */}
          {loanFilter !== 'all' && (
            <div className="mb-4 text-sm text-neutral">
              Affichage de {empruntsWithDetails.length} prêt{empruntsWithDetails.length !== 1 ? 's' : ''} sur {stats.totalEmprunts} au total
            </div>
          )}

          <div className="table-container">
            <div className="table-responsive">
              <table className={`table table-mobile-stack loan-tracking-table ${!isElectron ? 'browser-optimized' : ''}`}>
                <thead>
                  <tr>
                    <th>Livre</th>
                    <th>Emprunteur</th>
                    <th className="hide-mobile">Date d'emprunt</th>
                    <th>Date de retour prévue</th>
                    <th>Statut</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {empruntsWithDetails.length === 0 && loanFilter !== 'all' && (
                    <tr>
                      <td colSpan={6} className="text-center py-8 text-neutral">
                        <div className="flex flex-col items-center gap-2">
                          <div className="text-lg">📋</div>
                          <div>
                            {loanFilter === 'overdue' && 'Aucun prêt en retard'}
                            {loanFilter === 'due-soon' && 'Aucun prêt à échéance dans les 3 prochains jours'}
                          </div>
                          <button
                            className="text-primary hover:underline text-sm"
                            onClick={() => setLoanFilter('all')}
                          >
                            Voir tous les prêts
                          </button>
                        </div>
                      </td>
                    </tr>
                  )}
                  {empruntsWithDetails.length === 0 && loanFilter === 'all' && stats.totalEmprunts === 0 && (
                    <tr>
                      <td colSpan={6} className="text-center py-8 text-neutral">
                        <div className="flex flex-col items-center gap-2">
                          <div className="text-lg">📚</div>
                          <div>Aucun prêt en cours</div>
                          <div className="text-sm">Tous les livres sont disponibles</div>
                        </div>
                      </td>
                    </tr>
                  )}
                  {empruntsWithDetails.map(emprunt => (
                    <tr key={emprunt.id}>
                      <td data-label="Livre">
                        <div>
                          <div className="font-medium">{emprunt.titre}</div>
                          <div className="text-sm text-neutral">{emprunt.auteur}</div>
                        </div>
                      </td>
                      <td data-label="Emprunteur">
                        <div className="flex items-center gap-3">
                          {emprunt.user && (
                            <>
                              <img
                                src={emprunt.user.photo || '/default-avatar.png'}
                                alt={emprunt.user.nom}
                                className="w-8 h-8 rounded-full object-cover"
                                onError={(e) => {
                                  // Fallback to generated avatar if image fails to load
                                  (e.target as HTMLImageElement).src = `https://ui-avatars.com/api/?name=${encodeURIComponent(emprunt.user?.nom || '')}&background=0ea5e9&color=fff&size=32`;
                                }}
                              />
                              <span>{emprunt.user.nom}</span>
                            </>
                          )}
                          {!emprunt.user && <span className="text-neutral">Utilisateur inconnu</span>}
                        </div>
                      </td>
                      <td data-label="Date d'emprunt" className="hide-mobile">{emprunt.dateEmpruntFormatted}</td>
                      <td data-label="Date de retour prévue">{emprunt.dateRetourFormatted}</td>
                      <td data-label="Statut">
                        {emprunt.isOverdue && (
                          <span className="status-badge overdue">
                            En retard
                          </span>
                        )}
                        {!emprunt.isOverdue && emprunt.daysUntilDue !== null && emprunt.daysUntilDue <= 3 && (
                          <span className="status-badge warning">
                            Échéance proche ({emprunt.daysUntilDue}j)
                          </span>
                        )}
                        {!emprunt.isOverdue && (emprunt.daysUntilDue === null || emprunt.daysUntilDue > 3) && (
                          <span className="status-badge active">
                            En cours
                          </span>
                        )}
                      </td>
                      <td data-label="Actions">
                        <div className="action-buttons">
                          {onReturnBook && (
                            <button
                              className="action-btn action-btn--success action-btn--md"
                              onClick={() => handleReturnBookClick(emprunt)}
                            >
                              <span className="action-icon">
                                <CheckIcon size={14} />
                              </span>
                              <span className="action-label">Retourner</span>
                            </button>
                          )}
                          {onExtendLoan && (
                            <button
                              className="action-btn action-btn--warning action-btn--md"
                              onClick={() => handleExtendLoanClick(emprunt)}
                            >
                              <span className="action-icon">📅</span>
                              <span className="action-label">Prolonger</span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {emprunts.length === 0 && (
              <div className="text-center py-8 text-neutral">
                <ClipboardIcon size={48} className="mx-auto mb-4 opacity-50" />
                <p>Aucun prêt en cours.</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Extend Loan Modal */}
      <ExtendLoanModal
        isOpen={extendLoanModal.isOpen}
        onClose={() => setExtendLoanModal({ ...extendLoanModal, isOpen: false })}
        onConfirm={handleExtendLoanConfirm}
        bookTitle={extendLoanModal.bookTitle}
        memberName={extendLoanModal.memberName}
        currentDueDate={extendLoanModal.currentDueDate}
      />

      {/* Confirm Return Modal */}
      <ConfirmReturnModal
        isOpen={confirmReturnModal.isOpen}
        onClose={() => setConfirmReturnModal({ ...confirmReturnModal, isOpen: false })}
        onConfirm={handleReturnBookConfirm}
        bookTitle={confirmReturnModal.bookTitle}
        bookAuthor={confirmReturnModal.bookAuthor}
        memberName={confirmReturnModal.memberName}
        loanStartDate={confirmReturnModal.loanStartDate}
        originalDueDate={confirmReturnModal.originalDueDate}
      />
    </div>
  );
};

export default SuiviPrets;

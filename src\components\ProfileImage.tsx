import React, { useState, useEffect } from 'react';

interface ProfileImageProps {
  src: string | null | undefined;
  alt: string;
  className?: string;
  fallbackName?: string;
  size?: number;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
}

/**
 * ProfileImage component that handles image loading in Electron environment
 * Automatically resolves image paths and provides fallback to generated avatars
 */
const ProfileImage: React.FC<ProfileImageProps> = ({
  src,
  alt,
  className = '',
  fallbackName,
  size = 40,
  onError
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [, setHasError] = useState(false);

  // Generate fallback avatar URL
  const generateFallbackAvatar = (name?: string) => {
    const displayName = name || alt || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=0ea5e9&color=fff&size=${size}`;
  };

  useEffect(() => {
    const loadImage = async () => {
      setIsLoading(true);
      setHasError(false);

      if (!src) {
        // No source provided, use fallback immediately
        setImageSrc(generateFallbackAvatar(fallbackName));
        setIsLoading(false);
        return;
      }

      try {
        // Check if we're in Electron environment
        if (window.electronAPI && window.electronAPI.getUserImagePath) {
          // Use Electron IPC to resolve image path
          const resolvedPath = await window.electronAPI.getUserImagePath(src);
          if (resolvedPath) {
            setImageSrc(resolvedPath);
          } else {
            // Image not found, use fallback
            setImageSrc(generateFallbackAvatar(fallbackName));
          }
        } else {
          // Development mode or web environment, use src directly
          setImageSrc(src);
        }
      } catch (error) {
        console.error('Error loading profile image:', error);
        setImageSrc(generateFallbackAvatar(fallbackName));
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [src, fallbackName, alt, size]);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.log('Image failed to load, using fallback:', src);
    setHasError(true);
    setImageSrc(generateFallbackAvatar(fallbackName));
    
    if (onError) {
      onError(e);
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    // Show loading placeholder
    return (
      <div 
        className={`${className} profile-image-loading`}
        style={{ 
          width: size, 
          height: size, 
          backgroundColor: '#f3f4f6',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <div style={{ fontSize: '12px', color: '#9ca3af' }}>...</div>
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onError={handleImageError}
      onLoad={handleImageLoad}
      style={{ 
        width: size, 
        height: size,
        objectFit: 'cover'
      }}
    />
  );
};

export default ProfileImage;

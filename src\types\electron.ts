// Type definitions for Electron IPC API
// This file contains all the type definitions for the electronAPI exposed by the preload script

import type { Book, Category } from './library';
import type { LibraryMember, MemberProfile } from './member';
import type { User, AuditLog } from './auth';

export interface ElectronAPI {
  database: {
    // Books
    getAllBooks: () => Promise<Book[]>;
    getBookById: (id: number) => Promise<Book | null>;
    addBook: (book: Omit<Book, 'id' | 'createdAt' | 'updatedAt' | 'disponible'>) => Promise<Book>;
    updateBook: (id: number, updates: Partial<Book>) => Promise<boolean>;
    deleteBook: (id: number) => Promise<boolean>;

    // Categories
    getAllCategories: () => Promise<Category[]>;
    getCategoryById: (id: number) => Promise<Category | null>;
    addCategory: (name: string, description?: string) => Promise<Category>;
    updateCategory: (id: number, name: string, description?: string) => Promise<boolean>;
    deleteCategory: (id: number) => Promise<boolean>;
    reorderCategories: (categoryIds: number[]) => Promise<boolean>;
    getBooksCountInCategory: (categoryName: string) => Promise<number>;

    // Members
    getAllMembers: () => Promise<LibraryMember[]>;
    getMemberById: (id: number) => Promise<LibraryMember | null>;
    addMember: (member: Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'>) => Promise<LibraryMember>;
    updateMember: (id: number, updates: Partial<LibraryMember>) => Promise<boolean>;
    deleteMember: (id: number) => Promise<boolean>;
    getAllMemberProfiles: () => Promise<MemberProfile[]>;
    getMemberProfile: (id: number) => Promise<MemberProfile | null>;

    // Loans
    createLoan: (bookId: number, memberId: number, startDate: string, dueDate: string) => Promise<boolean>;
    returnBook: (bookId: number) => Promise<boolean>;
    extendLoan: (bookId: number, newDueDate: string) => Promise<boolean>;
    getMemberBorrowedBooks: (memberId: number) => Promise<any[]>;
    updateOverdueLoans: () => Promise<boolean>;

    // Users
    getAllUsers: () => Promise<User[]>;
    getUserByUsername: (username: string) => Promise<(User & { password_hash: string }) | null>;
    addUser: (username: string, email: string, passwordHash: string, role: 'super_admin' | 'administrator') => Promise<User>;
    updateUser: (userId: number, updates: Partial<User>) => Promise<boolean>;
    deleteUser: (userId: number) => Promise<boolean>;
    updateUserPassword: (userId: number, newPassword: string) => Promise<boolean>;
    updateLastLogin: (userId: number) => Promise<boolean>;

    // Authentication
    authenticateUser: (credentials: { username: string; password: string }) => Promise<User | null>;
    createUser: (userData: { username: string; email: string; password: string; role: 'super_admin' | 'administrator' }) => Promise<User>;

    // Audit logs
    addAuditLog: (userId: number, username: string, action: string, resource: string, resourceId?: number, details?: string) => Promise<boolean>;
    getAuditLogs: (limit?: number, offset?: number) => Promise<AuditLog[]>;
    getUserAuditLogs: (userId: number, limit?: number) => Promise<AuditLog[]>;

    // Statistics and utilities
    getStats: () => Promise<{ totalBooks: number; totalMembers: number; borrowedBooks: number; overdueBooks: number; dueSoonBooks: number; }>;
    getDatabasePath: () => Promise<string>;
    backup: (backupPath: string) => Promise<boolean>;
    
    // Migration
    migrateInitialData: () => Promise<{ success: boolean; message: string; }>;
  };

  // App utilities
  app: {
    getVersion: () => Promise<string>;
    quit: () => Promise<void>;
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    unmaximize: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
  };

  // File operations
  file: {
    showOpenDialog: (options: any) => Promise<any>;
    showSaveDialog: (options: any) => Promise<any>;
    readFile: (filePath: string) => Promise<string>;
    writeFile: (filePath: string, data: string) => Promise<boolean>;
  };

  // Image operations
  getUserImagePath: (imagePath: string) => Promise<string | null>;
  saveUserImage: (imageData: string, filename?: string) => Promise<string | null>;

  // Database reset operations
  resetToInitialData: () => Promise<{ success: boolean; message: string }>;
}

// Global type declaration
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};

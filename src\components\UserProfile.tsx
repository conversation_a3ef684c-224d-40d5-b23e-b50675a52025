// Detailed user profile component for BiblioTech
import React from 'react';
import { UsersIcon, BookIcon, CalendarIcon, PhoneIcon, MapPinIcon, MailIcon, IdCardIcon } from './Icons';
import ModalPortal from './ModalPortal';
import ProfileImage from './ProfileImage';
import type { MemberProfile, BorrowedBook } from '../types/member';

interface UserProfileProps {
  member: MemberProfile;
  onClose: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({
  member,
  onClose
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusBadge = (book: BorrowedBook) => {
    if (book.isOverdue) {
      return <span className="badge badge-error">En retard ({Math.abs(book.daysUntilDue)} jours)</span>;
    } else if (book.daysUntilDue <= 3) {
      return <span className="badge badge-warning">À échéance ({book.daysUntilDue} jours)</span>;
    } else {
      return <span className="badge badge-success">En cours</span>;
    }
  };

  return (
    <ModalPortal isOpen={true} onClose={onClose}>
      <div className="modal-content user-profile-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title flex items-center gap-2">
            <UsersIcon size={24} />
            Profil de {member.prenom} {member.nom}
          </h2>
          <button className="modal-close" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <div className="user-profile-grid">
            {/* Profile Information */}
            <div className="profile-section">
              <div className="profile-header">
                <ProfileImage
                  src={member.photo}
                  alt={`${member.prenom} ${member.nom}`}
                  className="profile-avatar"
                  fallbackName={`${member.prenom} ${member.nom}`}
                  size={120}
                />
                <div className="profile-info">
                  <h3 className="profile-name">{member.prenom} {member.nom}</h3>
                  <p className="profile-function">{member.fonction}</p>
                  <div className="profile-status">
                    <span className={`badge ${member.isActive ? 'badge-success' : 'badge-error'}`}>
                      {member.isActive ? 'Actif' : 'Inactif'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="profile-details">
                <div className="detail-item">
                  <IdCardIcon size={16} />
                  <span className="detail-label">Numéro de membre :</span>
                  <span className="detail-value">{member.numeroMembre}</span>
                </div>
                <div className="detail-item">
                  <PhoneIcon size={16} />
                  <span className="detail-label">Téléphone :</span>
                  <span className="detail-value">{member.telephone}</span>
                </div>
                <div className="detail-item">
                  <MapPinIcon size={16} />
                  <span className="detail-label">Adresse :</span>
                  <span className="detail-value">{member.adresse}</span>
                </div>
                {member.email && (
                  <div className="detail-item">
                    <MailIcon size={16} />
                    <span className="detail-label">Email :</span>
                    <span className="detail-value">{member.email}</span>
                  </div>
                )}
                <div className="detail-item">
                  <CalendarIcon size={16} />
                  <span className="detail-label">Date d'inscription :</span>
                  <span className="detail-value">{formatDate(member.dateInscription)}</span>
                </div>
              </div>
            </div>

            {/* Borrowed Books Section */}
            <div className="borrowed-books-section">
              <div className="section-header">
                <h4 className="section-title flex items-center gap-2">
                  <BookIcon size={20} />
                  Livres empruntés ({member.nombreLivresEmpruntes})
                </h4>
                {member.nombreLivresEnRetard > 0 && (
                  <span className="badge badge-error">
                    {member.nombreLivresEnRetard} en retard
                  </span>
                )}
              </div>

              {member.livresEmpruntes.length === 0 ? (
                <div className="empty-state">
                  <BookIcon size={48} className="empty-icon" />
                  <p>Aucun livre emprunté actuellement</p>
                </div>
              ) : (
                <div className="borrowed-books-list">
                  {member.livresEmpruntes.map((book) => (
                    <div key={book.id} className="borrowed-book-card">
                      <div className="book-info">
                        <h5 className="book-title">{book.titre}</h5>
                        <p className="book-author">par {book.auteur}</p>
                        <div className="book-dates">
                          <span className="date-item">
                            Emprunté le : {formatDate(book.dateEmprunt)}
                          </span>
                          <span className="date-item">
                            À rendre le : {formatDate(book.dateRetour)}
                          </span>
                        </div>
                      </div>
                      <div className="book-status">
                        {getStatusBadge(book)}
                      </div>
                      {/* Read-only view - no actions available */}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Fermer
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default UserProfile;

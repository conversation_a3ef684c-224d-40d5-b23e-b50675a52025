// Composant pour afficher la liste des livres et leur statut d'emprunt
import React, { useState, useMemo, useEffect } from 'react';
import { SearchIcon, EditIcon, TrashIcon, CheckIcon, XIcon, FilterIcon, DownloadIcon, UsersIcon } from './Icons';
import ConfirmationModal from './ConfirmationModal';
import EditBookModal from './EditBookModal';
import type { Book } from '../types/library';
import { exportBooksToCSV } from '../services/csv-utils';

export type Livre = {
  id: number;
  titre: string;
  auteur: string;
  categorie: string;
  genre: string;
  disponible: boolean;
  quantiteTotale: number;
  quantiteDisponible: number;
  emprunteurId?: number;
  dateEmprunt?: string;
  dateRetour?: string;
}



interface LivreListProps {
  livres: Livre[];
  onSelectLivre?: (livre: Livre) => void;
  onDelete?: (id: number) => void;
  onUpdate?: (id: number, updates: Partial<Book>) => void;
  onToggleAvailability?: (id: number) => void;
  onAssignBook?: (book: Livre) => void;
  searchTerm?: string;
  onSearchChange?: (term: string) => void;
  authorSearchTerm?: string;
  onAuthorSearchChange?: (term: string) => void;
  initialFilter?: 'all' | 'available' | 'borrowed' | 'overdue' | 'due-soon';
  onFilterChange?: (filter: 'all' | 'available' | 'borrowed' | 'overdue' | 'due-soon') => void;
  categories?: string[];
}

type SortField = 'titre' | 'auteur' | 'categorie' | 'genre' | 'disponible';
type SortDirection = 'asc' | 'desc';

const LivreList: React.FC<LivreListProps> = ({
  livres,
  onSelectLivre,
  onDelete,
  onUpdate,
  onToggleAvailability,
  onAssignBook,
  searchTerm = '',
  onSearchChange,
  authorSearchTerm = '',
  onAuthorSearchChange,
  initialFilter = 'all',
  onFilterChange,
  categories = []
}) => {
  const [sortField, setSortField] = useState<SortField>('titre');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'available' | 'borrowed' | 'overdue' | 'due-soon'>(initialFilter);
  const [isElectron, setIsElectron] = useState(false);

  // Confirmation modal state
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    type: 'delete' | 'toggle-availability';
    bookId: number;
    bookTitle: string;
    isAvailable: boolean;
  }>({
    isOpen: false,
    type: 'delete',
    bookId: 0,
    bookTitle: '',
    isAvailable: false
  });

  // Edit modal state
  const [editingBook, setEditingBook] = useState<Livre | null>(null);

  // Detect if running in Electron vs browser
  useEffect(() => {
    const checkElectron = () => {
      // Check for Electron-specific APIs
      return !!(window as any).electronAPI ||
             !!(window as any).require ||
             navigator.userAgent.toLowerCase().includes('electron');
    };
    setIsElectron(checkElectron());
  }, []);

  // Sync filter state with parent component
  useEffect(() => {
    setFilterStatus(initialFilter);
  }, [initialFilter]);

  // Notify parent of filter changes
  useEffect(() => {
    if (onFilterChange) {
      onFilterChange(filterStatus);
    }
  }, [filterStatus, onFilterChange]);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Confirmation modal handlers
  const handleDeleteClick = (livre: Livre) => {
    setConfirmationModal({
      isOpen: true,
      type: 'delete',
      bookId: livre.id,
      bookTitle: livre.titre,
      isAvailable: livre.disponible
    });
  };

  const handleToggleAvailabilityClick = (livre: Livre) => {
    setConfirmationModal({
      isOpen: true,
      type: 'toggle-availability',
      bookId: livre.id,
      bookTitle: livre.titre,
      isAvailable: livre.disponible
    });
  };

  const handleConfirmAction = () => {
    if (confirmationModal.type === 'delete' && onDelete) {
      onDelete(confirmationModal.bookId);
    } else if (confirmationModal.type === 'toggle-availability' && onToggleAvailability) {
      onToggleAvailability(confirmationModal.bookId);
    }

    setConfirmationModal({
      isOpen: false,
      type: 'delete',
      bookId: 0,
      bookTitle: '',
      isAvailable: false
    });
  };

  const handleCloseModal = () => {
    setConfirmationModal({
      isOpen: false,
      type: 'delete',
      bookId: 0,
      bookTitle: '',
      isAvailable: false
    });
  };

  // Edit modal handlers
  const handleEditClick = (livre: Livre) => {
    setEditingBook(livre);
  };

  const handleEditSave = (bookId: number, updates: Partial<Book>) => {
    if (onUpdate) {
      onUpdate(bookId, updates);
    }
    setEditingBook(null);
  };

  const handleEditClose = () => {
    setEditingBook(null);
  };

  const filteredAndSortedLivres = useMemo(() => {
    let filtered = livres;

    // Apply general search filter
    if (searchTerm) {
      filtered = filtered.filter(livre =>
        livre.titre.toLowerCase().includes(searchTerm.toLowerCase()) ||
        livre.auteur.toLowerCase().includes(searchTerm.toLowerCase()) ||
        livre.categorie.toLowerCase().includes(searchTerm.toLowerCase()) ||
        livre.genre.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply author-specific search filter
    if (authorSearchTerm) {
      filtered = filtered.filter(livre =>
        livre.auteur.toLowerCase().includes(authorSearchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus === 'available') {
      filtered = filtered.filter(livre => livre.disponible);
    } else if (filterStatus === 'borrowed') {
      filtered = filtered.filter(livre => !livre.disponible);
    } else if (filterStatus === 'overdue') {
      filtered = filtered.filter(livre => {
        if (livre.disponible || !livre.dateRetour) return false;
        const dueDate = new Date(livre.dateRetour);
        return new Date() > dueDate;
      });
    } else if (filterStatus === 'due-soon') {
      filtered = filtered.filter(livre => {
        if (livre.disponible || !livre.dateRetour) return false;
        const dueDate = new Date(livre.dateRetour);
        const today = new Date();
        const diffTime = dueDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 3 && diffDays >= 0;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: string | boolean = a[sortField];
      let bValue: string | boolean = b[sortField];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [livres, searchTerm, authorSearchTerm, filterStatus, sortField, sortDirection]);

  // Export books to Excel-compatible CSV with UTF-8 BOM for French characters
  const handleExport = () => {
    exportBooksToCSV(filteredAndSortedLivres, 'livres.csv');
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title">Liste des livres</h2>
            <p className="card-subtitle">{filteredAndSortedLivres.length} livre(s) trouvé(s)</p>
          </div>
          <button onClick={handleExport} className="btn btn-secondary">
            <DownloadIcon size={16} />
            Exporter CSV
          </button>
        </div>
      </div>

      {/* Icon Legend */}
      <div className="legend-section">
        <h3 className="legend-title">Guide des icônes d'action</h3>
        <div className="icon-legend">
          <div className="legend-item">
            <div className="legend-icon legend-icon--primary">
              <UsersIcon size={16} />
            </div>
            <span className="legend-text">Assigner un livre à un membre</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--success">
              <CheckIcon size={16} />
            </div>
            <span className="legend-text">Marquer disponible</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--warning">
              <XIcon size={16} />
            </div>
            <span className="legend-text">Marquer emprunté</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--secondary">
              <EditIcon size={16} />
            </div>
            <span className="legend-text">Modifier</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--danger">
              <TrashIcon size={16} />
            </div>
            <span className="legend-text">Supprimer</span>
          </div>
        </div>
      </div>

      <div className="card-content">
        {/* Search and Filters */}
        <div className="search-bar">
          <SearchIcon className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Rechercher par titre, auteur, catégorie ou genre..."
            value={searchTerm}
            onChange={(e) => onSearchChange?.(e.target.value)}
          />
        </div>

        {/* Author-specific search */}
        {onAuthorSearchChange && (
          <div className="search-bar">
            <SearchIcon className="search-icon" />
            <input
              type="text"
              className="search-input"
              placeholder="Rechercher par auteur spécifiquement..."
              value={authorSearchTerm}
              onChange={(e) => onAuthorSearchChange(e.target.value)}
            />
          </div>
        )}

        <div className="filter-bar">
          <div className="filter-group">
            <FilterIcon size={16} />
            <span className="filter-label">Statut :</span>
            <select
              className="filter-select"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
            >
              <option value="all">Tous les livres</option>
              <option value="available">Disponibles</option>
              <option value="borrowed">Empruntés</option>
              <option value="overdue">En retard</option>
              <option value="due-soon">À rendre bientôt</option>
            </select>
          </div>
        </div>

        {/* Table */}
        <div className="table-container">
          <div className="table-responsive">
            <table className={`table table-mobile-stack book-management-table ${!isElectron ? 'browser-optimized' : ''}`}>
              <thead>
                <tr>
                  <th
                    className={`sortable ${sortField === 'titre' ? `sort-${sortDirection}` : ''}`}
                    onClick={() => handleSort('titre')}
                  >
                    Titre
                  </th>
                  <th
                    className={`sortable ${sortField === 'auteur' ? `sort-${sortDirection}` : ''}`}
                    onClick={() => handleSort('auteur')}
                  >
                    Auteur
                  </th>
                  <th
                    className={`sortable hide-mobile ${sortField === 'categorie' ? `sort-${sortDirection}` : ''}`}
                    onClick={() => handleSort('categorie')}
                  >
                    Catégorie
                  </th>
                  <th
                    className={`sortable hide-mobile ${sortField === 'genre' ? `sort-${sortDirection}` : ''}`}
                    onClick={() => handleSort('genre')}
                  >
                    Genre
                  </th>
                  <th
                    className={`sortable ${sortField === 'disponible' ? `sort-${sortDirection}` : ''}`}
                    onClick={() => handleSort('disponible')}
                  >
                    Statut
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedLivres.map((livre) => (
                  <tr
                    key={livre.id}
                    style={{ cursor: onSelectLivre ? 'pointer' : 'default' }}
                    onClick={() => onSelectLivre && onSelectLivre(livre)}
                  >
                    <td data-label="Titre" className="font-medium">{livre.titre}</td>
                    <td data-label="Auteur">{livre.auteur}</td>
                    <td data-label="Catégorie" className="hide-mobile">{livre.categorie}</td>
                    <td data-label="Genre" className="hide-mobile">{livre.genre}</td>
                    <td data-label="Statut">
                      <div className="status-info">
                        <span className={`status-badge ${livre.disponible ? 'active' : 'warning'}`}>
                          {livre.disponible ? 'DISPONIBLE' : 'EMPRUNTÉ'}
                        </span>
                        <div className="quantity-info">
                          <small className="text-muted">
                            {livre.quantiteDisponible} disponible{livre.quantiteDisponible > 1 ? 's' : ''} sur {livre.quantiteTotale}
                          </small>
                        </div>
                      </div>
                    </td>
                    <td data-label="Actions">
                      <div className="action-buttons">
                        {livre.quantiteDisponible > 0 && onAssignBook && (
                          <button
                            className="action-btn action-btn--primary action-btn--sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onAssignBook(livre);
                            }}
                            title={`Prêter ce livre (${livre.quantiteDisponible} copie${livre.quantiteDisponible > 1 ? 's' : ''} disponible${livre.quantiteDisponible > 1 ? 's' : ''})`}
                          >
                            <UsersIcon size={16} />
                          </button>
                        )}
                        {onToggleAvailability && (
                          <button
                            className={`action-btn action-btn--sm ${livre.disponible ? 'action-btn--warning' : 'action-btn--success'}`}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleToggleAvailabilityClick(livre);
                            }}
                          >
                            {livre.disponible ? <XIcon size={16} /> : <CheckIcon size={16} />}
                          </button>
                        )}
                        {onUpdate && (
                          <button
                            className="action-btn action-btn--secondary action-btn--sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditClick(livre);
                            }}
                          >
                            <EditIcon size={16} />
                          </button>
                        )}
                        {onDelete && (
                          <button
                            className="action-btn action-btn--danger action-btn--sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick(livre);
                            }}
                          >
                            <TrashIcon size={16} />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredAndSortedLivres.length === 0 && (
            <div className="text-center py-8 text-neutral">
              <p>Aucun livre trouvé.</p>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={handleCloseModal}
        onConfirm={handleConfirmAction}
        title={confirmationModal.type === 'delete' ? 'Supprimer le livre' : 'Modifier le statut du livre'}
        message={
          confirmationModal.type === 'delete'
            ? `Êtes-vous sûr de vouloir supprimer le livre "${confirmationModal.bookTitle}" ? Cette action est irréversible.`
            : confirmationModal.isAvailable
              ? `Êtes-vous sûr de vouloir marquer le livre "${confirmationModal.bookTitle}" comme emprunté ?`
              : `Êtes-vous sûr de vouloir marquer le livre "${confirmationModal.bookTitle}" comme disponible ?`
        }
        confirmText={confirmationModal.type === 'delete' ? 'Supprimer' : 'Confirmer'}
        variant={confirmationModal.type === 'delete' ? 'danger' : 'warning'}
      />

      {/* Edit Book Modal */}
      {editingBook && (
        <EditBookModal
          book={editingBook}
          isOpen={!!editingBook}
          onClose={handleEditClose}
          onSave={handleEditSave}
          categories={categories}
        />
      )}
    </div>
  );
};

export default LivreList;

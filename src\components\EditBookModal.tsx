import React, { useState, useEffect, useRef } from 'react';
import ModalPortal from './ModalPortal';
import { XIcon, CheckIcon } from './Icons';

// Flexible book type that can accept both Book and Livre types
type EditableBook = {
  id: number;
  titre: string;
  auteur: string;
  categorie: string;
  genre: string;
  isbn?: string;
  anneePublication?: number;
  description?: string;
  disponible?: boolean;
  quantiteTotale?: number;
  quantiteDisponible?: number;
  emprunteurId?: number;
  dateEmprunt?: string;
  dateRetour?: string;
  createdAt?: string;
  updatedAt?: string;
};

interface UpdateBookData {
  titre: string;
  auteur: string;
  categorie: string;
  genre: string;
  isbn?: string;
  anneePublication?: number;
  description?: string;
  quantiteTotale?: number;
}

interface EditBookModalProps {
  book: EditableBook;
  isOpen: boolean;
  onClose: () => void;
  onSave: (bookId: number, updates: UpdateBookData) => void;
  categories: string[];
}

const EditBookModal: React.FC<EditBookModalProps> = ({
  book,
  isOpen,
  onClose,
  onSave,
  categories
}) => {
  const [formData, setFormData] = useState<UpdateBookData>({
    titre: book.titre,
    auteur: book.auteur,
    categorie: book.categorie,
    genre: book.genre,
    isbn: book.isbn || '',
    anneePublication: book.anneePublication || undefined,
    description: book.description || '',
    quantiteTotale: book.quantiteTotale || 1
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setFormData({
      titre: book.titre,
      auteur: book.auteur,
      categorie: book.categorie,
      genre: book.genre,
      isbn: book.isbn || '',
      anneePublication: book.anneePublication || undefined,
      description: book.description || ''
    });
    setErrors({});
  }, [book]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.titre?.trim()) {
      newErrors.titre = 'Le titre est requis';
    }

    if (!formData.auteur?.trim()) {
      newErrors.auteur = 'L\'auteur est requis';
    }

    if (!formData.categorie?.trim()) {
      newErrors.categorie = 'La catégorie est requise';
    }

    if (!formData.genre?.trim()) {
      newErrors.genre = 'Le genre est requis';
    }

    // Quantity validation
    if (formData.quantiteTotale !== undefined) {
      if (!Number.isInteger(formData.quantiteTotale) || formData.quantiteTotale < 1) {
        newErrors.quantiteTotale = 'La quantité doit être un nombre entier positif';
      } else if (formData.quantiteTotale > 1000) {
        newErrors.quantiteTotale = 'La quantité ne peut pas dépasser 1000';
      }
      // Check if new quantity is less than currently borrowed copies
      const currentlyBorrowed = (book.quantiteTotale || 1) - (book.quantiteDisponible || 1);
      if (formData.quantiteTotale < currentlyBorrowed) {
        newErrors.quantiteTotale = `La quantité ne peut pas être inférieure au nombre de copies empruntées (${currentlyBorrowed})`;
      }
    }

    if (formData.anneePublication && (formData.anneePublication < 1000 || formData.anneePublication > new Date().getFullYear())) {
      newErrors.anneePublication = 'Année de publication invalide';
    }

    if (formData.isbn && formData.isbn.trim() && !/^[\d\-X]+$/.test(formData.isbn.replace(/[\s\-]/g, ''))) {
      newErrors.isbn = 'Format ISBN invalide';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      
      const updateData: UpdateBookData = {
        titre: formData.titre.trim(),
        auteur: formData.auteur.trim(),
        categorie: formData.categorie.trim(),
        genre: formData.genre.trim(),
        isbn: formData.isbn?.trim() || undefined,
        anneePublication: formData.anneePublication || undefined,
        description: formData.description?.trim() || undefined,
        quantiteTotale: formData.quantiteTotale || 1
      };
      
      onSave(book.id, updateData);
      onClose();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateBookData, value: string | number | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <ModalPortal isOpen={isOpen} onClose={onClose}>
      <div
        ref={modalRef}
        className="modal-container modal-container--large"
        onClick={e => e.stopPropagation()}
      >
        <div className="modal-header">
          <h2 className="modal-title">Modifier le livre</h2>
          <button
            type="button"
            className="modal-close"
            onClick={onClose}
            aria-label="Fermer"
          >
            <XIcon size={20} />
          </button>
        </div>

        <div className="modal-body">
          <form onSubmit={handleSubmit} id="edit-book-form" noValidate>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="titre" className="form-label">
                  Titre *
                </label>
                <input
                  type="text"
                  id="titre"
                  className={`form-input ${errors.titre ? 'error' : ''}`}
                  value={formData.titre}
                  onChange={(e) => handleInputChange('titre', e.target.value)}
                  placeholder="Titre du livre"
                />
                {errors.titre && <span className="error-message">{errors.titre}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="auteur" className="form-label">
                  Auteur *
                </label>
                <input
                  type="text"
                  id="auteur"
                  className={`form-input ${errors.auteur ? 'error' : ''}`}
                  value={formData.auteur}
                  onChange={(e) => handleInputChange('auteur', e.target.value)}
                  placeholder="Nom de l'auteur"
                />
                {errors.auteur && <span className="error-message">{errors.auteur}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="categorie" className="form-label">
                  Catégorie *
                </label>
                <select
                  id="categorie"
                  className={`form-select ${errors.categorie ? 'error' : ''}`}
                  value={formData.categorie}
                  onChange={(e) => handleInputChange('categorie', e.target.value)}
                >
                  <option value="">Sélectionner une catégorie</option>
                  {categories.map((cat) => (
                    <option key={cat} value={cat}>
                      {cat}
                    </option>
                  ))}
                </select>
                {errors.categorie && <span className="error-message">{errors.categorie}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="genre" className="form-label">
                  Genre *
                </label>
                <input
                  type="text"
                  id="genre"
                  className={`form-input ${errors.genre ? 'error' : ''}`}
                  value={formData.genre}
                  onChange={(e) => handleInputChange('genre', e.target.value)}
                  placeholder="Genre du livre"
                />
                {errors.genre && <span className="error-message">{errors.genre}</span>}
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="quantiteTotale" className="form-label">
                  Quantité totale *
                </label>
                <input
                  type="number"
                  id="quantiteTotale"
                  className={`form-input ${errors.quantiteTotale ? 'error' : ''}`}
                  value={formData.quantiteTotale || 1}
                  onChange={(e) => handleInputChange('quantiteTotale', e.target.value ? parseInt(e.target.value) : 1)}
                  placeholder="1"
                  min="1"
                  max="1000"
                />
                {errors.quantiteTotale && <span className="error-message">{errors.quantiteTotale}</span>}
                <small className="form-help">
                  Nombre total de copies de ce livre
                  {book.quantiteDisponible !== undefined && (
                    <span> (actuellement {book.quantiteDisponible} disponible{book.quantiteDisponible > 1 ? 's' : ''})</span>
                  )}
                </small>
              </div>
              <div className="form-group">
                <label htmlFor="isbn" className="form-label">
                  ISBN (optionnel)
                </label>
                <input
                  type="text"
                  id="isbn"
                  className={`form-input ${errors.isbn ? 'error' : ''}`}
                  value={formData.isbn || ''}
                  onChange={(e) => handleInputChange('isbn', e.target.value)}
                  placeholder="978-2-123456-78-9"
                />
                {errors.isbn && <span className="error-message">{errors.isbn}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="anneePublication" className="form-label">
                  Année de publication (optionnel)
                </label>
                <input
                  type="number"
                  id="anneePublication"
                  className={`form-input ${errors.anneePublication ? 'error' : ''}`}
                  value={formData.anneePublication || ''}
                  onChange={(e) => handleInputChange('anneePublication', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="2023"
                  min="1000"
                  max={new Date().getFullYear()}
                />
                {errors.anneePublication && <span className="error-message">{errors.anneePublication}</span>}
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="description" className="form-label">
                Description (optionnel)
              </label>
              <textarea
                id="description"
                className="form-input"
                rows={3}
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Description du livre..."
              />
            </div>

            {/* General form error message */}
            {Object.keys(errors).length > 0 && (
              <div className="form-error-summary">
                <p className="error-message">
                  Veuillez corriger les erreurs ci-dessus avant de soumettre le formulaire.
                </p>
              </div>
            )}
          </form>
        </div>

        <div className="modal-footer">
          <button
            type="button"
            className="action-btn action-btn--secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            <XIcon size={16} />
            <span className="action-label">Annuler</span>
          </button>
          <button
            type="submit"
            form="edit-book-form"
            className={`action-btn action-btn--primary ${isLoading ? 'action-btn--loading' : ''}`}
            disabled={isLoading}
          >
            <CheckIcon size={16} />
            <span className="action-label">{isLoading ? '' : 'Sauvegarder'}</span>
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default EditBookModal;

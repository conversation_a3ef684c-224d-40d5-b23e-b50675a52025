import React, { useState, useEffect, useRef } from 'react';
import type { LibraryMember, UpdateMemberData } from '../types/member';
import ModalPortal from './ModalPortal';

interface EditMemberModalProps {
  member: LibraryMember;
  isOpen: boolean;
  onClose: () => void;
  onSave: (memberId: number, updates: UpdateMemberData) => void;
}

const EditMemberModal: React.FC<EditMemberModalProps> = ({
  member,
  isOpen,
  onClose,
  onSave
}) => {
  const [formData, setFormData] = useState<UpdateMemberData>({
    nom: member.nom,
    prenom: member.prenom,
    fonction: member.fonction,
    telephone: member.telephone,
    adresse: member.adresse,
    email: member.email || '',
    isActive: member.isActive
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setFormData({
      nom: member.nom,
      prenom: member.prenom,
      fonction: member.fonction,
      telephone: member.telephone,
      adresse: member.adresse,
      email: member.email || '',
      isActive: member.isActive
    });
    setErrors({});
  }, [member]);

  // Auto-scroll to modal when it opens
  useEffect(() => {
    if (isOpen && modalRef.current) {
      // Small delay to ensure modal is rendered
      setTimeout(() => {
        modalRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }, 100);
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.nom?.trim()) {
      newErrors.nom = 'Le nom est requis';
    }

    if (!formData.prenom?.trim()) {
      newErrors.prenom = 'Le prénom est requis';
    }

    if (!formData.fonction?.trim()) {
      newErrors.fonction = 'La fonction est requise';
    }

    if (!formData.telephone?.trim()) {
      newErrors.telephone = 'Le téléphone est requis';
    } else if (!/^\+?[\d\s-()]+$/.test(formData.telephone)) {
      newErrors.telephone = 'Format de téléphone invalide';
    }

    if (formData.email && formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.adresse?.trim()) {
      newErrors.adresse = 'L\'adresse est requise';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      onSave(member.id, formData);
      onClose();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateMemberData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <ModalPortal isOpen={isOpen} onClose={onClose}>
      <div
        ref={modalRef}
        className="modal-container"
        onClick={e => e.stopPropagation()}
      >
        <div className="modal-header">
          <h2 className="modal-title">Modifier le membre</h2>
          <button
            type="button"
            className="modal-close"
            onClick={onClose}
            aria-label="Fermer"
          >
            ×
          </button>
        </div>

        <div className="modal-body">
          <form onSubmit={handleSubmit} id="edit-member-form" noValidate>
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="nom" className="form-label">
                  Nom *
                </label>
                <input
                  type="text"
                  id="nom"
                  className={`form-input ${errors.nom ? 'error' : ''}`}
                  value={formData.nom || ''}
                  onChange={(e) => handleInputChange('nom', e.target.value)}
                  placeholder="Nom du membre"
                />
                {errors.nom && <span className="error-message">{errors.nom}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="prenom" className="form-label">
                  Prénom *
                </label>
                <input
                  type="text"
                  id="prenom"
                  className={`form-input ${errors.prenom ? 'error' : ''}`}
                  value={formData.prenom || ''}
                  onChange={(e) => handleInputChange('prenom', e.target.value)}
                  placeholder="Prénom du membre"
                />
                {errors.prenom && <span className="error-message">{errors.prenom}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="fonction" className="form-label">
                  Fonction *
                </label>
                <input
                  type="text"
                  id="fonction"
                  className={`form-input ${errors.fonction ? 'error' : ''}`}
                  value={formData.fonction || ''}
                  onChange={(e) => handleInputChange('fonction', e.target.value)}
                  placeholder="Fonction du membre"
                />
                {errors.fonction && <span className="error-message">{errors.fonction}</span>}
              </div>

              <div className="form-group">
                <label htmlFor="telephone" className="form-label">
                  Téléphone *
                </label>
                <input
                  type="tel"
                  id="telephone"
                  className={`form-input ${errors.telephone ? 'error' : ''}`}
                  value={formData.telephone || ''}
                  onChange={(e) => handleInputChange('telephone', e.target.value)}
                  placeholder="+243 123 456 789"
                />
                {errors.telephone && <span className="error-message">{errors.telephone}</span>}
              </div>

              <div className="form-group form-group-full">
                <label htmlFor="email" className="form-label">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  className={`form-input ${errors.email ? 'error' : ''}`}
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
                {errors.email && <span className="error-message">{errors.email}</span>}
              </div>

              <div className="form-group form-group-full">
                <label htmlFor="adresse" className="form-label">
                  Adresse *
                </label>
                <textarea
                  id="adresse"
                  className={`form-input ${errors.adresse ? 'error' : ''}`}
                  value={formData.adresse || ''}
                  onChange={(e) => handleInputChange('adresse', e.target.value)}
                  placeholder="Adresse complète du membre"
                  rows={3}
                />
                {errors.adresse && <span className="error-message">{errors.adresse}</span>}
              </div>

              <div className="form-group form-group-full">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.isActive || false}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  />
                  <span className="checkbox-text">Membre actif</span>
                </label>
              </div>

              {/* General form error message */}
              {Object.keys(errors).length > 0 && (
                <div className="form-error-summary">
                  <p className="error-message">
                    Veuillez corriger les erreurs ci-dessus avant de soumettre le formulaire.
                  </p>
                </div>
              )}
            </div>
          </form>
        </div>

        <div className="modal-footer">
          <button
            type="button"
            className="action-btn action-btn--secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            Annuler
          </button>
          <button
            type="submit"
            form="edit-member-form"
            className={`action-btn action-btn--primary ${isLoading ? 'action-btn--loading' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? '' : 'Sauvegarder'}
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default EditMemberModal;
// Fixed modal structure

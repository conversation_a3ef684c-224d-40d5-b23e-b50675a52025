import React, { useState, useEffect } from 'react';
import { XIcon, CalendarIcon, CheckIcon } from './Icons';
import ModalPortal from './ModalPortal';

interface ExtendLoanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newDueDate: string) => void;
  bookTitle: string;
  memberName: string;
  currentDueDate: string;
}

const ExtendLoanModal: React.FC<ExtendLoanModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  bookTitle,
  memberName,
  currentDueDate
}) => {
  const [newDueDate, setNewDueDate] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      // Default to 14 days from current due date
      const currentDate = new Date(currentDueDate);
      const defaultNewDate = new Date(currentDate);
      defaultNewDate.setDate(currentDate.getDate() + 14);
      setNewDueDate(defaultNewDate.toISOString().split('T')[0]);
      setError('');
    }
  }, [isOpen, currentDueDate]);

  const handleDateChange = (date: string) => {
    setNewDueDate(date);
    
    // Validate that new date is after current due date
    const currentDate = new Date(currentDueDate);
    const selectedDate = new Date(date);
    
    if (selectedDate <= currentDate) {
      setError('La nouvelle date doit être postérieure à la date d\'échéance actuelle');
    } else {
      setError('');
    }
  };

  const handleConfirm = () => {
    if (!error && newDueDate) {
      // Call the loan extension function directly
      onConfirm(newDueDate);
      // Close the modal
      onClose();
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateDaysExtension = () => {
    if (!newDueDate) return 0;
    const currentDate = new Date(currentDueDate);
    const selectedDate = new Date(newDueDate);
    const diffTime = selectedDate.getTime() - currentDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (!isOpen) return null;

  return (
    <ModalPortal isOpen={isOpen} onClose={onClose}>
      <div className="modal-container" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">
            <CalendarIcon size={24} />
            Prolonger le prêt
          </h3>
          <button onClick={onClose} className="modal-close">
            <XIcon size={20} />
          </button>
        </div>

        <div className="modal-body">
          <div className="loan-extension-info">
            <div className="info-section">
              <h4 className="font-semibold text-neutral-900">Détails du prêt</h4>
              <div className="info-grid">
                <div className="info-item">
                  <span className="info-label">Livre :</span>
                  <span className="info-value font-medium">"{bookTitle}"</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Emprunteur :</span>
                  <span className="info-value font-medium">{memberName}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Échéance actuelle :</span>
                  <span className="info-value text-warning font-medium">
                    {formatDate(currentDueDate)}
                  </span>
                </div>
              </div>
            </div>

            <div className="form-section">
              <div className="form-group">
                <label htmlFor="newDueDate" className="form-label">
                  Nouvelle date d'échéance
                </label>
                <input
                  type="date"
                  id="newDueDate"
                  value={newDueDate}
                  onChange={(e) => handleDateChange(e.target.value)}
                  className={`form-input ${error ? 'form-input--error' : ''}`}
                  min={new Date(new Date(currentDueDate).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                />
                {error && (
                  <div className="form-error">{error}</div>
                )}
              </div>

              {newDueDate && !error && (
                <div className="extension-preview">
                  <div className="preview-card">
                    <h5 className="font-semibold text-success">Aperçu de la prolongation</h5>
                    <div className="preview-details">
                      <div className="preview-item">
                        <span>Nouvelle échéance :</span>
                        <span className="font-medium text-success">
                          {formatDate(newDueDate)}
                        </span>
                      </div>
                      <div className="preview-item">
                        <span>Extension :</span>
                        <span className="font-medium text-primary">
                          +{calculateDaysExtension()} jour(s)
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onClose} className="action-btn action-btn--secondary">
            <XIcon size={16} />
            <span className="action-label">Annuler</span>
          </button>
          <button
            onClick={handleConfirm}
            disabled={!!error || !newDueDate}
            className="action-btn action-btn--success"
          >
            <CheckIcon size={16} />
            <span className="action-label">Confirmer la prolongation</span>
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ExtendLoanModal;

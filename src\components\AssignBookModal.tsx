import React, { useState, useEffect } from 'react';
import { XIcon, SearchIcon, UsersIcon, CheckIcon, CalendarIcon } from './Icons';
import type { LibraryMember } from '../types/member';
import type { Book } from '../types/library';
import ProfileImage from './ProfileImage';

interface AssignBookModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (memberId: number, startDate: string, dueDate: string) => Promise<void>;
  book: Book | null;
  members: LibraryMember[];
  memberProfiles: any[];
}

const AssignBookModal: React.FC<AssignBookModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  book,
  members,
  memberProfiles
}) => {
  const [selectedMember, setSelectedMember] = useState<LibraryMember | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [startDate, setStartDate] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Reset form
      setSelectedMember(null);
      setSearchTerm('');
      setError('');
      setIsSubmitting(false);

      // Set default dates
      const today = new Date();
      const defaultDueDate = new Date();
      defaultDueDate.setDate(today.getDate() + 14);

      setStartDate(today.toISOString().split('T')[0]);
      setDueDate(defaultDueDate.toISOString().split('T')[0]);
    }
  }, [isOpen]);

  const filteredMembers = members.filter(member =>
    member.isActive && (
      member.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.numeroMembre.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const getMemberBorrowedCount = (memberId: number) => {
    const profile = memberProfiles.find(p => p.id === memberId);
    return profile?.nombreLivresEmpruntes || 0;
  };



  const handleDateChange = (type: 'start' | 'due', date: string) => {
    if (type === 'start') {
      setStartDate(date);
      // Auto-adjust due date to be 14 days after start date
      const newStartDate = new Date(date);
      const newDueDate = new Date(newStartDate);
      newDueDate.setDate(newStartDate.getDate() + 14);
      setDueDate(newDueDate.toISOString().split('T')[0]);
    } else {
      setDueDate(date);
    }

    // Validate dates
    const start = new Date(type === 'start' ? date : startDate);
    const due = new Date(type === 'due' ? date : dueDate);
    
    if (due <= start) {
      setError('La date de retour doit être postérieure à la date d\'emprunt');
    } else {
      setError('');
    }
  };

  const handleConfirm = async () => {
    if (!selectedMember) {
      setError('Veuillez sélectionner un membre');
      return;
    }

    if (!startDate || !dueDate) {
      setError('Veuillez renseigner les dates');
      return;
    }

    if (new Date(dueDate) <= new Date(startDate)) {
      setError('La date de retour doit être postérieure à la date d\'emprunt');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await onConfirm(selectedMember.id, startDate, dueDate);
      // Modal will be closed by parent component on success
    } catch (error) {
      // Handle inline error display for duplicate book borrowing
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue lors de l\'assignation du livre';

      if (errorMessage.includes('déjà emprunté ce livre')) {
        setError('Ce membre a déjà emprunté ce livre. Veuillez sélectionner un autre membre ou un autre livre.');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateLoanDuration = () => {
    if (!startDate || !dueDate) return 0;
    const start = new Date(startDate);
    const due = new Date(dueDate);
    const diffTime = due.getTime() - start.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (!isOpen || !book) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-container modal-container--large" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">
            <UsersIcon size={24} />
            Assigner un livre à un membre
          </h3>
          <button onClick={onClose} className="modal-close">
            <XIcon size={20} />
          </button>
        </div>

        <div className="modal-body">
          <div className="assign-book-content">
            {/* Book Information */}
            <div className="book-info-section">
              <h4 className="section-title">Livre à emprunter</h4>
              <div className="book-card">
                <div className="book-details">
                  <h5 className="book-title">"{book.titre}"</h5>
                  <p className="book-author">par {book.auteur}</p>
                  <div className="book-meta">
                    <span className="meta-item">Catégorie: {book.categorie}</span>
                    <span className="meta-item">ISBN: {book.isbn}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Member Selection */}
            <div className="member-selection-section">
              <h4 className="section-title">Sélectionner un membre</h4>
              
              <div className="search-box">
                <SearchIcon size={20} />
                <input
                  type="text"
                  placeholder="Rechercher par nom, prénom ou numéro de membre..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="search-input"
                />
              </div>

              <div className="members-list">
                {filteredMembers.length > 0 ? (
                  filteredMembers.slice(0, 5).map(member => (
                    <div
                      key={member.id}
                      className={`member-item ${selectedMember?.id === member.id ? 'selected' : ''}`}
                      onClick={() => setSelectedMember(member)}
                    >
                      <ProfileImage
                        src={member.photo}
                        alt={`${member.prenom} ${member.nom}`}
                        className="member-avatar"
                        fallbackName={`${member.prenom} ${member.nom}`}
                        size={40}
                      />
                      <div className="member-info">
                        <div className="member-name">{member.prenom} {member.nom}</div>
                        <div className="member-details">
                          <span className="member-number">{member.numeroMembre}</span>
                          <span className="member-books">
                            {getMemberBorrowedCount(member.id)} livre(s) emprunté(s)
                          </span>
                        </div>
                      </div>
                      {selectedMember?.id === member.id && (
                        <div className="selection-indicator">✓</div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="empty-state">
                    <p>Aucun membre trouvé</p>
                  </div>
                )}
              </div>
            </div>

            {/* Date Selection */}
            <div className="date-selection-section">
              <h4 className="section-title">Dates du prêt</h4>
              
              <div className="date-inputs">
                <div className="form-group">
                  <label htmlFor="startDate" className="form-label">
                    <CalendarIcon size={16} />
                    Date d'emprunt
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    value={startDate}
                    onChange={(e) => handleDateChange('start', e.target.value)}
                    className="form-input"
                    max={new Date().toISOString().split('T')[0]}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="dueDate" className="form-label">
                    <CalendarIcon size={16} />
                    Date de retour prévue
                  </label>
                  <input
                    type="date"
                    id="dueDate"
                    value={dueDate}
                    onChange={(e) => handleDateChange('due', e.target.value)}
                    className="form-input"
                    min={startDate}
                  />
                </div>
              </div>

              {startDate && dueDate && !error && (
                <div className="loan-summary">
                  <div className="summary-card">
                    <h5 className="summary-title">Résumé du prêt</h5>
                    <div className="summary-details">
                      <div className="summary-item">
                        <span>Durée :</span>
                        <span className="font-medium">{calculateLoanDuration()} jour(s)</span>
                      </div>
                      <div className="summary-item">
                        <span>Du :</span>
                        <span className="font-medium">{formatDate(startDate)}</span>
                      </div>
                      <div className="summary-item">
                        <span>Au :</span>
                        <span className="font-medium">{formatDate(dueDate)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <div className="form-error">{error}</div>
              )}
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onClose} className="action-btn action-btn--secondary">
            <XIcon size={16} />
            <span className="action-label">Annuler</span>
          </button>
          <button
            onClick={handleConfirm}
            disabled={!selectedMember || !!error || !startDate || !dueDate || isSubmitting}
            className="action-btn action-btn--primary"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span className="action-label">Traitement...</span>
              </>
            ) : (
              <>
                <CheckIcon size={16} />
                <span className="action-label">Confirmer l'emprunt</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssignBookModal;

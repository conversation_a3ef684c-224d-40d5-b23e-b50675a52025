// Service de base de données SQLite pour BiblioTech
// Database service for BiblioTech library management system

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import type { Book, Category } from '../types/library';
import type { LibraryMember, BorrowedBook, MemberProfile } from '../types/member';
import type { User, AuditLog, CreateUserData, LoginCredentials } from '../types/auth';

// Fonction utilitaire pour obtenir le chemin de la base de données
function getDatabasePath(): string {
  try {
    // Vérifier si nous sommes dans un environnement Electron
    const isElectron = typeof window !== 'undefined' && window.process && (window.process as any).type;

    if (isElectron) {
      // En environnement Electron, utiliser l'API Electron pour obtenir le chemin userData
      const { app } = require('electron');
      const userDataPath = app.getPath('userData');
      return path.join(userDataPath, 'bibliotech.db');
    }
  } catch (error) {
    // Fallback si Electron n'est pas disponible
    console.log('Electron non disponible, utilisation du dossier local');
  }

  // En développement ou dans un autre environnement, utiliser un dossier local
  const dataDir = path.join(process.cwd(), 'data');

  // Créer le dossier data s'il n'existe pas
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  return path.join(dataDir, 'bibliotech.db');
}

// Fonction utilitaire pour obtenir le dossier des images utilisateur
function getUserImagesPath(): string {
  try {
    // Vérifier si nous sommes dans un environnement Electron
    const isElectron = typeof window !== 'undefined' && window.process && (window.process as any).type;

    if (isElectron) {
      // En environnement Electron, utiliser l'API Electron pour obtenir le chemin userData
      const { app } = require('electron');
      const userDataPath = app.getPath('userData');
      const imagesPath = path.join(userDataPath, 'usersimages');

      // Créer le dossier s'il n'existe pas
      if (!fs.existsSync(imagesPath)) {
        fs.mkdirSync(imagesPath, { recursive: true });
      }

      return imagesPath;
    }
  } catch (error) {
    console.log('Electron non disponible, utilisation du dossier local pour les images');
  }

  // En développement, utiliser le dossier public/usersimages
  const imagesDir = path.join(process.cwd(), 'public', 'usersimages');

  // Créer le dossier s'il n'existe pas
  if (!fs.existsSync(imagesDir)) {
    fs.mkdirSync(imagesDir, { recursive: true });
  }

  return imagesDir;
}

// Fonction utilitaire pour sauvegarder une image de profil
function saveProfileImage(base64Data: string, filename: string): string {
  try {
    // Extraire les données base64 (enlever le préfixe data:image/...)
    const matches = base64Data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
    if (!matches) {
      throw new Error('Format de données base64 invalide');
    }

    const imageBuffer = Buffer.from(matches[2], 'base64');
    const imagesPath = getUserImagesPath();
    const filePath = path.join(imagesPath, filename);

    // Sauvegarder le fichier
    fs.writeFileSync(filePath, imageBuffer);

    // Retourner le chemin relatif pour l'affichage
    return `usersimages/${filename}`;
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de l\'image:', error);
    throw error;
  }
}

// Fonction utilitaire pour générer un nom de fichier unique
function generateImageFilename(originalName?: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension = originalName ? path.extname(originalName) : '.jpg';
  return `profile_${timestamp}_${random}${extension}`;
}

// Fonction utilitaire pour hacher un mot de passe
function hashPassword(password: string): string {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
}

// Fonction utilitaire pour vérifier un mot de passe
function verifyPassword(password: string, hashedPassword: string): boolean {
  const [salt, hash] = hashedPassword.split(':');
  const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
}



class DatabaseService {
  public db: Database.Database; // Exposer pour les opérations de réinitialisation
  private dbPath: string;

  constructor() {
    // Obtenir le chemin de la base de données selon l'environnement
    this.dbPath = getDatabasePath();

    console.log(`📁 Chemin de la base de données: ${this.dbPath}`);

    // Créer la base de données
    this.db = new Database(this.dbPath);
    this.initializeDatabase();

    // Initialiser les données par défaut
    this.seedInitialData();
  }

  /**
   * Initialise la structure de la base de données
   */
  private initializeDatabase(): void {
    // Activer les clés étrangères
    this.db.pragma('foreign_keys = ON');

    // Table des catégories
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL UNIQUE,
        description TEXT,
        ordre INTEGER DEFAULT 0,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des livres
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS livres (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        titre TEXT NOT NULL,
        auteur TEXT NOT NULL,
        categorie TEXT NOT NULL,
        genre TEXT,
        isbn TEXT,
        annee_publication INTEGER,
        description TEXT,
        disponible BOOLEAN NOT NULL DEFAULT 1,
        quantite_totale INTEGER NOT NULL DEFAULT 1,
        quantite_disponible INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (categorie) REFERENCES categories(nom) ON UPDATE CASCADE
      )
    `);

    // Migration: Add quantity columns to existing tables if they don't exist
    try {
      this.db.exec(`ALTER TABLE livres ADD COLUMN quantite_totale INTEGER NOT NULL DEFAULT 1`);
      this.db.exec(`ALTER TABLE livres ADD COLUMN quantite_disponible INTEGER NOT NULL DEFAULT 1`);

      // Update existing books: set quantities based on current availability
      this.db.exec(`
        UPDATE livres
        SET quantite_totale = 1,
            quantite_disponible = CASE WHEN disponible = 1 THEN 1 ELSE 0 END
        WHERE quantite_totale IS NULL OR quantite_disponible IS NULL
      `);
    } catch (error) {
      // Columns already exist, ignore error
    }

    // Table des membres de la bibliothèque
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS membres (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        fonction TEXT NOT NULL,
        numero_membre TEXT NOT NULL UNIQUE,
        telephone TEXT NOT NULL,
        adresse TEXT NOT NULL,
        email TEXT,
        photo TEXT,
        date_inscription TEXT NOT NULL,
        is_active BOOLEAN NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des utilisateurs système
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS utilisateurs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        email TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('super_admin', 'administrator')),
        is_active BOOLEAN NOT NULL DEFAULT 1,
        last_login TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des emprunts
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS emprunts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        livre_id INTEGER NOT NULL,
        membre_id INTEGER NOT NULL,
        date_emprunt TEXT NOT NULL,
        date_retour_prevue TEXT NOT NULL,
        date_retour_effective TEXT,
        statut TEXT NOT NULL DEFAULT 'actif' CHECK (statut IN ('actif', 'retourne', 'en_retard')),
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (livre_id) REFERENCES livres(id) ON DELETE CASCADE,
        FOREIGN KEY (membre_id) REFERENCES membres(id) ON DELETE CASCADE
      )
    `);

    // Table des journaux d'audit
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS journaux_audit (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        username TEXT NOT NULL,
        action TEXT NOT NULL,
        resource TEXT NOT NULL,
        resource_id INTEGER,
        details TEXT NOT NULL,
        timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE
      )
    `);

    // Index pour améliorer les performances
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_livres_categorie ON livres(categorie);
      CREATE INDEX IF NOT EXISTS idx_livres_disponible ON livres(disponible);
      CREATE INDEX IF NOT EXISTS idx_emprunts_livre ON emprunts(livre_id);
      CREATE INDEX IF NOT EXISTS idx_emprunts_membre ON emprunts(membre_id);
      CREATE INDEX IF NOT EXISTS idx_emprunts_statut ON emprunts(statut);
      CREATE INDEX IF NOT EXISTS idx_membres_numero ON membres(numero_membre);
      CREATE INDEX IF NOT EXISTS idx_audit_user ON journaux_audit(user_id);
      CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON journaux_audit(timestamp);
    `);

    // Trigger pour mettre à jour automatiquement updated_at
    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_livres_timestamp 
      AFTER UPDATE ON livres
      BEGIN
        UPDATE livres SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS update_membres_timestamp 
      AFTER UPDATE ON membres
      BEGIN
        UPDATE membres SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS update_categories_timestamp 
      AFTER UPDATE ON categories
      BEGIN
        UPDATE categories SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS update_emprunts_timestamp 
      AFTER UPDATE ON emprunts
      BEGIN
        UPDATE emprunts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS update_utilisateurs_timestamp 
      AFTER UPDATE ON utilisateurs
      BEGIN
        UPDATE utilisateurs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);
  }

  /**
   * Ferme la connexion à la base de données
   */
  close(): void {
    this.db.close();
  }

  /**
   * Obtient le chemin de la base de données
   */
  getDatabasePath(): string {
    return this.dbPath;
  }

  /**
   * Exécute une sauvegarde de la base de données
   */
  backup(backupPath: string): void {
    this.db.backup(backupPath);
  }

  /**
   * Obtient les statistiques de la base de données
   */
  getStats(): {
    totalBooks: number;
    totalMembers: number;
    totalLoans: number;
    activeLoans: number;
    overdueLoans: number;
  } {
    const totalBooks = this.db.prepare('SELECT COUNT(*) as count FROM livres').get() as { count: number };
    const totalMembers = this.db.prepare('SELECT COUNT(*) as count FROM membres WHERE is_active = 1').get() as { count: number };
    const totalLoans = this.db.prepare('SELECT COUNT(*) as count FROM emprunts').get() as { count: number };
    const activeLoans = this.db.prepare('SELECT COUNT(*) as count FROM emprunts WHERE statut = "actif"').get() as { count: number };
    const overdueLoans = this.db.prepare('SELECT COUNT(*) as count FROM emprunts WHERE statut = "en_retard"').get() as { count: number };

    return {
      totalBooks: totalBooks.count,
      totalMembers: totalMembers.count,
      totalLoans: totalLoans.count,
      activeLoans: activeLoans.count,
      overdueLoans: overdueLoans.count
    };
  }

  // ==================== MÉTHODES POUR LES LIVRES ====================

  /**
   * Récupère tous les livres
   */
  getAllBooks(): Book[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        titre,
        auteur,
        categorie,
        genre,
        isbn,
        annee_publication as anneePublication,
        description,
        disponible,
        quantite_totale as quantiteTotale,
        quantite_disponible as quantiteDisponible,
        created_at as createdAt,
        updated_at as updatedAt
      FROM livres
      ORDER BY titre ASC
    `);

    const rows = stmt.all() as any[];
    return rows.map(row => ({
      ...row,
      disponible: Boolean(row.disponible),
      quantiteTotale: row.quantiteTotale || 1,
      quantiteDisponible: row.quantiteDisponible || (row.disponible ? 1 : 0),
      // Ajouter les informations d'emprunt si le livre n'est pas disponible
      ...(row.disponible ? {} : this.getBookLoanInfo(row.id))
    }));
  }

  /**
   * Récupère les informations d'emprunt d'un livre
   */
  private getBookLoanInfo(bookId: number): Partial<Book> {
    const stmt = this.db.prepare(`
      SELECT
        e.membre_id as emprunteurId,
        e.date_emprunt as dateEmprunt,
        e.date_retour_prevue as dateRetour
      FROM emprunts e
      WHERE e.livre_id = ? AND e.statut = 'actif'
      ORDER BY e.created_at DESC
      LIMIT 1
    `);

    const loan = stmt.get(bookId) as any;
    return loan ? {
      emprunteurId: loan.emprunteurId,
      dateEmprunt: loan.dateEmprunt,
      dateRetour: loan.dateRetour
    } : {};
  }

  /**
   * Récupère un livre par son ID
   */
  getBookById(id: number): Book | null {
    const stmt = this.db.prepare(`
      SELECT
        id,
        titre,
        auteur,
        categorie,
        genre,
        isbn,
        annee_publication as anneePublication,
        description,
        disponible,
        quantite_totale as quantiteTotale,
        quantite_disponible as quantiteDisponible,
        created_at as createdAt,
        updated_at as updatedAt
      FROM livres
      WHERE id = ?
    `);

    const row = stmt.get(id) as any;
    if (!row) return null;

    return {
      ...row,
      disponible: Boolean(row.disponible),
      quantiteTotale: row.quantiteTotale || 1,
      quantiteDisponible: row.quantiteDisponible || (row.disponible ? 1 : 0),
      ...(row.disponible ? {} : this.getBookLoanInfo(row.id))
    };
  }

  /**
   * Ajoute un nouveau livre
   */
  addBook(book: Omit<Book, 'id' | 'createdAt' | 'updatedAt' | 'disponible'>): Book {
    const quantite = book.quantiteTotale || 1;
    const stmt = this.db.prepare(`
      INSERT INTO livres (
        titre, auteur, categorie, genre, isbn,
        annee_publication, description, disponible,
        quantite_totale, quantite_disponible
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      book.titre,
      book.auteur,
      book.categorie,
      book.genre || null,
      book.isbn || null,
      book.anneePublication || null,
      book.description || null,
      quantite > 0 ? 1 : 0, // disponible: true if quantity > 0
      quantite,
      quantite // initially all copies are available
    );

    const newBook = this.getBookById(result.lastInsertRowid as number);
    if (!newBook) {
      throw new Error('Erreur lors de la création du livre');
    }

    return newBook;
  }

  /**
   * Met à jour un livre
   */
  updateBook(id: number, updates: Partial<Omit<Book, 'id' | 'createdAt' | 'updatedAt'>>): boolean {
    const fields: string[] = [];
    const values: any[] = [];

    // Construire dynamiquement la requête UPDATE
    if (updates.titre !== undefined) {
      fields.push('titre = ?');
      values.push(updates.titre);
    }
    if (updates.auteur !== undefined) {
      fields.push('auteur = ?');
      values.push(updates.auteur);
    }
    if (updates.categorie !== undefined) {
      fields.push('categorie = ?');
      values.push(updates.categorie);
    }
    if (updates.genre !== undefined) {
      fields.push('genre = ?');
      values.push(updates.genre);
    }
    if (updates.isbn !== undefined) {
      fields.push('isbn = ?');
      values.push(updates.isbn);
    }
    if (updates.anneePublication !== undefined) {
      fields.push('annee_publication = ?');
      values.push(updates.anneePublication);
    }
    if (updates.description !== undefined) {
      fields.push('description = ?');
      values.push(updates.description);
    }
    if (updates.disponible !== undefined) {
      fields.push('disponible = ?');
      values.push(updates.disponible ? 1 : 0);
    }

    // Handle quantity updates with special logic
    if (updates.quantiteTotale !== undefined) {
      // Get current quantities to calculate the difference
      const currentBook = this.getBookById(id);
      if (currentBook) {
        const currentBorrowed = currentBook.quantiteTotale - currentBook.quantiteDisponible;
        const newAvailable = updates.quantiteTotale - currentBorrowed;

        // Ensure we don't go below borrowed copies
        if (newAvailable >= 0) {
          fields.push('quantite_totale = ?');
          values.push(updates.quantiteTotale);
          fields.push('quantite_disponible = ?');
          values.push(newAvailable);
          fields.push('disponible = ?');
          values.push(newAvailable > 0 ? 1 : 0);
        }
      }
    }

    if (fields.length === 0) return false;

    values.push(id);
    const stmt = this.db.prepare(`
      UPDATE livres
      SET ${fields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  }

  /**
   * Supprime un livre
   */
  deleteBook(id: number): boolean {
    // Vérifier s'il y a des emprunts actifs
    const activeLoansStmt = this.db.prepare(`
      SELECT COUNT(*) as count
      FROM emprunts
      WHERE livre_id = ? AND statut = 'actif'
    `);
    const activeLoanCount = activeLoansStmt.get(id) as { count: number };

    if (activeLoanCount.count > 0) {
      throw new Error('Impossible de supprimer un livre actuellement emprunté');
    }

    const stmt = this.db.prepare('DELETE FROM livres WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // ==================== MÉTHODES POUR LES CATÉGORIES ====================

  /**
   * Récupère toutes les catégories
   */
  getAllCategories(): Category[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        nom as name,
        description,
        ordre as order,
        created_at as createdAt,
        updated_at as updatedAt
      FROM categories
      ORDER BY ordre ASC, nom ASC
    `);

    return stmt.all() as Category[];
  }

  /**
   * Récupère une catégorie par son ID
   */
  getCategoryById(id: number): Category | null {
    const stmt = this.db.prepare(`
      SELECT
        id,
        nom as name,
        description,
        ordre as order,
        created_at as createdAt,
        updated_at as updatedAt
      FROM categories
      WHERE id = ?
    `);

    return stmt.get(id) as Category | null;
  }

  /**
   * Ajoute une nouvelle catégorie
   */
  addCategory(name: string, description?: string): Category {
    // Obtenir le prochain ordre
    const maxOrderStmt = this.db.prepare('SELECT MAX(ordre) as maxOrder FROM categories');
    const maxOrderResult = maxOrderStmt.get() as { maxOrder: number | null };
    const nextOrder = (maxOrderResult.maxOrder || 0) + 1;

    const stmt = this.db.prepare(`
      INSERT INTO categories (nom, description, ordre)
      VALUES (?, ?, ?)
    `);

    const result = stmt.run(name, description || null, nextOrder);
    const newCategory = this.getCategoryById(result.lastInsertRowid as number);

    if (!newCategory) {
      throw new Error('Erreur lors de la création de la catégorie');
    }

    return newCategory;
  }

  /**
   * Met à jour une catégorie
   */
  updateCategory(id: number, name: string, description?: string): boolean {
    const stmt = this.db.prepare(`
      UPDATE categories
      SET nom = ?, description = ?
      WHERE id = ?
    `);

    const result = stmt.run(name, description || null, id);
    return result.changes > 0;
  }

  /**
   * Supprime une catégorie
   */
  deleteCategory(id: number): boolean {
    const category = this.getCategoryById(id);
    if (!category) return false;

    // Déplacer tous les livres de cette catégorie vers "Non classé"
    const updateBooksStmt = this.db.prepare(`
      UPDATE livres
      SET categorie = 'Non classé'
      WHERE categorie = ?
    `);
    updateBooksStmt.run(category.name);

    // Supprimer la catégorie
    const stmt = this.db.prepare('DELETE FROM categories WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  /**
   * Réorganise l'ordre des catégories
   */
  reorderCategories(categoryIds: number[]): boolean {
    const transaction = this.db.transaction(() => {
      categoryIds.forEach((id, index) => {
        const stmt = this.db.prepare('UPDATE categories SET ordre = ? WHERE id = ?');
        stmt.run(index, id);
      });
    });

    try {
      transaction();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Compte le nombre de livres dans une catégorie
   */
  getBooksCountInCategory(categoryName: string): number {
    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM livres WHERE categorie = ?');
    const result = stmt.get(categoryName) as { count: number };
    return result.count;
  }

  // ==================== MÉTHODES POUR LES MEMBRES ====================

  /**
   * Récupère tous les membres
   */
  getAllMembers(): LibraryMember[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        nom,
        prenom,
        fonction,
        numero_membre as numeroMembre,
        telephone,
        adresse,
        email,
        photo,
        date_inscription as dateInscription,
        is_active as isActive,
        created_at as createdAt,
        updated_at as updatedAt
      FROM membres
      ORDER BY nom ASC, prenom ASC
    `);

    const rows = stmt.all() as any[];
    return rows.map(row => ({
      ...row,
      isActive: Boolean(row.isActive)
    }));
  }

  /**
   * Récupère un membre par son ID
   */
  getMemberById(id: number): LibraryMember | null {
    const stmt = this.db.prepare(`
      SELECT
        id,
        nom,
        prenom,
        fonction,
        numero_membre as numeroMembre,
        telephone,
        adresse,
        email,
        photo,
        date_inscription as dateInscription,
        is_active as isActive,
        created_at as createdAt,
        updated_at as updatedAt
      FROM membres
      WHERE id = ?
    `);

    const row = stmt.get(id) as any;
    if (!row) return null;

    return {
      ...row,
      isActive: Boolean(row.isActive)
    };
  }

  /**
   * Ajoute un nouveau membre
   */
  addMember(member: Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'>): LibraryMember {
    const stmt = this.db.prepare(`
      INSERT INTO membres (
        nom, prenom, fonction, numero_membre, telephone,
        adresse, email, photo, date_inscription, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      member.nom,
      member.prenom,
      member.fonction,
      member.numeroMembre,
      member.telephone,
      member.adresse,
      member.email || null,
      member.photo,
      member.dateInscription,
      member.isActive ? 1 : 0
    );

    const newMember = this.getMemberById(result.lastInsertRowid as number);
    if (!newMember) {
      throw new Error('Erreur lors de la création du membre');
    }

    return newMember;
  }

  /**
   * Met à jour un membre
   */
  updateMember(id: number, updates: Partial<Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'>>): boolean {
    const fields: string[] = [];
    const values: any[] = [];

    if (updates.nom !== undefined) {
      fields.push('nom = ?');
      values.push(updates.nom);
    }
    if (updates.prenom !== undefined) {
      fields.push('prenom = ?');
      values.push(updates.prenom);
    }
    if (updates.fonction !== undefined) {
      fields.push('fonction = ?');
      values.push(updates.fonction);
    }
    if (updates.numeroMembre !== undefined) {
      fields.push('numero_membre = ?');
      values.push(updates.numeroMembre);
    }
    if (updates.telephone !== undefined) {
      fields.push('telephone = ?');
      values.push(updates.telephone);
    }
    if (updates.adresse !== undefined) {
      fields.push('adresse = ?');
      values.push(updates.adresse);
    }
    if (updates.email !== undefined) {
      fields.push('email = ?');
      values.push(updates.email);
    }
    if (updates.photo !== undefined) {
      fields.push('photo = ?');
      values.push(updates.photo);
    }
    if (updates.dateInscription !== undefined) {
      fields.push('date_inscription = ?');
      values.push(updates.dateInscription);
    }
    if (updates.isActive !== undefined) {
      fields.push('is_active = ?');
      values.push(updates.isActive ? 1 : 0);
    }

    if (fields.length === 0) return false;

    values.push(id);
    const stmt = this.db.prepare(`
      UPDATE membres
      SET ${fields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  }

  /**
   * Supprime un membre
   */
  deleteMember(id: number): boolean {
    // Vérifier s'il y a des emprunts actifs
    const activeLoansStmt = this.db.prepare(`
      SELECT COUNT(*) as count
      FROM emprunts
      WHERE membre_id = ? AND statut = 'actif'
    `);
    const activeLoanCount = activeLoansStmt.get(id) as { count: number };

    if (activeLoanCount.count > 0) {
      throw new Error('Impossible de supprimer un membre ayant des emprunts actifs');
    }

    const stmt = this.db.prepare('DELETE FROM membres WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // ==================== MÉTHODES POUR LES EMPRUNTS ====================

  /**
   * Crée un nouvel emprunt
   */
  createLoan(bookId: number, memberId: number, startDate: string, dueDate: string): boolean {
    const transaction = this.db.transaction(() => {
      // Check if book has available copies
      const checkBookStmt = this.db.prepare('SELECT quantite_disponible, titre FROM livres WHERE id = ?');
      const book = checkBookStmt.get(bookId) as any;

      if (!book || book.quantite_disponible <= 0) {
        throw new Error('Aucune copie disponible pour ce livre');
      }

      // Check if member already has an active loan for this specific book
      const checkExistingLoanStmt = this.db.prepare(`
        SELECT COUNT(*) as count
        FROM emprunts
        WHERE livre_id = ? AND membre_id = ? AND statut = 'actif'
      `);
      const existingLoan = checkExistingLoanStmt.get(bookId, memberId) as any;

      if (existingLoan && existingLoan.count > 0) {
        throw new Error(`Ce membre a déjà emprunté ce livre ("${book.titre}"). Un membre ne peut pas emprunter plusieurs copies du même livre.`);
      }

      // Decrease available quantity by 1
      const updateBookStmt = this.db.prepare(`
        UPDATE livres
        SET quantite_disponible = quantite_disponible - 1,
            disponible = CASE WHEN quantite_disponible - 1 > 0 THEN 1 ELSE 0 END
        WHERE id = ?
      `);
      updateBookStmt.run(bookId);

      // Créer l'emprunt
      const createLoanStmt = this.db.prepare(`
        INSERT INTO emprunts (livre_id, membre_id, date_emprunt, date_retour_prevue, statut)
        VALUES (?, ?, ?, ?, 'actif')
      `);
      createLoanStmt.run(bookId, memberId, startDate, dueDate);
    });

    try {
      transaction();
      return true;
    } catch (error) {
      console.error('Error creating loan:', error);
      throw error; // Re-throw to preserve the specific error message
    }
  }

  /**
   * Retourne un livre (termine l'emprunt)
   */
  returnBook(bookId: number): boolean {
    const transaction = this.db.transaction(() => {
      // Check if there are active loans for this book
      const checkStmt = this.db.prepare('SELECT COUNT(*) as count FROM emprunts WHERE livre_id = ? AND statut = "actif"');
      const result = checkStmt.get(bookId) as any;

      if (!result || result.count === 0) {
        throw new Error('Aucun emprunt actif trouvé pour ce livre');
      }

      // Increase available quantity by 1
      const updateBookStmt = this.db.prepare(`
        UPDATE livres
        SET quantite_disponible = quantite_disponible + 1,
            disponible = 1
        WHERE id = ?
      `);
      updateBookStmt.run(bookId);

      // Mettre à jour l'emprunt (only one active loan per book at a time)
      const updateLoanStmt = this.db.prepare(`
        UPDATE emprunts
        SET statut = 'retourne', date_retour_effective = CURRENT_TIMESTAMP
        WHERE livre_id = ? AND statut = 'actif'
        ORDER BY date_emprunt ASC
        LIMIT 1
      `);
      updateLoanStmt.run(bookId);
    });

    try {
      transaction();
      return true;
    } catch (error) {
      console.error('Error returning book:', error);
      return false;
    }
  }

  /**
   * Prolonge un emprunt
   */
  extendLoan(bookId: number, newDueDate: string): boolean {
    const stmt = this.db.prepare(`
      UPDATE emprunts
      SET date_retour_prevue = ?, statut = 'actif'
      WHERE livre_id = ? AND statut IN ('actif', 'en_retard')
    `);

    const result = stmt.run(newDueDate, bookId);
    return result.changes > 0;
  }

  /**
   * Met à jour le statut des emprunts en retard
   */
  updateOverdueLoans(): void {
    const today = new Date().toISOString().split('T')[0];
    const stmt = this.db.prepare(`
      UPDATE emprunts
      SET statut = 'en_retard'
      WHERE statut = 'actif' AND date_retour_prevue < ?
    `);
    stmt.run(today);
  }

  /**
   * Récupère les livres empruntés par un membre
   */
  getMemberBorrowedBooks(memberId: number): BorrowedBook[] {
    const stmt = this.db.prepare(`
      SELECT
        e.id,
        e.livre_id as livreId,
        l.titre,
        l.auteur,
        e.date_emprunt as dateEmprunt,
        e.date_retour_prevue as dateRetour,
        e.statut
      FROM emprunts e
      JOIN livres l ON e.livre_id = l.id
      WHERE e.membre_id = ? AND e.statut IN ('actif', 'en_retard')
      ORDER BY e.date_emprunt DESC
    `);

    const rows = stmt.all(memberId) as any[];
    return rows.map(row => {
      const today = new Date();
      const dueDate = new Date(row.dateRetour);
      const timeDiff = dueDate.getTime() - today.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      return {
        id: row.id,
        livreId: row.livreId,
        titre: row.titre,
        auteur: row.auteur,
        dateEmprunt: row.dateEmprunt,
        dateRetour: row.dateRetour,
        isOverdue: daysDiff < 0,
        daysUntilDue: daysDiff
      };
    });
  }

  /**
   * Récupère le profil complet d'un membre avec ses emprunts
   */
  getMemberProfile(memberId: number): MemberProfile | null {
    const member = this.getMemberById(memberId);
    if (!member) return null;

    const borrowedBooks = this.getMemberBorrowedBooks(memberId);
    const overdueBooks = borrowedBooks.filter(book => book.isOverdue);

    return {
      ...member,
      livresEmpruntes: borrowedBooks,
      nombreLivresEmpruntes: borrowedBooks.length,
      nombreLivresEnRetard: overdueBooks.length
    };
  }

  /**
   * Récupère tous les profils de membres avec leurs emprunts
   */
  getAllMemberProfiles(): MemberProfile[] {
    const members = this.getAllMembers();
    return members.map(member => {
      const borrowedBooks = this.getMemberBorrowedBooks(member.id);
      const overdueBooks = borrowedBooks.filter(book => book.isOverdue);

      return {
        ...member,
        livresEmpruntes: borrowedBooks,
        nombreLivresEmpruntes: borrowedBooks.length,
        nombreLivresEnRetard: overdueBooks.length
      };
    });
  }

  // ==================== MÉTHODES POUR LES UTILISATEURS ====================

  /**
   * Récupère tous les utilisateurs
   */
  getAllUsers(): User[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        username,
        email,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      ORDER BY username ASC
    `);

    const rows = stmt.all() as any[];
    return rows.map(row => ({
      ...row,
      isActive: Boolean(row.isActive)
    }));
  }

  /**
   * Récupère un utilisateur par son nom d'utilisateur
   */
  getUserByUsername(username: string): (User & { password_hash: string }) | null {
    const stmt = this.db.prepare(`
      SELECT
        id,
        username,
        email,
        password_hash,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      WHERE username = ?
    `);

    const row = stmt.get(username) as any;
    if (!row) return null;

    return {
      ...row,
      isActive: Boolean(row.isActive)
    };
  }

  /**
   * Ajoute un nouvel utilisateur
   */
  addUser(username: string, email: string, passwordHash: string, role: 'super_admin' | 'administrator'): User {
    const stmt = this.db.prepare(`
      INSERT INTO utilisateurs (username, email, password_hash, role, is_active)
      VALUES (?, ?, ?, ?, 1)
    `);

    const result = stmt.run(username, email, passwordHash, role);

    const newUserStmt = this.db.prepare(`
      SELECT
        id,
        username,
        email,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      WHERE id = ?
    `);

    const newUser = newUserStmt.get(result.lastInsertRowid) as any;
    if (!newUser) {
      throw new Error('Erreur lors de la création de l\'utilisateur');
    }

    return {
      ...newUser,
      isActive: Boolean(newUser.isActive)
    };
  }

  /**
   * Met à jour la dernière connexion d'un utilisateur
   */
  updateLastLogin(userId: number): boolean {
    const stmt = this.db.prepare(`
      UPDATE utilisateurs
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(userId);
    return result.changes > 0;
  }

  // ==================== MÉTHODES POUR LES JOURNAUX D'AUDIT ====================

  /**
   * Ajoute une entrée au journal d'audit
   */
  addAuditLog(userId: number, username: string, action: string, resource: string, resourceId?: number, details?: string): boolean {
    const stmt = this.db.prepare(`
      INSERT INTO journaux_audit (user_id, username, action, resource, resource_id, details)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(userId, username, action, resource, resourceId || null, details || '');
    return result.changes > 0;
  }

  /**
   * Récupère les journaux d'audit
   */
  getAuditLogs(limit: number = 100, offset: number = 0): AuditLog[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        user_id as userId,
        username,
        action,
        resource,
        resource_id as resourceId,
        details,
        timestamp
      FROM journaux_audit
      ORDER BY timestamp DESC
      LIMIT ? OFFSET ?
    `);

    return stmt.all(limit, offset) as AuditLog[];
  }

  /**
   * Récupère les journaux d'audit pour un utilisateur spécifique
   */
  getUserAuditLogs(userId: number, limit: number = 50): AuditLog[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        user_id as userId,
        username,
        action,
        resource,
        resource_id as resourceId,
        details,
        timestamp
      FROM journaux_audit
      WHERE user_id = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `);

    return stmt.all(userId, limit) as AuditLog[];
  }

  // ==================== MÉTHODES D'AUTHENTIFICATION ====================

  /**
   * Authentifie un utilisateur
   */
  authenticateUser(credentials: LoginCredentials): User | null {
    const userWithPassword = this.getUserByUsername(credentials.username);

    if (!userWithPassword || !userWithPassword.isActive) {
      return null;
    }

    // Vérifier le mot de passe
    if (!verifyPassword(credentials.password, userWithPassword.password_hash)) {
      return null;
    }

    // Mettre à jour la dernière connexion
    this.updateLastLogin(userWithPassword.id);

    // Retourner l'utilisateur sans le hash du mot de passe
    const { password_hash, ...user } = userWithPassword;
    return user;
  }

  /**
   * Crée un nouvel utilisateur avec mot de passe haché
   */
  createUser(userData: CreateUserData): User {
    // Vérifier si l'utilisateur existe déjà
    const existingUser = this.getUserByUsername(userData.username);
    if (existingUser) {
      throw new Error('Un utilisateur avec ce nom d\'utilisateur existe déjà');
    }

    // Hacher le mot de passe
    const passwordHash = hashPassword(userData.password);

    // Créer l'utilisateur
    return this.addUser(userData.username, userData.email, passwordHash, userData.role);
  }

  /**
   * Met à jour un utilisateur
   */
  updateUser(userId: number, updates: Partial<User>): boolean {
    const fields: string[] = [];
    const values: any[] = [];

    // Construire dynamiquement la requête UPDATE
    if (updates.username !== undefined) {
      fields.push('username = ?');
      values.push(updates.username);
    }
    if (updates.email !== undefined) {
      fields.push('email = ?');
      values.push(updates.email);
    }
    if (updates.role !== undefined) {
      fields.push('role = ?');
      values.push(updates.role);
    }
    if (updates.isActive !== undefined) {
      fields.push('is_active = ?');
      values.push(updates.isActive ? 1 : 0);
    }

    if (fields.length === 0) return false;

    values.push(userId);
    const stmt = this.db.prepare(`
      UPDATE utilisateurs
      SET ${fields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  }

  /**
   * Supprime un utilisateur
   */
  deleteUser(userId: number): boolean {
    const stmt = this.db.prepare('DELETE FROM utilisateurs WHERE id = ?');
    const result = stmt.run(userId);
    return result.changes > 0;
  }

  /**
   * Met à jour le mot de passe d'un utilisateur
   */
  updateUserPassword(userId: number, newPassword: string): boolean {
    const passwordHash = hashPassword(newPassword);

    const stmt = this.db.prepare(`
      UPDATE utilisateurs
      SET password_hash = ?
      WHERE id = ?
    `);

    const result = stmt.run(passwordHash, userId);
    return result.changes > 0;
  }

  // ==================== MÉTHODES DE GESTION DES FICHIERS ====================

  /**
   * Sauvegarde une image de profil et retourne le chemin
   */
  saveProfileImage(base64Data: string, originalFilename?: string): string {
    const filename = generateImageFilename(originalFilename);
    return saveProfileImage(base64Data, filename);
  }

  /**
   * Supprime une image de profil
   */
  deleteProfileImage(imagePath: string): boolean {
    try {
      const imagesPath = getUserImagesPath();
      const filename = path.basename(imagePath);
      const fullPath = path.join(imagesPath, filename);

      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'image:', error);
      return false;
    }
  }

  // ==================== MÉTHODES D'INITIALISATION ====================

  /**
   * Initialise les données par défaut si la base est vide
   */
  seedInitialData(): void {
    // Vérifier si des données existent déjà
    const userCount = this.db.prepare('SELECT COUNT(*) as count FROM utilisateurs').get() as { count: number };
    const categoryCount = this.db.prepare('SELECT COUNT(*) as count FROM categories').get() as { count: number };

    // Créer l'utilisateur super admin par défaut
    if (userCount.count === 0) {
      console.log('🔐 Création des utilisateurs par défaut avec mots de passe sécurisés...');

      // Create default super admin user
      this.createUser({
        username: 'superadmin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'super_admin'
      });

      // Create default administrator user
      this.createUser({
        username: 'admin1',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'administrator'
      });

      console.log('✅ Utilisateurs par défaut créés avec succès');
    }

    // Créer les catégories par défaut
    if (categoryCount.count === 0) {
      console.log('📚 Création des catégories par défaut...');
      const defaultCategories = [
        { name: 'Fiction', description: 'Romans et nouvelles' },
        { name: 'Science', description: 'Livres scientifiques et techniques' },
        { name: 'Histoire', description: 'Livres d\'histoire et biographies' },
        { name: 'Philosophie', description: 'Ouvrages philosophiques et essais' },
        { name: 'Biographie', description: 'Biographies et mémoires' }
      ];

      for (const category of defaultCategories) {
        this.addCategory(category.name, category.description);
      }
    }
  }
}

// Instance singleton de la base de données
let databaseInstance: DatabaseService | null = null;

export const getDatabase = (): DatabaseService => {
  if (!databaseInstance) {
    console.log('🚀 Initialisation de la base de données BiblioTech...');
    databaseInstance = new DatabaseService();
    console.log('✅ Base de données initialisée avec succès');
  }
  return databaseInstance;
};

// Fonction pour réinitialiser la base de données (utile pour les tests)
export const resetDatabase = (): void => {
  if (databaseInstance) {
    databaseInstance.close();
    databaseInstance = null;
  }
};

export default DatabaseService;

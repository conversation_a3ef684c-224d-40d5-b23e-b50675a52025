import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { User, AuthState, LoginCredentials, CreateUserData, AuditLog } from '../types/auth';
import '../types/electron'; // Import global type declarations

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  register: (userData: CreateUserData) => Promise<boolean>;
  createUser: (userData: CreateUserData) => Promise<boolean>;
  updateUser: (userId: number, updates: Partial<User>) => Promise<boolean>;
  deleteUser: (userId: number) => Promise<boolean>;
  resetPassword: (userId: number, newPassword: string) => Promise<boolean>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<boolean>;
  getAllUsers: () => Promise<User[]>;
  getAuditLogs: () => Promise<AuditLog[]>;
  logAction: (action: string, resource: string, resourceId?: number, details?: string) => void;
  checkFirstTimeSetup: () => Promise<boolean>;
  setupFirstSuperAdmin: (userData: CreateUserData) => Promise<boolean>;
}

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true };
    case 'LOGIN_SUCCESS':
      return { user: action.payload, isAuthenticated: true, isLoading: false };
    case 'LOGIN_FAILURE':
      return { user: null, isAuthenticated: false, isLoading: false };
    case 'LOGOUT':
      return { user: null, isAuthenticated: false, isLoading: false };
    case 'UPDATE_USER':
      return { ...state, user: action.payload };
    default:
      return state;
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Note: Mock data removed - now using database for all authentication

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    isAuthenticated: false,
    isLoading: false,
  });

  // Check if we're in Electron environment
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  // Auto-logout after 30 minutes of inactivity
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    const resetTimeout = () => {
      clearTimeout(timeoutId);
      if (state.isAuthenticated) {
        timeoutId = setTimeout(() => {
          logout();
          alert('Session expirée. Veuillez vous reconnecter.');
        }, 30 * 60 * 1000); // 30 minutes
      }
    };

    const handleActivity = () => resetTimeout();

    if (state.isAuthenticated) {
      resetTimeout();
      window.addEventListener('mousedown', handleActivity);
      window.addEventListener('keydown', handleActivity);
    }

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('mousedown', handleActivity);
      window.removeEventListener('keydown', handleActivity);
    };
  }, [state.isAuthenticated]);

  // Check for existing session on app load
  useEffect(() => {
    const savedUser = localStorage.getItem('bibliotech_user');
    if (savedUser && isElectron) {
      try {
        const user = JSON.parse(savedUser);
        // Verify user still exists in database
        window.electronAPI.database.getUserByUsername(user.username).then(dbUser => {
          if (dbUser && dbUser.isActive) {
            dispatch({ type: 'LOGIN_SUCCESS', payload: user });
          } else {
            localStorage.removeItem('bibliotech_user');
          }
        }).catch(() => {
          localStorage.removeItem('bibliotech_user');
        });
      } catch (error) {
        localStorage.removeItem('bibliotech_user');
      }
    }
  }, [isElectron]);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    if (!isElectron) {
      console.error('❌ Not running in Electron environment');
      dispatch({ type: 'LOGIN_FAILURE' });
      return false;
    }

    console.log('🔐 Starting login process for:', credentials.username);
    dispatch({ type: 'LOGIN_START' });

    try {
      // Check if electronAPI is available
      if (!window.electronAPI || !window.electronAPI.database) {
        console.error('❌ Electron API not available');
        dispatch({ type: 'LOGIN_FAILURE' });
        return false;
      }

      // Use the database service to authenticate the user properly
      // This will handle password verification correctly
      console.log('🔍 Calling authenticateUser...');
      const authenticatedUser = await window.electronAPI.database.authenticateUser(credentials);

      if (!authenticatedUser) {
        console.log('❌ Authentication failed - no user returned');
        dispatch({ type: 'LOGIN_FAILURE' });
        return false;
      }

      console.log('✅ Authentication successful for:', authenticatedUser.username);

      // Store user session
      localStorage.setItem('bibliotech_user', JSON.stringify(authenticatedUser));
      dispatch({ type: 'LOGIN_SUCCESS', payload: authenticatedUser });

      // Log the successful login action
      try {
        await window.electronAPI.database.addAuditLog(
          authenticatedUser.id,
          authenticatedUser.username,
          'LOGIN',
          'AUTH',
          undefined,
          'Connexion réussie'
        );
      } catch (auditError) {
        console.warn('⚠️ Failed to log audit entry:', auditError);
        // Don't fail login for audit log issues
      }

      return true;
    } catch (error) {
      console.error('❌ Login error:', error);
      if (error instanceof Error) {
        console.error('❌ Error stack:', error.stack);
      }
      dispatch({ type: 'LOGIN_FAILURE' });
      return false;
    }
  };

  const logout = () => {
    if (state.user && isElectron) {
      window.electronAPI.database.addAuditLog(state.user.id, state.user.username, 'LOGOUT', 'AUTH', undefined, 'Déconnexion');
    }
    localStorage.removeItem('bibliotech_user');
    dispatch({ type: 'LOGOUT' });
  };

  const register = async (userData: CreateUserData): Promise<boolean> => {
    if (!isElectron) {
      return false;
    }

    try {
      // Use the database service to create user with proper password hashing
      const newUser = await window.electronAPI.database.createUser(userData);

      // Log the registration
      await window.electronAPI.database.addAuditLog(
        newUser.id,
        newUser.username,
        'REGISTER',
        'AUTH',
        newUser.id,
        `Nouvel utilisateur enregistré: ${userData.username}`
      );
      return true;
    } catch (error) {
      console.error('Error registering user:', error);
      return false;
    }
  };

  const createUser = async (userData: CreateUserData): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin' || !isElectron) {
      return false;
    }

    try {
      // Use the database service to create user with proper password hashing
      const newUser = await window.electronAPI.database.createUser(userData);

      await window.electronAPI.database.addAuditLog(
        state.user.id,
        state.user.username,
        'CREATE',
        'USER',
        newUser.id,
        `Utilisateur créé: ${userData.username}`
      );
      return true;
    } catch (error) {
      console.error('Error creating user:', error);
      return false;
    }
  };

  const updateUser = async (userId: number, updates: Partial<User>): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin' || !isElectron) {
      return false;
    }

    try {
      const success = await window.electronAPI.database.updateUser(userId, updates);
      if (success) {
        await window.electronAPI.database.addAuditLog(state.user.id, state.user.username, 'UPDATE', 'USER', userId, 'Utilisateur modifié');
      }
      return success;
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  };

  const deleteUser = async (userId: number): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin' || !isElectron) {
      return false;
    }

    try {
      const users = await window.electronAPI.database.getAllUsers();
      const user = users.find(u => u.id === userId);
      if (!user) return false;

      const success = await window.electronAPI.database.deleteUser(userId);
      if (success) {
        await window.electronAPI.database.addAuditLog(state.user.id, state.user.username, 'DELETE', 'USER', userId, `Utilisateur supprimé: ${user.username}`);
      }
      return success;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  };

  const resetPassword = async (userId: number, newPassword: string): Promise<boolean> => {
    if (!state.user || state.user.role !== 'super_admin' || !isElectron) {
      return false;
    }

    try {
      const users = await window.electronAPI.database.getAllUsers();
      const user = users.find(u => u.id === userId);
      if (!user) return false;

      const success = await window.electronAPI.database.updateUserPassword(userId, newPassword);
      if (success) {
        await window.electronAPI.database.addAuditLog(state.user.id, state.user.username, 'RESET_PASSWORD', 'USER', userId, `Mot de passe réinitialisé pour: ${user.username}`);
      }
      return success;
    } catch (error) {
      console.error('Error resetting password:', error);
      return false;
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<boolean> => {
    if (!state.user || !isElectron) {
      return false;
    }

    try {
      // First verify the current password by attempting to authenticate
      const authResult = await window.electronAPI.database.authenticateUser({
        username: state.user.username,
        password: currentPassword
      });

      if (!authResult) {
        console.log('Current password verification failed');
        return false;
      }

      // If authentication successful, update the password
      const success = await window.electronAPI.database.updateUserPassword(state.user.id, newPassword);
      if (success) {
        await window.electronAPI.database.addAuditLog(
          state.user.id,
          state.user.username,
          'CHANGE_PASSWORD',
          'USER',
          state.user.id,
          'Mot de passe modifié par l\'utilisateur'
        );
      }
      return success;
    } catch (error) {
      console.error('Error changing password:', error);
      return false;
    }
  };

  const getAllUsers = async (): Promise<User[]> => {
    if (!state.user || state.user.role !== 'super_admin' || !isElectron) {
      return [];
    }

    try {
      return await window.electronAPI.database.getAllUsers();
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  };

  const getAuditLogs = async (): Promise<AuditLog[]> => {
    // ✅ Allow both super_admin and administrator roles to view audit logs
    if (!state.user || (state.user.role !== 'super_admin' && state.user.role !== 'administrator') || !isElectron) {
      console.log('❌ getAuditLogs: Access denied for user:', state.user?.username, 'Role:', state.user?.role);
      return [];
    }

    try {
      console.log('✅ getAuditLogs: Fetching audit logs for user:', state.user.username, 'Role:', state.user.role);
      return await window.electronAPI.database.getAuditLogs();
    } catch (error) {
      console.error('Error getting audit logs:', error);
      return [];
    }
  };

  const logAction = async (action: string, resource: string, resourceId?: number, details?: string) => {
    if (!state.user || !isElectron) return;

    try {
      await window.electronAPI.database.addAuditLog(state.user.id, state.user.username, action, resource, resourceId, details);
    } catch (error) {
      console.error('Error logging action:', error);
    }
  };

  // Check if this is the first time setup (no users exist)
  const checkFirstTimeSetup = async (): Promise<boolean> => {
    if (!isElectron) return false;

    try {
      const users = await window.electronAPI.database.getAllUsers();
      return users.length === 0;
    } catch (error) {
      console.error('Error checking first time setup:', error);
      return false;
    }
  };

  // Setup the first Super Admin account
  const setupFirstSuperAdmin = async (userData: CreateUserData): Promise<boolean> => {
    if (!isElectron) return false;

    try {
      // Ensure this is a super_admin role
      const superAdminData = { ...userData, role: 'super_admin' as const };

      // Create the user
      const newUser = await window.electronAPI.database.createUser(superAdminData);

      // Log the setup action
      await window.electronAPI.database.addAuditLog(
        newUser.id,
        newUser.username,
        'SETUP',
        'AUTH',
        newUser.id,
        'Configuration initiale - Premier Super Admin créé'
      );

      return true;
    } catch (error) {
      console.error('Error setting up first super admin:', error);
      return false;
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    register,
    createUser,
    updateUser,
    deleteUser,
    resetPassword,
    changePassword,
    getAllUsers,
    getAuditLogs,
    logAction,
    checkFirstTimeSetup,
    setupFirstSuperAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

# Guide de Test Complet - BiblioTech

## 🧪 Plan de Test pour Vérifier l'Intégration de la Base de Données

### Phase 1: Tests de Base - Démarrage et Initialisation

#### Test 1.1: Démarrage de l'Application
```bash
# Dans le terminal, depuis le dossier du projet
npm run build
npx electron .
```

**✅ Critères de Réussite:**
- L'application se lance sans erreurs
- L'écran de chargement "Initialisation de BiblioTech" apparaît
- L'application charge les données et affiche le tableau de bord
- Aucune erreur de base de données dans la console

#### Test 1.2: Vérification de la Base de Données
**Localisation attendue:**
- **Développement:** `./data/bibliotech.db`
- **Production:** `%APPDATA%/BiblioTech/bibliotech.db`

**✅ Critères de Réussite:**
- Le fichier `bibliotech.db` est créé automatiquement
- La taille du fichier est > 0 bytes
- Les données d'exemple sont visibles dans l'interface

### Phase 2: Tests Fonctionnels - CRUD Operations

#### Test 2.1: Gestion des Livres

**Ajouter un Livre:**
1. Aller à "Gestion des Livres"
2. Cliquer "Ajouter un Livre"
3. Remplir les champs:
   - Titre: "Test Livre Database"
   - Auteur: "Testeur BiblioTech"
   - Catégorie: "Fiction"
   - Genre: "Test"
   - ISBN: "9781234567890"
   - Année: 2025
   - Description: "Livre de test pour vérifier la base de données"
4. Sauvegarder

**✅ Critères de Réussite:**
- Le livre apparaît immédiatement dans la liste
- Message de succès affiché
- Le livre persiste après redémarrage de l'application

**Modifier un Livre:**
1. Cliquer sur "Modifier" pour le livre de test
2. Changer le titre en "Test Livre Database - Modifié"
3. Sauvegarder

**✅ Critères de Réussite:**
- Les modifications sont visibles immédiatement
- Les changements persistent après redémarrage

**Supprimer un Livre:**
1. Cliquer sur "Supprimer" pour le livre de test
2. Confirmer la suppression

**✅ Critères de Réussite:**
- Le livre disparaît de la liste
- La suppression persiste après redémarrage

#### Test 2.2: Gestion des Catégories

**Ajouter une Catégorie:**
1. Aller à "Gestion des Catégories"
2. Cliquer "Ajouter une Catégorie"
3. Nom: "Test Catégorie"
4. Description: "Catégorie de test pour la base de données"
5. Sauvegarder

**✅ Critères de Réussite:**
- La catégorie apparaît dans la liste
- Elle est disponible lors de l'ajout de livres
- Persiste après redémarrage

#### Test 2.3: Gestion des Membres

**Ajouter un Membre:**
1. Aller à "Gestion des Membres"
2. Cliquer "Ajouter un Membre"
3. Remplir:
   - Nom: "Test"
   - Prénom: "Utilisateur"
   - Fonction: "Testeur de Base de Données"
   - Téléphone: "+243 999 888 777"
   - Adresse: "123 Rue de Test, Kinshasa"
   - Email: "<EMAIL>"
4. Sauvegarder

**✅ Critères de Réussite:**
- Le membre apparaît dans la liste
- Un numéro de membre est généré automatiquement
- Le profil est accessible depuis "Profils Utilisateurs"

### Phase 3: Tests de Prêts et Retours

#### Test 3.1: Créer un Prêt
1. Aller à "Gestion des Livres"
2. Trouver un livre disponible
3. Cliquer "Assigner"
4. Sélectionner le membre de test
5. Définir les dates de prêt
6. Confirmer

**✅ Critères de Réussite:**
- Le livre devient "Non disponible"
- Le prêt apparaît dans "Suivi des Prêts"
- Le livre apparaît dans le profil du membre

#### Test 3.2: Retourner un Livre
1. Aller à "Suivi des Prêts"
2. Trouver le prêt de test
3. Cliquer "Retourner"
4. Confirmer

**✅ Critères de Réussite:**
- Le livre redevient "Disponible"
- Le prêt disparaît de la liste active
- Le livre disparaît du profil du membre

#### Test 3.3: Prolonger un Prêt
1. Créer un nouveau prêt
2. Aller à "Suivi des Prêts"
3. Cliquer "Prolonger"
4. Choisir une nouvelle date
5. Confirmer

**✅ Critères de Réussite:**
- La nouvelle date de retour est mise à jour
- Le changement est visible partout dans l'application

### Phase 4: Tests de Performance et Données

#### Test 4.1: Recherche et Filtrage
1. Aller à "Gestion des Livres"
2. Tester la recherche par titre
3. Tester la recherche par auteur
4. Tester les filtres par catégorie
5. Tester les filtres par disponibilité

**✅ Critères de Réussite:**
- Les résultats apparaissent instantanément
- Les filtres fonctionnent correctement
- Aucun délai perceptible même avec tous les livres

#### Test 4.2: Statistiques du Tableau de Bord
1. Aller au "Tableau de Bord"
2. Vérifier les statistiques:
   - Nombre total de livres
   - Nombre de membres
   - Prêts actifs
   - Livres en retard

**✅ Critères de Réussite:**
- Les chiffres correspondent aux données réelles
- Les statistiques se mettent à jour en temps réel
- Les cartes cliquables fonctionnent

### Phase 5: Tests de Persistance

#### Test 5.1: Redémarrage de l'Application
1. Fermer complètement l'application
2. Relancer l'application
3. Vérifier que toutes les données sont présentes

**✅ Critères de Réussite:**
- Toutes les données ajoutées sont toujours là
- L'état des prêts est conservé
- Les modifications sont persistantes

#### Test 5.2: Test de l'Exécutable Packagé
```bash
# Créer l'exécutable
npx electron-packager . BiblioTech --platform=win32 --arch=x64 --out=dist-electron --overwrite

# Lancer l'exécutable
./dist-electron/BiblioTech-win32-x64/BiblioTech.exe
```

**✅ Critères de Réussite:**
- L'exécutable se lance sans erreurs
- Toutes les fonctionnalités marchent identiquement
- La base de données est créée dans le bon dossier utilisateur

### Phase 6: Tests d'Erreurs et Récupération

#### Test 6.1: Gestion des Erreurs
1. Essayer de supprimer un livre actuellement emprunté
2. Essayer de supprimer un membre avec des prêts actifs
3. Essayer d'ajouter un livre avec des données manquantes

**✅ Critères de Réussite:**
- Messages d'erreur appropriés affichés
- L'application ne plante pas
- Les données restent cohérentes

#### Test 6.2: Test de Récupération
1. Fermer brutalement l'application pendant une opération
2. Relancer l'application
3. Vérifier l'intégrité des données

**✅ Critères de Réussite:**
- L'application redémarre normalement
- Aucune corruption de données
- Toutes les données sont cohérentes

### 🎯 Checklist Final de Validation

**Base de Données:**
- [ ] Fichier SQLite créé automatiquement
- [ ] Données d'exemple chargées
- [ ] Toutes les tables créées avec les bons schémas
- [ ] Index et contraintes fonctionnels

**Fonctionnalités CRUD:**
- [ ] Ajout de livres fonctionne
- [ ] Modification de livres fonctionne
- [ ] Suppression de livres fonctionne
- [ ] Gestion des catégories complète
- [ ] Gestion des membres complète

**Système de Prêts:**
- [ ] Création de prêts fonctionne
- [ ] Retour de livres fonctionne
- [ ] Prolongation de prêts fonctionne
- [ ] Détection des retards automatique

**Performance:**
- [ ] Recherche instantanée
- [ ] Filtrage rapide
- [ ] Interface réactive
- [ ] Pas de délais perceptibles

**Distribution:**
- [ ] Exécutable créé avec succès
- [ ] Application autonome (aucune dépendance)
- [ ] Fonctionne sur machine propre
- [ ] Base de données portable

**Persistance:**
- [ ] Données sauvegardées automatiquement
- [ ] Redémarrage sans perte de données
- [ ] Cohérence après fermeture brutale

---

## 🚀 Instructions d'Exécution des Tests

1. **Ouvrir un terminal** dans le dossier du projet
2. **Exécuter chaque phase** dans l'ordre
3. **Cocher chaque test** réussi
4. **Noter toute anomalie** pour correction
5. **Valider l'exécutable final** sur une machine propre

Si tous les tests passent, **BiblioTech est prêt pour la production !** 🎉

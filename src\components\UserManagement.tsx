import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getPermissions } from '../types/auth';
import { PlusIcon, EditIcon, TrashIcon, CheckIcon, XIcon, UsersIcon, EyeIcon, EyeOffIcon } from './Icons';
import type { User, CreateUserData } from '../types/auth';
import ModalPortal from './ModalPortal';
import ConfirmationModal from './ConfirmationModal';

const UserManagement: React.FC = () => {
  const { user: currentUser, getAllUsers, createUser, updateUser, deleteUser, resetPassword } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<CreateUserData>({
    username: '',
    email: '',
    password: '',
    role: 'administrator'
  });
  const [editData, setEditData] = useState({ email: '', isActive: true });
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Confirmation modal state
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    user: User | null;
  }>({
    isOpen: false,
    user: null
  });

  const permissions = currentUser ? getPermissions(currentUser.role) : null;

  // Load users
  useEffect(() => {
    const loadUsers = async () => {
      if (permissions?.canManageAdministrators) {
        try {
          setIsLoading(true);
          const usersData = await getAllUsers();
          setUsers(usersData);
        } catch (error) {
          console.error('Error loading users:', error);
          setUsers([]);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadUsers();
  }, [getAllUsers, permissions?.canManageAdministrators]);

  if (!permissions?.canManageAdministrators) {
    return (
      <div className="card">
        <div className="card-content text-center py-8">
          <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
          <p className="text-neutral">Vous n'avez pas les permissions pour gérer les utilisateurs.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="card">
        <div className="card-content text-center py-8">
          <div className="loading loading-spinner loading-lg"></div>
          <p className="text-neutral mt-4">Chargement des utilisateurs...</p>
        </div>
      </div>
    );
  }

  // Form validation function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Username validation
    if (!newUser.username.trim()) {
      errors.username = 'Le nom d\'utilisateur est requis';
    } else if (newUser.username.length < 3) {
      errors.username = 'Le nom d\'utilisateur doit contenir au moins 3 caractères';
    } else if (!/^[a-zA-Z0-9_]+$/.test(newUser.username)) {
      errors.username = 'Le nom d\'utilisateur ne peut contenir que des lettres, chiffres et underscores';
    }

    // Email validation
    if (!newUser.email.trim()) {
      errors.email = 'L\'email est requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) {
      errors.email = 'Format d\'email invalide';
    }

    // Password validation
    if (!newUser.password.trim()) {
      errors.password = 'Le mot de passe est requis';
    } else if (newUser.password.length < 8) {
      errors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newUser.password)) {
      errors.password = 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateUser = async () => {
    if (!validateForm()) {
      return;
    }

    const success = await createUser(newUser);
    if (success) {
      setNewUser({ username: '', email: '', password: '', role: 'administrator' });
      setIsCreating(false);
      setShowPassword(false);
      setFormErrors({});
      // Refresh users list
      const usersData = await getAllUsers();
      setUsers(usersData);
    } else {
      setFormErrors({ general: 'Erreur lors de la création de l\'utilisateur. Nom d\'utilisateur ou email déjà utilisé.' });
    }
  };

  const handleUpdateUser = async (userId: number) => {
    if (!editData.email.trim()) {
      alert('L\'email est requis');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editData.email)) {
      alert('Format d\'email invalide');
      return;
    }

    const success = await updateUser(userId, {
      email: editData.email.trim(),
      isActive: editData.isActive
    });

    if (success) {
      setEditingUser(null);
      setEditData({ email: '', isActive: true });
      // Refresh users list
      const usersData = await getAllUsers();
      setUsers(usersData);
    } else {
      alert('Erreur lors de la modification de l\'utilisateur');
    }
  };

  const handleDeleteUserClick = (user: User) => {
    if (user.id === currentUser?.id) {
      alert('Vous ne pouvez pas supprimer votre propre compte');
      return;
    }

    setConfirmationModal({
      isOpen: true,
      user
    });
  };

  const handleConfirmDeleteUser = async () => {
    if (confirmationModal.user) {
      const success = await deleteUser(confirmationModal.user.id);
      if (success) {
        // Refresh users list
        const usersData = await getAllUsers();
        setUsers(usersData);
      } else {
        alert('Erreur lors de la suppression de l\'utilisateur');
      }
    }
    setConfirmationModal({
      isOpen: false,
      user: null
    });
  };

  const handleCloseConfirmationModal = () => {
    setConfirmationModal({
      isOpen: false,
      user: null
    });
  };

  const handleResetPassword = async () => {
    if (!resetPasswordUser || !newPassword.trim()) {
      alert('Veuillez saisir un nouveau mot de passe');
      return;
    }

    if (newPassword.length < 8) {
      alert('Le mot de passe doit contenir au moins 8 caractères');
      return;
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
      alert('Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre');
      return;
    }

    const success = await resetPassword(resetPasswordUser.id, newPassword);
    if (success) {
      setResetPasswordUser(null);
      setNewPassword('');
      setShowNewPassword(false);
      alert('Mot de passe réinitialisé avec succès');
    } else {
      alert('Erreur lors de la réinitialisation du mot de passe');
    }
  };

  const startEdit = (user: User) => {
    setEditingUser(user);
    setEditData({ email: user.email, isActive: user.isActive });
  };

  const cancelEdit = () => {
    setEditingUser(null);
    setEditData({ email: '', isActive: true });
  };

  return (
    <div>
      <div className="card">
        <div className="card-header">
          {/* Header section with title and button */}
          <div className="flex justify-between items-start mb-4">
            <div className="flex-1 mr-6">
              <h2 className="card-title flex items-center gap-2">
                <UsersIcon size={24} />
                Gestion des utilisateurs
              </h2>
              <p className="card-subtitle">Gérez les comptes administrateurs</p>
            </div>
            <button
              onClick={() => setIsCreating(true)}
              className="btn btn-primary flex-shrink-0"
              disabled={isCreating}
            >
              <PlusIcon size={16} />
              Nouvel administrateur
            </button>
          </div>

          {/* Informational note section - separate from header */}
          <div className="alert alert-info">
            <p className="text-sm">
              ℹ️ <strong>Note:</strong> Les comptes membres sont automatiquement créés lors de l'ajout d'un nouveau membre dans la section "Gestion des membres".
            </p>
          </div>
        </div>

        {/* Create new user form - positioned right after header */}
        {isCreating && (
          <div className="card mb-6" style={{ border: '2px solid var(--color-primary-200)' }}>
            <div className="card-header">
              <h3 className="card-title">Créer un nouvel administrateur</h3>
            </div>
            <div className="card-content">
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Nom d'utilisateur *</label>
                  <input
                    type="text"
                    className={`form-input ${formErrors.username ? 'border-red-300 bg-red-50' : ''}`}
                    value={newUser.username}
                    onChange={(e) => {
                      setNewUser(prev => ({ ...prev, username: e.target.value }));
                      if (formErrors.username) {
                        setFormErrors(prev => ({ ...prev, username: '' }));
                      }
                    }}
                    placeholder="nom_utilisateur"
                    autoFocus
                  />
                  {formErrors.username && (
                    <p className="text-red-600 text-sm mt-1">{formErrors.username}</p>
                  )}
                </div>
                <div className="form-group">
                  <label className="form-label">Email *</label>
                  <input
                    type="email"
                    className={`form-input ${formErrors.email ? 'border-red-300 bg-red-50' : ''}`}
                    value={newUser.email}
                    onChange={(e) => {
                      setNewUser(prev => ({ ...prev, email: e.target.value }));
                      if (formErrors.email) {
                        setFormErrors(prev => ({ ...prev, email: '' }));
                      }
                    }}
                    placeholder="<EMAIL>"
                  />
                  {formErrors.email && (
                    <p className="text-red-600 text-sm mt-1">{formErrors.email}</p>
                  )}
                </div>
              </div>
              {/* General Error */}
              {formErrors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <p className="text-red-600 text-sm">{formErrors.general}</p>
                </div>
              )}

              <div className="form-group">
                <label className="form-label">Mot de passe *</label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    className={`form-input pr-12 ${formErrors.password ? 'border-red-300 bg-red-50' : ''}`}
                    value={newUser.password}
                    onChange={(e) => {
                      setNewUser(prev => ({ ...prev, password: e.target.value }));
                      if (formErrors.password) {
                        setFormErrors(prev => ({ ...prev, password: '' }));
                      }
                    }}
                    placeholder="Minimum 8 caractères"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showPassword ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
                  </button>
                </div>
                {formErrors.password && (
                  <p className="text-red-600 text-sm mt-1">{formErrors.password}</p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Minimum 8 caractères avec au moins une minuscule, une majuscule et un chiffre
                </p>
              </div>

              {/* Role Information */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div className="flex items-start">
                  <UsersIcon size={18} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-blue-800 font-medium">Rôle: Administrateur</p>
                    <p className="text-xs text-blue-700 mt-1">
                      Ce compte aura les privilèges d'administrateur pour gérer les livres, membres et emprunts.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex gap-3 justify-end mt-6 pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    setIsCreating(false);
                    setNewUser({ username: '', email: '', password: '', role: 'administrator' });
                    setShowPassword(false);
                    setFormErrors({});
                  }}
                  className="btn btn-secondary"
                >
                  <XIcon size={16} className="mr-1" />
                  Annuler
                </button>
                <button
                  onClick={handleCreateUser}
                  className="btn btn-primary"
                  disabled={!newUser.username.trim() || !newUser.email.trim() || !newUser.password.trim()}
                >
                  <CheckIcon size={16} className="mr-1" />
                  Créer l'administrateur
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Icon Legend */}
        <div className="legend-section">
          <h3 className="legend-title">Guide des icônes d'action</h3>
          <div className="icon-legend">
            <div className="legend-item">
              <div className="legend-icon legend-icon--success">
                <CheckIcon size={16} />
              </div>
              <span className="legend-text">Sauvegarder</span>
            </div>
            <div className="legend-item">
              <div className="legend-icon legend-icon--secondary">
                <XIcon size={16} />
              </div>
              <span className="legend-text">Annuler</span>
            </div>
            <div className="legend-item">
              <div className="legend-icon legend-icon--secondary">
                <EditIcon size={16} />
              </div>
              <span className="legend-text">Modifier</span>
            </div>
            <div className="legend-item">
              <div className="legend-icon legend-icon--warning">
                🔑
              </div>
              <span className="legend-text">Réinitialiser mot de passe</span>
            </div>
            <div className="legend-item">
              <div className="legend-icon legend-icon--danger">
                <TrashIcon size={16} />
              </div>
              <span className="legend-text">Supprimer</span>
            </div>
          </div>
        </div>

        <div className="card-content">

          {/* Users table */}
          <div className="table-container">
            <div className="table-responsive">
              <table className="table table-mobile-stack user-management-table">
                <thead>
                  <tr>
                    <th>Utilisateur</th>
                    <th>Email</th>
                    <th>Rôle</th>
                    <th>Statut</th>
                    <th className="hide-mobile">Dernière connexion</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td data-label="Utilisateur">
                        <div className="font-medium">{user.username}</div>
                        <div className="text-sm text-neutral">ID: {user.id}</div>
                      </td>
                      <td data-label="Email">
                        {user.email}
                      </td>
                      <td data-label="Rôle">
                        <span className={`badge ${user.role === 'super_admin' ? 'badge-error' : 'badge-neutral'}`}>
                          {user.role === 'super_admin' ? 'SUPER ADMIN' : 'ADMINISTRATEUR'}
                        </span>
                      </td>
                      <td data-label="Statut">
                        <span className={`badge ${user.isActive ? 'badge-success' : 'badge-warning'}`}>
                          {user.isActive ? 'ACTIF' : 'INACTIF'}
                        </span>
                      </td>
                      <td data-label="Dernière connexion" className="hide-mobile">
                        {user.lastLogin
                          ? new Date(user.lastLogin).toLocaleDateString('fr-FR', {
                              day: '2-digit',
                              month: '2-digit',
                              year: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })
                          : 'Jamais'
                        }
                      </td>
                      <td data-label="Actions">
                        <div className="action-buttons">
                          <button
                            onClick={() => startEdit(user)}
                            className="action-btn action-btn--secondary action-btn--sm"
                            disabled={user.id === currentUser?.id}
                          >
                            <EditIcon size={16} />
                          </button>
                          <button
                            onClick={() => setResetPasswordUser(user)}
                            className="action-btn action-btn--warning action-btn--sm"
                          >
                            🔑
                          </button>
                          <button
                            onClick={() => handleDeleteUserClick(user)}
                            className="action-btn action-btn--danger action-btn--sm"
                            disabled={user.id === currentUser?.id || user.role === 'super_admin'}
                          >
                            <TrashIcon size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {users.length === 0 && (
            <div className="text-center py-8 text-neutral">
              <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
              <p>Aucun utilisateur trouvé.</p>
            </div>
          )}
        </div>
      </div>

      {/* Reset Password Modal */}
      <ModalPortal
        isOpen={!!resetPasswordUser}
        onClose={() => {
          setResetPasswordUser(null);
          setNewPassword('');
        }}
      >
        {resetPasswordUser && (
          <div className="modal-container" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2 className="modal-title">Réinitialiser le mot de passe</h2>
              <p className="modal-subtitle">
                Utilisateur: {resetPasswordUser.username}
              </p>
              <button
                className="modal-close"
                onClick={() => {
                  setResetPasswordUser(null);
                  setNewPassword('');
                }}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label className="form-label">Nouveau mot de passe *</label>
                <div className="relative">
                  <input
                    type={showNewPassword ? 'text' : 'password'}
                    className="form-input pr-12"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Minimum 8 caractères avec majuscule, minuscule et chiffre"
                    autoFocus
                    style={{ fontSize: '16px' }}
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {showNewPassword ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
                  </button>
                </div>
                {newPassword && newPassword.length < 8 && (
                  <p className="text-red-600 text-sm mt-1">Le mot de passe doit contenir au moins 8 caractères</p>
                )}
                {newPassword && newPassword.length >= 8 && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword) && (
                  <p className="text-red-600 text-sm mt-1">Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre</p>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button
                onClick={() => {
                  setResetPasswordUser(null);
                  setNewPassword('');
                }}
                className="btn btn-secondary"
              >
                Annuler
              </button>
              <button
                onClick={handleResetPassword}
                className="btn btn-primary"
                disabled={!newPassword.trim() || newPassword.length < 8 || !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)}
              >
                Réinitialiser
              </button>
            </div>
          </div>
        )}
      </ModalPortal>

      {/* Edit User Modal */}
      <ModalPortal isOpen={!!editingUser} onClose={cancelEdit}>
        {editingUser && (
          <div className="modal-container" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2 className="modal-title">Modifier l'utilisateur</h2>
              <p className="modal-subtitle">
                Utilisateur: {editingUser.username} (ID: {editingUser.id})
              </p>
              <button
                className="modal-close"
                onClick={() => cancelEdit()}
              >
                ×
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label className="form-label">Email *</label>
                <input
                  type="email"
                  className="form-input"
                  value={editData.email}
                  onChange={(e) => setEditData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Adresse email"
                  autoFocus
                />
              </div>
              <div className="form-group">
                <label className="form-label">Statut</label>
                <select
                  className="form-select"
                  value={editData.isActive ? 'active' : 'inactive'}
                  onChange={(e) => setEditData(prev => ({ ...prev, isActive: e.target.value === 'active' }))}
                >
                  <option value="active">Actif</option>
                  <option value="inactive">Inactif</option>
                </select>
              </div>
              <div className="form-group">
                <label className="form-label">Rôle</label>
                <div className="form-input-readonly">
                  {editingUser.role === 'super_admin' ? 'SUPER ADMIN' : 'ADMINISTRATEUR'}
                </div>
                <p className="form-help">Le rôle ne peut pas être modifié</p>
              </div>
            </div>
            <div className="modal-footer">
              <button
                onClick={() => cancelEdit()}
                className="btn btn-secondary"
              >
                Annuler
              </button>
              <button
                onClick={() => handleUpdateUser(editingUser.id)}
                className="btn btn-primary"
                disabled={!editData.email.trim()}
              >
                Sauvegarder
              </button>
            </div>
          </div>
        )}
      </ModalPortal>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={handleCloseConfirmationModal}
        onConfirm={handleConfirmDeleteUser}
        title="Supprimer l'utilisateur"
        message={
          confirmationModal.user
            ? `Êtes-vous sûr de vouloir supprimer l'utilisateur "${confirmationModal.user.username}" ? Cette action est irréversible et supprimera définitivement le compte administrateur.`
            : ''
        }
        confirmText="Supprimer"
        variant="danger"
      />
    </div>
  );
};

export default UserManagement;

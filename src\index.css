/* Modern Library Management System - Design System */
:root {
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Colors - Primary Palette */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  /* Colors - Neutral Palette */
  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;

  /* Colors - Semantic */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;

  --color-error-25: #fefcfc;
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Print-specific variables */
  --card-width: 85.6mm;
  --card-height: 53.98mm;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-neutral-900);
  background-color: var(--color-neutral-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--color-neutral-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin: 0;
  line-height: var(--leading-relaxed);
}

/* Links */
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-700);
  text-decoration: underline;
}

/* Focus Styles */
*:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* ===== PRINT STYLES ===== */

/* Print Modal Styles */
.print-modal {
  max-width: 800px;
  width: 90vw;
}

.print-actions {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background-color: var(--color-neutral-100);
  border-radius: var(--radius-md);
  text-align: center;
}

.print-actions .btn {
  margin-bottom: var(--space-2);
}

/* Member Card Styles */
.member-card {
  display: flex;
  justify-content: center;
  padding: var(--space-4);
  background-color: var(--color-neutral-100);
  border-radius: var(--radius-lg);
}

.member-card-front {
  width: 100%;
  max-width: 550px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid var(--color-primary-600);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  font-size: 0.9rem;
}

.member-card-front .card-header {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%) !important;
  color: #ffffff !important;
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-logo {
  width: 55px;
  height: 55px;
  object-fit: contain;
  background-color: #ffffff !important;
  border-radius: var(--radius-lg);
  padding: var(--space-3);
  border: 3px solid #ffffff !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.card-title h3 {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  margin: 0;
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-title p {
  font-size: var(--text-sm);
  margin: 0;
  opacity: 0.95;
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-body {
  padding: var(--space-5);
  display: flex;
  gap: var(--space-5);
}

.member-photo-section {
  flex-shrink: 0;
}

.member-photo {
  width: 90px;
  height: 90px;
  border-radius: var(--radius-lg);
  object-fit: cover;
  border: 3px solid var(--color-primary-200);
  box-shadow: var(--shadow-md);
}

.member-info-section {
  flex: 1;
  min-width: 0;
}

.member-name h4 {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-2);
  padding: var(--space-1) 0;
  border-bottom: 1px solid var(--color-neutral-200);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: var(--font-medium);
  color: var(--color-neutral-600);
  font-size: var(--text-xs);
  flex-shrink: 0;
  min-width: 80px;
}

.detail-value {
  font-weight: var(--font-normal);
  color: var(--color-neutral-900);
  font-size: var(--text-xs);
  text-align: right;
  word-break: break-word;
}

.detail-value.status-active {
  color: var(--color-success-600);
  font-weight: var(--font-semibold);
}

.detail-value.status-inactive {
  color: var(--color-error-600);
  font-weight: var(--font-semibold);
}

.card-footer {
  background-color: var(--color-neutral-50);
  padding: var(--space-3);
  border-top: 1px solid var(--color-neutral-200);
}

.card-footer-text {
  font-size: var(--text-xs);
  color: var(--color-neutral-600);
  text-align: center;
  margin-bottom: var(--space-2);
  line-height: 1.4;
}

.card-date {
  text-align: center;
}

.card-date p {
  font-size: var(--text-xs);
  color: var(--color-neutral-500);
  font-weight: var(--font-medium);
  margin: 0;
}

/* Print Media Queries */
@media print {
  /* Hide everything except the card */
  body * {
    visibility: hidden;
  }

  .member-card,
  .member-card * {
    visibility: visible;
  }

  .print-hide {
    display: none !important;
  }

  /* Reset page margins and setup */
  @page {
    margin: 0.5in;
    size: A4;
  }

  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.3;
  }

  /* Position the card for printing */
  .member-card {
    position: absolute;
    top: 0;
    left: 0;
    width: var(--card-width);
    height: var(--card-height);
    padding: 0;
    background: white !important;
    border-radius: 0;
    transform: scale(1.5);
    transform-origin: top left;
  }

  .member-card-front {
    width: 100%;
    height: 100%;
    max-width: none;
    border: 2px solid var(--color-primary-600);
    border-radius: 8px;
    box-shadow: none;
    font-size: 8pt;
    background: white !important;
  }

  .member-card-front .card-header {
    background: var(--color-primary-600) !important;
    color: #ffffff !important;
    padding: 8px;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .card-logo {
    width: 32px;
    height: 32px;
    padding: 4px;
    background-color: #ffffff !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .card-title h3 {
    font-size: 10pt;
    color: #ffffff !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .card-title p {
    font-size: 7pt;
    color: #ffffff !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .card-body {
    padding: 8px;
    gap: 8px;
  }

  .member-photo {
    width: 55px;
    height: 55px;
    border: 2px solid #0284c7;
  }

  .member-name h4 {
    font-size: 9pt;
    margin-bottom: 4px;
  }

  .detail-row {
    gap: 4px;
    padding: 1px 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .detail-label,
  .detail-value {
    font-size: 6pt;
  }

  .detail-label {
    min-width: 50px;
  }

  .card-footer {
    background-color: #f8fafc !important;
    padding: 6px;
    border-top: 1px solid #e2e8f0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .card-footer-text,
  .card-date p {
    font-size: 5pt;
    margin-bottom: 2px;
  }
}

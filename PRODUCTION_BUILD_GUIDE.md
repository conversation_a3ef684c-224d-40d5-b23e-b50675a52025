# BiblioTech Production Windows Installer Guide

## 🎯 Overview
This guide provides comprehensive instructions for building a production-ready Windows installer (.exe) for the BiblioTech library management system.

## ✅ Current Status
- **Electron Application**: ✅ Fully functional
- **SQLite Database**: ✅ Offline-capable with better-sqlite3
- **Windows Packaging**: ✅ Working with electron-packager
- **NSIS Installer**: ✅ Successfully generating .exe installer
- **French UI**: ✅ Complete localization
- **Member Photos**: ✅ Local storage system

## 🛠️ Build Process

### Prerequisites
- Node.js 18+ installed
- Windows 10/11 (64-bit)
- All dependencies installed (`npm install`)

### Quick Build Commands

#### 1. Clean Build (Recommended)
```bash
npm run clean
npm run build-installer
```

#### 2. Individual Steps
```bash
# Build React application
npm run build

# Package Electron app
npm run pack-win

# Create NSIS installer
npm run build-installer
```

### Build Outputs
- **Packaged App**: `out/BiblioTech-win32-x64/` (Portable version)
- **Installer**: `dist-electron/BiblioTech-Setup-1.0.2.exe` (NSIS installer)

## 📦 What's Included

### Core Application
- Complete Electron runtime (no external Node.js required)
- React frontend with TypeScript
- SQLite database with better-sqlite3 (native module)
- All npm dependencies bundled

### Features Preserved
- ✅ Book management (add, edit, delete, search, categorize)
- ✅ Member management with photo upload/storage
- ✅ Loan tracking and management
- ✅ Dashboard with statistics
- ✅ Category management
- ✅ User administration (Super Admin/Admin roles)
- ✅ Audit logging
- ✅ French language interface
- ✅ Offline operation (no internet required)
- ✅ Default accounts (superadmin/admin123, admin1/admin123)

### File Structure in Installer
```
BiblioTech/
├── BiblioTech.exe (Main executable)
├── resources/
│   ├── app/ (Application files)
│   ├── assets/ (Icons, images)
│   └── usersimages/ (Member photos)
├── node_modules/ (Runtime dependencies)
└── locales/ (Electron localization)
```

## 🔧 Configuration Details

### Package.json Scripts
- `clean`: Remove build artifacts
- `build`: Compile TypeScript and build React app
- `pack-win`: Create portable Windows package
- `build-installer`: Create NSIS installer
- `postinstall`: Rebuild native dependencies

### Electron-Builder Configuration
- **Target**: Windows x64 NSIS installer
- **Output**: `dist-electron/BiblioTech-Setup-1.0.2.exe`
- **Size**: ~320MB (includes full Electron runtime)
- **Installation**: User-selectable directory
- **Shortcuts**: Desktop and Start Menu

## 🚀 Installation Process

### For End Users
1. Download `BiblioTech-Setup-1.0.2.exe`
2. Run installer (Windows may show security warning - click "More info" → "Run anyway")
3. Choose installation directory
4. Complete installation
5. Launch from desktop shortcut

### System Requirements
- Windows 10/11 (64-bit)
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space
- No internet connection required

## 🔐 Security Considerations

### Code Signing
- Currently using unsigned binaries (free approach)
- Windows will show "Unknown publisher" warning
- Users need to click "More info" → "Run anyway"

### To Add Code Signing (Optional)
1. Obtain code signing certificate
2. Add to package.json:
```json
"win": {
  "certificateFile": "path/to/certificate.p12",
  "certificatePassword": "password"
}
```

## 📊 Build Optimization

### File Exclusions
The build process excludes:
- Source TypeScript files (`src/`)
- Development configuration files
- Documentation files
- Demo distribution files
- Test files

### Size Optimization
- Production React build (minified)
- Only runtime dependencies included
- Native modules properly rebuilt for Windows x64

## 🐛 Troubleshooting

### Common Issues

#### 1. "electron.exe not found" Error
**Solution**: Use `npm run pack-win` instead of direct electron-builder

#### 2. Native Module Errors
**Solution**: Run `npx electron-rebuild` before building

#### 3. Icon Loading Errors
**Solution**: Ensure icon files are valid .ico format or remove icon references

#### 4. NSIS Plugin Errors
**Solution**: Remove custom NSIS scripts that require additional plugins

### Build Verification
Test the installer by:
1. Installing on a clean Windows machine
2. Verifying all features work offline
3. Testing database creation and member photo storage
4. Confirming default login accounts work

## 📈 Production Deployment

### Distribution Checklist
- [ ] Test installer on clean Windows 10/11 systems
- [ ] Verify offline functionality
- [ ] Test all core features (books, members, loans)
- [ ] Confirm database initialization
- [ ] Test member photo upload/display
- [ ] Verify audit logging works
- [ ] Test backup/restore functionality

### File Naming Convention
- Development: `BiblioTech-Setup-1.0.2.exe`
- Production: `BiblioTech-Production-Setup-1.0.2.exe`

## 🔄 Version Updates

### To Update Version
1. Update `version` in `package.json`
2. Run build process
3. Test new installer
4. Distribute updated installer

### Automatic Updates
Currently disabled for offline-first approach. To enable:
```json
"publish": {
  "provider": "github",
  "repo": "your-repo"
}
```

## 📞 Support Information

### Default Accounts
- **Super Admin**: username `superadmin`, password `admin123`
- **Admin**: username `admin1`, password `admin123`

### Database Location
- **Development**: `data/bibliotech.db`
- **Production**: `%APPDATA%/BiblioTech/bibliotech.db`

### Member Photos
- **Development**: `public/usersimages/`
- **Production**: `%APPDATA%/BiblioTech/usersimages/`

---

**Built with**: Electron 36.4.0, React 19.1.0, TypeScript 5.8.3, better-sqlite3 11.10.0
**Target Platform**: Windows 10/11 (64-bit)
**License**: Commercial/Proprietary

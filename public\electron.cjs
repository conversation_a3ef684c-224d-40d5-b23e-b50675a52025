const { app, <PERSON><PERSON><PERSON><PERSON><PERSON>ow, <PERSON>u, shell, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// Set up logging to file for debugging
const logFile = path.join(app.getPath('userData'), 'debug.log');
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

console.log = (...args) => {
  const message = args.join(' ');
  originalConsoleLog(...args);
  try {
    fs.appendFileSync(logFile, `[LOG] ${new Date().toISOString()}: ${message}\n`);
  } catch (e) {
    // Ignore file write errors
  }
};

console.error = (...args) => {
  const message = args.join(' ');
  originalConsoleError(...args);
  try {
    fs.appendFileSync(logFile, `[ERROR] ${new Date().toISOString()}: ${message}\n`);
  } catch (e) {
    // Ignore file write errors
  }
};

// Determine if we're in development mode and set up proper paths
const isDev = process.env.NODE_ENV === 'development';

// In packaged app, __dirname will be inside resources/app.asar or resources/app
// We need to find the correct path to index.html
let indexPath;
if (isDev) {
  // Development mode - use local dist folder
  indexPath = path.join(__dirname, '../dist/index.html');
} else {
  // Production mode - index.html should be in the same directory as electron.cjs
  indexPath = path.join(__dirname, 'index.html');
}

console.log('🔍 Environment check:');
console.log('  isDev:', isDev);
console.log('  __dirname:', __dirname);
console.log('  process.resourcesPath:', process.resourcesPath);
console.log('  indexPath:', indexPath);
console.log('  indexPath exists:', fs.existsSync(indexPath));

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    title: 'BiblioTech', // Set window title explicitly
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, 'assets', 'icon.ico'), // Use .ico file for better Windows compatibility
    show: false, // Don't show until ready
    titleBarStyle: 'default',
    autoHideMenuBar: true // Hide menu bar by default
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3001'
    : `file://${indexPath}`;

  console.log('🚀 Loading application:');
  console.log('  URL:', startUrl);
  console.log('  File exists:', fs.existsSync(indexPath));

  mainWindow.loadURL(startUrl);



  // Handle load errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', validatedURL, errorCode, errorDescription);
  });

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Only open dev tools in development
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Remove menu bar completely for minimal desktop experience
  Menu.setApplicationMenu(null);
}

function createMenu() {
  const template = [
    {
      label: 'Fichier',
      submenu: [
        {
          label: 'Nouveau livre',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-action', 'new-book');
          }
        },
        {
          label: 'Import en lot',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            mainWindow.webContents.send('menu-action', 'bulk-import');
          }
        },
        { type: 'separator' },
        {
          label: 'Exporter les données',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-action', 'export-data');
          }
        },
        { type: 'separator' },
        {
          label: 'Quitter',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Navigation',
      submenu: [
        {
          label: 'Tableau de bord',
          accelerator: 'CmdOrCtrl+1',
          click: () => {
            mainWindow.webContents.send('menu-action', 'navigate-dashboard');
          }
        },
        {
          label: 'Gestion des livres',
          accelerator: 'CmdOrCtrl+2',
          click: () => {
            mainWindow.webContents.send('menu-action', 'navigate-books');
          }
        },
        {
          label: 'Catégories',
          accelerator: 'CmdOrCtrl+3',
          click: () => {
            mainWindow.webContents.send('menu-action', 'navigate-categories');
          }
        },
        {
          label: 'Utilisateurs',
          accelerator: 'CmdOrCtrl+4',
          click: () => {
            mainWindow.webContents.send('menu-action', 'navigate-users');
          }
        },
        {
          label: 'Suivi des prêts',
          accelerator: 'CmdOrCtrl+5',
          click: () => {
            mainWindow.webContents.send('menu-action', 'navigate-loans');
          }
        }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        {
          label: 'Actualiser',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload();
          }
        },
        {
          label: 'Forcer l\'actualisation',
          accelerator: 'CmdOrCtrl+Shift+R',
          click: () => {
            mainWindow.webContents.reloadIgnoringCache();
          }
        },
        { type: 'separator' },
        {
          label: 'Zoom avant',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom + 1);
          }
        },
        {
          label: 'Zoom arrière',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom - 1);
          }
        },
        {
          label: 'Zoom normal',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            mainWindow.webContents.setZoomLevel(0);
          }
        },
        { type: 'separator' },
        {
          label: 'Plein écran',
          accelerator: 'F11',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
          }
        }
      ]
    },
    {
      label: 'Aide',
      submenu: [
        {
          label: 'À propos de BiblioTech',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'À propos de BiblioTech',
              message: 'BiblioTech v1.0.2',
              detail: 'Système de gestion de bibliothèque moderne\n\nDéveloppé avec React et Electron\nCompatible Windows 10/11\n\n© 2025 BiblioTech Team',
              buttons: ['OK']
            });
          }
        },
        {
          label: 'Guide d\'utilisation',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Guide d\'utilisation',
              message: 'Comptes de démonstration',
              detail: 'Super Admin:\nUtilisateur: superadmin\nMot de passe: admin123\n\nAdministrateur:\nUtilisateur: admin1\nMot de passe: admin123\n\nUtilisez la navigation de gauche pour accéder aux différentes fonctionnalités.',
              buttons: ['OK']
            });
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        {
          label: 'À propos de ' + app.getName(),
          role: 'about'
        },
        { type: 'separator' },
        {
          label: 'Services',
          role: 'services',
          submenu: []
        },
        { type: 'separator' },
        {
          label: 'Masquer ' + app.getName(),
          accelerator: 'Command+H',
          role: 'hide'
        },
        {
          label: 'Masquer les autres',
          accelerator: 'Command+Shift+H',
          role: 'hideothers'
        },
        {
          label: 'Tout afficher',
          role: 'unhide'
        },
        { type: 'separator' },
        {
          label: 'Quitter',
          accelerator: 'Command+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers - removed duplicate, handled in database initialization

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// Set app user model ID for Windows
if (process.platform === 'win32') {
  app.setAppUserModelId('com.bibliotech.app');
}

// Database service initialization
let databaseService = null;
let globalUserImagesPath = null;

// Initialize database service with portable mode support
async function initializeDatabaseService() {
  try {
    // Import database modules
    const Database = require('better-sqlite3');

    // Detect if running in portable mode
    const isDev = process.env.NODE_ENV === 'development';
    const isPortable = !isDev && process.resourcesPath &&
      path.dirname(process.execPath) === path.dirname(path.dirname(process.resourcesPath));

    // Configure paths based on mode
    let userDataPath, dbPath, userImagesPath;

    if (isDev) {
      // Development mode - use local data directory
      userDataPath = path.join(__dirname, '..', 'data');
      console.log('🔧 Running in development mode');
    } else if (isPortable) {
      // Portable mode - use directory relative to executable
      userDataPath = path.join(path.dirname(process.execPath), 'BiblioTech-Data');
      console.log('🎒 Running in portable mode');
    } else {
      // Installed mode - use system AppData
      userDataPath = app.getPath('userData');
      console.log('💻 Running in installed mode');
    }

    // Ensure the data directory exists
    if (!fs.existsSync(userDataPath)) {
      fs.mkdirSync(userDataPath, { recursive: true });
      console.log('📁 Created data directory:', userDataPath);
    }

    // Set up paths
    dbPath = path.join(userDataPath, 'bibliotech.db');
    userImagesPath = path.join(userDataPath, 'usersimages');

    // Set global variable for use in other functions
    globalUserImagesPath = userImagesPath;

    // Ensure user images directory exists
    if (!fs.existsSync(userImagesPath)) {
      fs.mkdirSync(userImagesPath, { recursive: true });
      console.log('🖼️ Created user images directory:', userImagesPath);
    }

    // Copy initial profile images from public/usersimages to userData/usersimages
    const publicImagesPath = path.join(__dirname, 'usersimages');
    if (fs.existsSync(publicImagesPath)) {
      console.log('📸 Copying initial profile images...');
      const imageFiles = fs.readdirSync(publicImagesPath);

      imageFiles.forEach(filename => {
        const srcPath = path.join(publicImagesPath, filename);
        const destPath = path.join(userImagesPath, filename);

        // Only copy if destination doesn't exist to avoid overwriting user uploads
        if (!fs.existsSync(destPath)) {
          try {
            fs.copyFileSync(srcPath, destPath);
            console.log(`✅ Copied ${filename} to user images directory`);
          } catch (error) {
            console.error(`❌ Failed to copy ${filename}:`, error);
          }
        }
      });
    }

    console.log('📁 Data directory:', userDataPath);
    console.log('🗃️ Database path:', dbPath);
    console.log('🖼️ User images path:', userImagesPath);

    // Create database instance
    const db = new Database(dbPath);

    // Initialize database schema
    initializeDatabase(db);

    // Migrate initial data if needed
    await migrateInitialDataIfNeeded(db);

    databaseService = db;
    console.log('✅ Database service initialized successfully');

    return true;
  } catch (error) {
    console.error('❌ Failed to initialize database service:', error);
    return false;
  }
}

// Initialize database schema
function initializeDatabase(db) {
  // Enable foreign keys
  db.pragma('foreign_keys = ON');

  // Create tables
  db.exec(`
    CREATE TABLE IF NOT EXISTS categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      nom TEXT NOT NULL UNIQUE,
      description TEXT,
      ordre INTEGER DEFAULT 0,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS livres (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      titre TEXT NOT NULL,
      auteur TEXT NOT NULL,
      categorie TEXT NOT NULL,
      genre TEXT,
      isbn TEXT,
      annee_publication INTEGER,
      description TEXT,
      disponible BOOLEAN NOT NULL DEFAULT 1,
      quantite_totale INTEGER NOT NULL DEFAULT 1,
      quantite_disponible INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (categorie) REFERENCES categories(nom) ON UPDATE CASCADE
    );

    CREATE TABLE IF NOT EXISTS membres (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      nom TEXT NOT NULL,
      prenom TEXT NOT NULL,
      fonction TEXT NOT NULL,
      numero_membre TEXT NOT NULL UNIQUE,
      telephone TEXT NOT NULL,
      adresse TEXT NOT NULL,
      email TEXT,
      photo TEXT,
      date_inscription TEXT NOT NULL,
      is_active BOOLEAN NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS utilisateurs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT NOT NULL UNIQUE,
      email TEXT NOT NULL,
      password_hash TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('super_admin', 'administrator')),
      is_active BOOLEAN NOT NULL DEFAULT 1,
      last_login TEXT,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS emprunts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      livre_id INTEGER NOT NULL,
      membre_id INTEGER NOT NULL,
      date_emprunt TEXT NOT NULL,
      date_retour_prevue TEXT NOT NULL,
      date_retour_effective TEXT,
      statut TEXT NOT NULL DEFAULT 'actif' CHECK (statut IN ('actif', 'retourne', 'en_retard')),
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (livre_id) REFERENCES livres(id) ON DELETE CASCADE,
      FOREIGN KEY (membre_id) REFERENCES membres(id) ON DELETE CASCADE
    );

    CREATE TABLE IF NOT EXISTS journaux_audit (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      username TEXT NOT NULL,
      action TEXT NOT NULL,
      resource TEXT NOT NULL,
      resource_id INTEGER,
      details TEXT NOT NULL,
      timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES utilisateurs(id) ON DELETE CASCADE
    );
  `);

  // Migration: Add quantity columns to existing tables if they don't exist
  try {
    db.exec(`ALTER TABLE livres ADD COLUMN quantite_totale INTEGER NOT NULL DEFAULT 1`);
    db.exec(`ALTER TABLE livres ADD COLUMN quantite_disponible INTEGER NOT NULL DEFAULT 1`);

    // Update existing books: set quantities based on current availability
    db.exec(`
      UPDATE livres
      SET quantite_totale = 1,
          quantite_disponible = CASE WHEN disponible = 1 THEN 1 ELSE 0 END
      WHERE quantite_totale IS NULL OR quantite_disponible IS NULL
    `);
  } catch (error) {
    // Columns already exist, ignore error
  }

  // Create indexes
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_livres_categorie ON livres(categorie);
    CREATE INDEX IF NOT EXISTS idx_livres_disponible ON livres(disponible);
    CREATE INDEX IF NOT EXISTS idx_emprunts_livre ON emprunts(livre_id);
    CREATE INDEX IF NOT EXISTS idx_emprunts_membre ON emprunts(membre_id);
    CREATE INDEX IF NOT EXISTS idx_emprunts_statut ON emprunts(statut);
    CREATE INDEX IF NOT EXISTS idx_membres_numero ON membres(numero_membre);
    CREATE INDEX IF NOT EXISTS idx_audit_user ON journaux_audit(user_id);
    CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON journaux_audit(timestamp);
  `);
}

// Migrate initial data if database is empty or force reset
async function migrateInitialDataIfNeeded(db, forceReset = false) {
  try {
    console.log('🔍 Checking if initial data migration is needed...');

    // Check if data already exists
    const bookCount = db.prepare('SELECT COUNT(*) as count FROM livres').get();
    const userCount = db.prepare('SELECT COUNT(*) as count FROM utilisateurs').get();
    const memberCount = db.prepare('SELECT COUNT(*) as count FROM membres').get();

    // Check loans count
    const loanCount = db.prepare("SELECT COUNT(*) as count FROM emprunts WHERE statut = 'actif'").get();

    console.log('📊 Current database state:');
    console.log('  - Books:', bookCount.count);
    console.log('  - Users:', userCount.count);
    console.log('  - Members:', memberCount.count);
    console.log('  - Active Loans:', loanCount.count);

    // Check if we should skip demo data migration (production mode)
    const isProduction = process.env.NODE_ENV === 'production' || process.env.BIBLIOTECH_PRODUCTION === 'true';

    // Force reset for clean presentation data or migrate if database is empty
    if (forceReset || (bookCount.count === 0 && userCount.count === 0 && memberCount.count === 0)) {
      if (forceReset) {
        console.log('🔄 Force reset requested, clearing existing data...');
        // Clear existing data in correct order (respecting foreign keys)
        db.prepare('DELETE FROM emprunts').run();
        db.prepare('DELETE FROM membres').run();
        db.prepare('DELETE FROM livres').run();
        db.prepare('DELETE FROM utilisateurs').run();
        db.prepare('DELETE FROM categories').run();
        db.prepare('DELETE FROM journaux_audit').run();
        console.log('✅ Existing data cleared');
      }

      if (isProduction && !forceReset) {
        console.log('🏭 Production mode detected - skipping demo data migration');
        console.log('📚 Only creating essential categories for production use');

        // Only create categories in production mode
        const insertCategory = db.prepare('INSERT INTO categories (nom, description, ordre) VALUES (?, ?, ?)');
        const categories = [
          ['Fiction', 'Romans, nouvelles et œuvres littéraires', 0],
          ['Science', 'Ouvrages scientifiques et techniques', 1],
          ['Histoire', 'Livres d\'histoire et biographies', 2],
          ['Philosophie', 'Ouvrages philosophiques et de réflexion', 3],
          ['Art', 'Livres sur l\'art, la musique et la culture', 4],
          ['Jeunesse', 'Livres pour enfants et adolescents', 5],
          ['Non classé', 'Livres sans catégorie spécifique', 6]
        ];

        categories.forEach(([nom, description, ordre]) => {
          insertCategory.run(nom, description, ordre);
        });

        console.log('✅ Production setup completed - categories created, no demo data');
        return;
      }

      console.log('🚀 Migrating initial demo data...');
    } else {
      console.log('📚 Database already contains data, skipping migration');
      return;
    }

    // Insert categories
    const insertCategory = db.prepare('INSERT INTO categories (nom, description, ordre) VALUES (?, ?, ?)');
    const categories = [
      ['Fiction', 'Romans, nouvelles et œuvres littéraires', 0],
      ['Science', 'Ouvrages scientifiques et techniques', 1],
      ['Histoire', 'Livres d\'histoire et biographies', 2],
      ['Philosophie', 'Ouvrages philosophiques et de réflexion', 3],
      ['Art', 'Livres sur l\'art, la musique et la culture', 4],
      ['Jeunesse', 'Livres pour enfants et adolescents', 5],
      ['Non classé', 'Livres sans catégorie spécifique', 6]
    ];

    categories.forEach(([nom, description, ordre]) => {
      insertCategory.run(nom, description, ordre);
    });

    // Insert books
    const insertBook = db.prepare(`
      INSERT INTO livres (titre, auteur, categorie, genre, isbn, annee_publication, description, disponible)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const books = [
      ['Le Petit Prince', 'Antoine de Saint-Exupéry', 'Fiction', 'Conte', '9782070408504', 1943, 'Un conte poétique et philosophique', 0],
      ['Une brève histoire du temps', 'Stephen Hawking', 'Science', 'Physique', '9782070368228', 1988, 'Exploration de l\'univers et du temps', 1],
      ['L\'Étranger', 'Albert Camus', 'Fiction', 'Roman', '9782070360024', 1942, 'Roman existentialiste emblématique', 0],
      ['Sapiens: Une brève histoire de l\'humanité', 'Yuval Noah Harari', 'Histoire', 'Essai', '9782226257017', 2011, 'Une perspective fascinante sur l\'évolution humaine', 1],
      ['L\'Art de la guerre', 'Sun Tzu', 'Philosophie', 'Traité', null, -500, 'Traité de stratégie militaire et philosophique', 1],
      ['Harry Potter à l\'école des sorciers', 'J.K. Rowling', 'Jeunesse', 'Fantasy', '9782070541270', 1997, 'Premier tome de la saga Harry Potter', 1],
      ['La Joconde', 'Leonardo da Vinci', 'Art', 'Biographie', null, 1503, 'L\'histoire du chef-d\'œuvre de Léonard de Vinci', 1],
      ['1984', 'George Orwell', 'Fiction', 'Dystopie', '9782070368228', 1949, 'Roman dystopique sur la surveillance totalitaire', 1]
    ];

    books.forEach(([titre, auteur, categorie, genre, isbn, annee, description, disponible]) => {
      insertBook.run(titre, auteur, categorie, genre, isbn, annee, description, disponible);
    });

    // Insert members
    const insertMember = db.prepare(`
      INSERT INTO membres (nom, prenom, fonction, numero_membre, telephone, adresse, email, photo, date_inscription, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    // Updated member data to match available images in public/usersimages
    const members = [
      ['Ushindi', 'Gabriel', 'Ingenieur et Chercheur dans Les Nouvelles Technologies', 'BT20250001', '+243 123 456 789', '123 Avenue de la Paix, Kinshasa, RDC', '<EMAIL>', '/usersimages/gabriel.jpg', '2024-01-15', 1],
      ['Nyota', 'Grace', 'Professeure de Littérature', 'BT20250002', '+243 987 654 321', '456 Boulevard de la Liberté, Lubumbashi, RDC', '<EMAIL>', '/usersimages/grace_nyota.jpg', '2024-02-10', 1],
      ['Abusa', 'Jocy', 'Médecin Généraliste', 'BT20250003', '+243 555 123 456', '789 Rue de la Santé, Goma, RDC', '<EMAIL>', '/usersimages/jocyabusa.jpg', '2024-03-05', 1],
      ['Mukendi', 'Joseph', 'Architecte', 'BT20250004', '+243 777 888 999', '321 Avenue de l\'Architecture, Bukavu, RDC', '<EMAIL>', '/usersimages/joseph.jpg', '2024-04-12', 1],
      ['Tshisekedi', 'Aaron', 'Avocat', 'BT20250005', '+243 444 555 666', '654 Rue de la Justice, Mbuji-Mayi, RDC', '<EMAIL>', '/usersimages/aaron.png', '2024-05-20', 1]
    ];

    members.forEach(([nom, prenom, fonction, numero, telephone, adresse, email, photo, date, active]) => {
      insertMember.run(nom, prenom, fonction, numero, telephone, adresse, email, photo, date, active);
    });

    // Insert users
    console.log('👥 Creating default users...');
    const insertUser = db.prepare(`
      INSERT INTO utilisateurs (username, email, password_hash, role, is_active)
      VALUES (?, ?, ?, ?, ?)
    `);

    // Create default users with properly hashed passwords
    const crypto = require('crypto');
    function hashPassword(password) {
      const salt = crypto.randomBytes(16).toString('hex');
      const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
      return `${salt}:${hash}`;
    }

    const users = [
      ['superadmin', '<EMAIL>', hashPassword('admin123'), 'super_admin', 1],
      ['admin1', '<EMAIL>', hashPassword('admin123'), 'administrator', 1]
    ];

    users.forEach(([username, email, hash, role, active]) => {
      try {
        insertUser.run(username, email, hash, role, active);
        console.log('✅ Created user:', username, 'with role:', role);
      } catch (error) {
        console.error('❌ Failed to create user:', username, error);
      }
    });

    // Verify users were created
    const finalUserCount = db.prepare('SELECT COUNT(*) as count FROM utilisateurs').get();
    console.log('👥 Total users created:', finalUserCount.count);

    // Create some sample loans
    const insertLoan = db.prepare(`
      INSERT INTO emprunts (livre_id, membre_id, date_emprunt, date_retour_prevue, statut)
      VALUES (?, ?, ?, ?, ?)
    `);

    const loans = [
      [1, 1, '2025-06-01', '2025-06-15', 'actif'],
      [3, 2, '2025-05-20', '2025-06-20', 'actif']
    ];

    loans.forEach(([livre_id, membre_id, date_emprunt, date_retour, statut]) => {
      insertLoan.run(livre_id, membre_id, date_emprunt, date_retour, statut);
    });

    console.log('✅ Initial data migration completed successfully');
  } catch (error) {
    console.error('❌ Error during data migration:', error);
  }
}

// Initialize database when app is ready
app.whenReady().then(async () => {
  await initializeDatabaseService();
  createWindow();
});

// Helper function to get book loan information
function getBookLoanInfo(bookId) {
  if (!databaseService) return {};

  try {
    const stmt = databaseService.prepare(`
      SELECT
        e.membre_id as emprunteurId,
        e.date_emprunt as dateEmprunt,
        e.date_retour_prevue as dateRetour
      FROM emprunts e
      WHERE e.livre_id = ? AND e.statut = 'actif'
      ORDER BY e.created_at DESC
      LIMIT 1
    `);

    const loan = stmt.get(bookId);
    console.log(`🔍 Checking loan for book ${bookId}:`, loan);

    return loan ? {
      emprunteurId: loan.emprunteurId,
      dateEmprunt: loan.dateEmprunt,
      dateRetour: loan.dateRetour
    } : {};
  } catch (error) {
    console.error('Error getting book loan info:', error);
    return {};
  }
}

// IPC Handlers for database operations
ipcMain.handle('db:getAllBooks', async () => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        titre,
        auteur,
        categorie,
        genre,
        isbn,
        annee_publication as anneePublication,
        description,
        disponible,
        quantite_totale as quantiteTotale,
        quantite_disponible as quantiteDisponible,
        created_at as createdAt,
        updated_at as updatedAt
      FROM livres
      ORDER BY titre ASC
    `);

    const rows = stmt.all();
    const booksWithLoans = rows.map(row => {
      const loanInfo = row.disponible ? {} : getBookLoanInfo(row.id);
      const book = {
        ...row,
        disponible: Boolean(row.disponible),
        quantiteTotale: row.quantiteTotale || 1,
        quantiteDisponible: row.quantiteDisponible || (row.disponible ? 1 : 0),
        // Add loan information if the book is not available
        ...loanInfo
      };

      // Debug logging for books with loans
      if (!row.disponible) {
        console.log(`📖 Book "${row.titre}" - Available: ${row.disponible}, Loan info:`, loanInfo);
      }

      return book;
    });

    console.log(`📚 Returning ${booksWithLoans.length} books, ${booksWithLoans.filter(b => !b.disponible).length} borrowed`);
    return booksWithLoans;
  } catch (error) {
    console.error('Error getting all books:', error);
    return [];
  }
});

ipcMain.handle('db:addBook', async (event, book) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const quantite = book.quantite || 1;
    const stmt = databaseService.prepare(`
      INSERT INTO livres (
        titre, auteur, categorie, genre, isbn,
        annee_publication, description, disponible,
        quantite_totale, quantite_disponible
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      book.titre,
      book.auteur,
      book.categorie,
      book.genre || null,
      book.isbn || null,
      book.anneePublication || null,
      book.description || null,
      quantite > 0 ? 1 : 0, // disponible: true if quantity > 0
      quantite,
      quantite // initially all copies are available
    );

    // Get the created book
    const getStmt = databaseService.prepare(`
      SELECT
        id,
        titre,
        auteur,
        categorie,
        genre,
        isbn,
        annee_publication as anneePublication,
        description,
        disponible,
        quantite_totale as quantiteTotale,
        quantite_disponible as quantiteDisponible,
        created_at as createdAt,
        updated_at as updatedAt
      FROM livres
      WHERE id = ?
    `);

    const newBook = getStmt.get(result.lastInsertRowid);
    return {
      ...newBook,
      disponible: Boolean(newBook.disponible),
      quantiteTotale: newBook.quantiteTotale || 1,
      quantiteDisponible: newBook.quantiteDisponible || (newBook.disponible ? 1 : 0)
    };
  } catch (error) {
    console.error('Error adding book:', error);
    throw error;
  }
});

ipcMain.handle('db:getAllCategories', async () => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        nom as name,
        description,
        ordre as "order",
        created_at as createdAt,
        updated_at as updatedAt
      FROM categories
      ORDER BY ordre ASC, nom ASC
    `);

    return stmt.all();
  } catch (error) {
    console.error('Error getting categories:', error);
    return [];
  }
});

ipcMain.handle('db:getAllMembers', async () => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        nom,
        prenom,
        fonction,
        numero_membre as numeroMembre,
        telephone,
        adresse,
        email,
        photo,
        date_inscription as dateInscription,
        is_active as isActive,
        created_at as createdAt,
        updated_at as updatedAt
      FROM membres
      ORDER BY nom ASC, prenom ASC
    `);

    const rows = stmt.all();
    return rows.map(row => ({
      ...row,
      isActive: Boolean(row.isActive)
    }));
  } catch (error) {
    console.error('Error getting members:', error);
    return [];
  }
});

ipcMain.handle('db:getAllMemberProfiles', async () => {
  if (!databaseService) return [];

  try {
    // Get all members
    const membersStmt = databaseService.prepare(`
      SELECT
        id,
        nom,
        prenom,
        fonction,
        numero_membre as numeroMembre,
        telephone,
        adresse,
        email,
        photo,
        date_inscription as dateInscription,
        is_active as isActive,
        created_at as createdAt,
        updated_at as updatedAt
      FROM membres
      ORDER BY nom ASC, prenom ASC
    `);

    const members = membersStmt.all().map(row => ({
      ...row,
      isActive: Boolean(row.isActive)
    }));

    // Get borrowed books for each member
    const borrowedBooksStmt = databaseService.prepare(`
      SELECT
        e.id,
        e.livre_id as livreId,
        l.titre,
        l.auteur,
        e.date_emprunt as dateEmprunt,
        e.date_retour_prevue as dateRetour,
        e.statut
      FROM emprunts e
      JOIN livres l ON e.livre_id = l.id
      WHERE e.membre_id = ? AND e.statut IN ('actif', 'en_retard')
      ORDER BY e.date_emprunt DESC
    `);

    return members.map(member => {
      const borrowedBooks = borrowedBooksStmt.all(member.id).map(book => {
        const today = new Date();
        const dueDate = new Date(book.dateRetour);
        const timeDiff = dueDate.getTime() - today.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        return {
          ...book,
          isOverdue: daysDiff < 0,
          daysUntilDue: daysDiff
        };
      });

      const overdueBooks = borrowedBooks.filter(book => book.isOverdue);

      return {
        ...member,
        livresEmpruntes: borrowedBooks,
        nombreLivresEmpruntes: borrowedBooks.length,
        nombreLivresEnRetard: overdueBooks.length
      };
    });
  } catch (error) {
    console.error('Error getting member profiles:', error);
    return [];
  }
});

// Get borrowed books for a specific member
ipcMain.handle('db:getMemberBorrowedBooks', async (event, memberId) => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        l.id,
        l.titre,
        l.auteur,
        l.categorie,
        l.genre,
        e.date_emprunt as dateEmprunt,
        e.date_retour_prevue as dateRetour,
        e.statut
      FROM emprunts e
      JOIN livres l ON e.livre_id = l.id
      WHERE e.membre_id = ? AND e.statut = 'actif'
      ORDER BY e.date_emprunt DESC
    `);

    const rows = stmt.all(memberId);
    return rows.map(row => {
      const dateRetour = new Date(row.dateRetour);
      const today = new Date();
      const daysDiff = Math.ceil((dateRetour.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

      return {
        ...row,
        emprunteurId: memberId,
        disponible: false,
        isOverdue: daysDiff < 0,
        daysUntilDue: daysDiff,
        dateEmpruntFormatted: new Date(row.dateEmprunt).toLocaleDateString('fr-FR'),
        dateRetourFormatted: dateRetour.toLocaleDateString('fr-FR')
      };
    });
  } catch (error) {
    console.error('Error getting member borrowed books:', error);
    return [];
  }
});

// Enhanced stats handler that matches dashboard requirements exactly
ipcMain.handle('db:getStats', async () => {
  if (!databaseService) return { totalBooks: 0, totalMembers: 0, borrowedBooks: 0, overdueBooks: 0, dueSoonBooks: 0 };

  try {
    // Get total books count
    const totalBooks = databaseService.prepare('SELECT COUNT(*) as count FROM livres').get();

    // Get total active members count
    const totalMembers = databaseService.prepare('SELECT COUNT(*) as count FROM membres WHERE is_active = 1').get();

    // Get borrowed books count (books that are currently not available)
    const borrowedBooks = databaseService.prepare('SELECT COUNT(*) as count FROM livres WHERE disponible = 0').get();

    // Get overdue books count (books with return date in the past)
    const overdueBooks = databaseService.prepare(`
      SELECT COUNT(*) as count
      FROM emprunts e
      WHERE e.statut = 'actif'
      AND e.date_retour_prevue IS NOT NULL
      AND date(e.date_retour_prevue) < date('now')
    `).get();

    // Get due soon books count (books due within 3 days)
    const dueSoonBooks = databaseService.prepare(`
      SELECT COUNT(*) as count
      FROM emprunts e
      WHERE e.statut = 'actif'
      AND e.date_retour_prevue IS NOT NULL
      AND date(e.date_retour_prevue) >= date('now')
      AND date(e.date_retour_prevue) <= date('now', '+3 days')
    `).get();

    return {
      totalBooks: totalBooks.count,
      totalMembers: totalMembers.count,
      borrowedBooks: borrowedBooks.count,
      overdueBooks: overdueBooks.count,
      dueSoonBooks: dueSoonBooks.count
    };
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    return { totalBooks: 0, totalMembers: 0, borrowedBooks: 0, overdueBooks: 0, dueSoonBooks: 0 };
  }
});

// Additional IPC handlers for missing operations
ipcMain.handle('db:updateBook', async (event, id, updates) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    // Enhanced logging for deployment debugging
    console.log('[DEPLOYMENT DEBUG] db:updateBook called with:', { id, updates });

    const fields = [];
    const values = [];

    if (updates.titre !== undefined) {
      fields.push('titre = ?');
      values.push(updates.titre);
    }
    if (updates.auteur !== undefined) {
      fields.push('auteur = ?');
      values.push(updates.auteur);
    }
    if (updates.categorie !== undefined) {
      fields.push('categorie = ?');
      values.push(updates.categorie);
    }
    if (updates.genre !== undefined) {
      fields.push('genre = ?');
      values.push(updates.genre);
    }
    if (updates.isbn !== undefined) {
      fields.push('isbn = ?');
      values.push(updates.isbn);
    }
    if (updates.anneePublication !== undefined) {
      fields.push('annee_publication = ?');
      values.push(updates.anneePublication);
    }
    if (updates.description !== undefined) {
      fields.push('description = ?');
      values.push(updates.description);
    }
    if (updates.disponible !== undefined) {
      fields.push('disponible = ?');
      values.push(updates.disponible ? 1 : 0);
      console.log('[DEPLOYMENT DEBUG] Updating disponible field to:', updates.disponible ? 1 : 0);
    }

    // Handle quantity updates with special logic
    if (updates.quantiteTotale !== undefined) {
      // Get current quantities to calculate the difference
      const currentBookStmt = databaseService.prepare(`
        SELECT quantite_totale, quantite_disponible
        FROM livres WHERE id = ?
      `);
      const currentBook = currentBookStmt.get(id);

      if (currentBook) {
        const currentBorrowed = currentBook.quantite_totale - currentBook.quantite_disponible;
        const newAvailable = updates.quantiteTotale - currentBorrowed;

        // Ensure we don't go below borrowed copies
        if (newAvailable >= 0) {
          fields.push('quantite_totale = ?');
          values.push(updates.quantiteTotale);
          fields.push('quantite_disponible = ?');
          values.push(newAvailable);
          fields.push('disponible = ?');
          values.push(newAvailable > 0 ? 1 : 0);
        }
      }
    }

    if (fields.length === 0) {
      console.log('[DEPLOYMENT DEBUG] No fields to update, returning false');
      return false;
    }

    values.push(id);
    const query = `UPDATE livres SET ${fields.join(', ')} WHERE id = ?`;
    console.log('[DEPLOYMENT DEBUG] Executing query:', query, 'with values:', values);

    const stmt = databaseService.prepare(query);
    const result = stmt.run(...values);

    console.log('[DEPLOYMENT DEBUG] Update result:', { changes: result.changes, lastInsertRowid: result.lastInsertRowid });

    // Verify the update by reading the book back
    const verifyStmt = databaseService.prepare('SELECT * FROM livres WHERE id = ?');
    const updatedBook = verifyStmt.get(id);
    console.log('[DEPLOYMENT DEBUG] Book after update:', updatedBook);

    return result.changes > 0;
  } catch (error) {
    console.error('[DEPLOYMENT DEBUG] Error updating book:', error);
    console.error('[DEPLOYMENT DEBUG] Error stack:', error.stack);
    throw error;
  }
});

ipcMain.handle('db:deleteBook', async (event, id) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    console.log('[DEPLOYMENT DEBUG] db:deleteBook called with id:', id);

    // Check for active loans
    const activeLoansStmt = databaseService.prepare(`
      SELECT COUNT(*) as count
      FROM emprunts
      WHERE livre_id = ? AND statut = 'actif'
    `);
    const activeLoanCount = activeLoansStmt.get(id);
    console.log('[DEPLOYMENT DEBUG] Active loan count for book:', activeLoanCount);

    if (activeLoanCount.count > 0) {
      console.log('[DEPLOYMENT DEBUG] Cannot delete book - has active loans');
      throw new Error('Impossible de supprimer un livre actuellement emprunté');
    }

    const stmt = databaseService.prepare('DELETE FROM livres WHERE id = ?');
    const result = stmt.run(id);
    console.log('[DEPLOYMENT DEBUG] Delete result:', { changes: result.changes });
    return result.changes > 0;
  } catch (error) {
    console.error('[DEPLOYMENT DEBUG] Error deleting book:', error);
    console.error('[DEPLOYMENT DEBUG] Error stack:', error.stack);
    throw error;
  }
});

ipcMain.handle('db:addCategory', async (event, name, description) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    // Vérifier si la catégorie existe déjà
    const existingStmt = databaseService.prepare('SELECT id FROM categories WHERE nom = ?');
    const existing = existingStmt.get(name);
    if (existing) {
      throw new Error(`Une catégorie avec le nom "${name}" existe déjà`);
    }

    // Get next order
    const maxOrderStmt = databaseService.prepare('SELECT MAX(ordre) as maxOrder FROM categories');
    const maxOrderResult = maxOrderStmt.get();
    const nextOrder = (maxOrderResult.maxOrder || 0) + 1;

    const stmt = databaseService.prepare(`
      INSERT INTO categories (nom, description, ordre)
      VALUES (?, ?, ?)
    `);

    const result = stmt.run(name, description || null, nextOrder);

    // Get the created category
    const getStmt = databaseService.prepare(`
      SELECT
        id,
        nom as name,
        description,
        ordre as "order",
        created_at as createdAt,
        updated_at as updatedAt
      FROM categories
      WHERE id = ?
    `);

    return getStmt.get(result.lastInsertRowid);
  } catch (error) {
    console.error('Error adding category:', error);
    // Améliorer les messages d'erreur pour les contraintes de base de données
    if (error.message && error.message.includes('UNIQUE constraint failed')) {
      throw new Error(`Une catégorie avec le nom "${name}" existe déjà`);
    }
    throw error;
  }
});

ipcMain.handle('db:updateCategory', async (event, id, name, description) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    // Vérifier si une autre catégorie avec ce nom existe déjà
    const existingStmt = databaseService.prepare('SELECT id FROM categories WHERE nom = ? AND id != ?');
    const existing = existingStmt.get(name, id);
    if (existing) {
      throw new Error(`Une catégorie avec le nom "${name}" existe déjà`);
    }

    const stmt = databaseService.prepare(`
      UPDATE categories
      SET nom = ?, description = ?
      WHERE id = ?
    `);

    const result = stmt.run(name, description || null, id);
    return result.changes > 0;
  } catch (error) {
    console.error('Error updating category:', error);
    // Améliorer les messages d'erreur pour les contraintes de base de données
    if (error.message && error.message.includes('UNIQUE constraint failed')) {
      throw new Error(`Une catégorie avec le nom "${name}" existe déjà`);
    }
    throw error;
  }
});

ipcMain.handle('db:deleteCategory', async (event, id) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const category = databaseService.prepare(`
      SELECT nom as name FROM categories WHERE id = ?
    `).get(id);

    if (!category) return false;

    // Move books to "Non classé"
    const updateBooksStmt = databaseService.prepare(`
      UPDATE livres
      SET categorie = 'Non classé'
      WHERE categorie = ?
    `);
    updateBooksStmt.run(category.name);

    // Delete category
    const stmt = databaseService.prepare('DELETE FROM categories WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  } catch (error) {
    console.error('Error deleting category:', error);
    throw error;
  }
});

ipcMain.handle('db:reorderCategories', async (event, categoryIds) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const transaction = databaseService.transaction(() => {
      categoryIds.forEach((id, index) => {
        const stmt = databaseService.prepare('UPDATE categories SET ordre = ? WHERE id = ?');
        stmt.run(index, id);
      });
    });

    transaction();
    return true;
  } catch (error) {
    console.error('Error reordering categories:', error);
    return false;
  }
});

ipcMain.handle('db:getBooksCountInCategory', async (event, categoryName) => {
  if (!databaseService) return 0;

  try {
    const stmt = databaseService.prepare('SELECT COUNT(*) as count FROM livres WHERE categorie = ?');
    const result = stmt.get(categoryName);
    return result.count;
  } catch (error) {
    console.error('Error getting books count:', error);
    return 0;
  }
});

// Member management handlers
ipcMain.handle('db:addMember', async (event, member) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const stmt = databaseService.prepare(`
      INSERT INTO membres (
        nom, prenom, fonction, numero_membre, telephone,
        adresse, email, photo, date_inscription, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      member.nom,
      member.prenom,
      member.fonction,
      member.numeroMembre,
      member.telephone,
      member.adresse,
      member.email || null,
      member.photo,
      member.dateInscription,
      member.isActive ? 1 : 0
    );

    // Get the created member
    const getStmt = databaseService.prepare(`
      SELECT
        id,
        nom,
        prenom,
        fonction,
        numero_membre as numeroMembre,
        telephone,
        adresse,
        email,
        photo,
        date_inscription as dateInscription,
        is_active as isActive,
        created_at as createdAt,
        updated_at as updatedAt
      FROM membres
      WHERE id = ?
    `);

    const newMember = getStmt.get(result.lastInsertRowid);
    return {
      ...newMember,
      isActive: Boolean(newMember.isActive)
    };
  } catch (error) {
    console.error('Error adding member:', error);
    throw error;
  }
});

ipcMain.handle('db:updateMember', async (event, id, updates) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const fields = [];
    const values = [];

    if (updates.nom !== undefined) {
      fields.push('nom = ?');
      values.push(updates.nom);
    }
    if (updates.prenom !== undefined) {
      fields.push('prenom = ?');
      values.push(updates.prenom);
    }
    if (updates.fonction !== undefined) {
      fields.push('fonction = ?');
      values.push(updates.fonction);
    }
    if (updates.numeroMembre !== undefined) {
      fields.push('numero_membre = ?');
      values.push(updates.numeroMembre);
    }
    if (updates.telephone !== undefined) {
      fields.push('telephone = ?');
      values.push(updates.telephone);
    }
    if (updates.adresse !== undefined) {
      fields.push('adresse = ?');
      values.push(updates.adresse);
    }
    if (updates.email !== undefined) {
      fields.push('email = ?');
      values.push(updates.email);
    }
    if (updates.photo !== undefined) {
      fields.push('photo = ?');
      values.push(updates.photo);
    }
    if (updates.dateInscription !== undefined) {
      fields.push('date_inscription = ?');
      values.push(updates.dateInscription);
    }
    if (updates.isActive !== undefined) {
      fields.push('is_active = ?');
      values.push(updates.isActive ? 1 : 0);
    }

    if (fields.length === 0) return false;

    values.push(id);
    const stmt = databaseService.prepare(`
      UPDATE membres
      SET ${fields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  } catch (error) {
    console.error('Error updating member:', error);
    throw error;
  }
});

ipcMain.handle('db:deleteMember', async (event, id) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    // Check for active loans
    const activeLoansStmt = databaseService.prepare(`
      SELECT COUNT(*) as count
      FROM emprunts
      WHERE membre_id = ? AND statut = 'actif'
    `);
    const activeLoanCount = activeLoansStmt.get(id);

    if (activeLoanCount.count > 0) {
      throw new Error('Impossible de supprimer un membre ayant des emprunts actifs');
    }

    const stmt = databaseService.prepare('DELETE FROM membres WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  } catch (error) {
    console.error('Error deleting member:', error);
    throw error;
  }
});

// Loan management handlers
ipcMain.handle('db:createLoan', async (event, bookId, memberId, startDate, dueDate) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const transaction = databaseService.transaction(() => {
      // Check if book has available copies
      const checkBookStmt = databaseService.prepare('SELECT quantite_disponible, titre FROM livres WHERE id = ?');
      const book = checkBookStmt.get(bookId);

      if (!book || book.quantite_disponible <= 0) {
        throw new Error('Aucune copie disponible pour ce livre');
      }

      // Check if member already has an active loan for this specific book
      const checkExistingLoanStmt = databaseService.prepare(`
        SELECT COUNT(*) as count
        FROM emprunts
        WHERE livre_id = ? AND membre_id = ? AND statut = 'actif'
      `);
      const existingLoan = checkExistingLoanStmt.get(bookId, memberId);

      if (existingLoan && existingLoan.count > 0) {
        throw new Error(`Ce membre a déjà emprunté ce livre ("${book.titre}"). Un membre ne peut pas emprunter plusieurs copies du même livre.`);
      }

      // Decrease available quantity by 1
      const updateBookStmt = databaseService.prepare(`
        UPDATE livres
        SET quantite_disponible = quantite_disponible - 1,
            disponible = CASE WHEN quantite_disponible - 1 > 0 THEN 1 ELSE 0 END
        WHERE id = ?
      `);
      updateBookStmt.run(bookId);

      // Create loan
      const createLoanStmt = databaseService.prepare(`
        INSERT INTO emprunts (livre_id, membre_id, date_emprunt, date_retour_prevue, statut)
        VALUES (?, ?, ?, ?, 'actif')
      `);
      createLoanStmt.run(bookId, memberId, startDate, dueDate);
    });

    transaction();
    return true;
  } catch (error) {
    console.error('Error creating loan:', error);
    throw error; // Re-throw to preserve the specific error message
  }
});

ipcMain.handle('db:returnBook', async (event, bookId) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const transaction = databaseService.transaction(() => {
      // Check if there are active loans for this book
      const checkStmt = databaseService.prepare('SELECT COUNT(*) as count FROM emprunts WHERE livre_id = ? AND statut = \'actif\'');
      const result = checkStmt.get(bookId);

      if (!result || result.count === 0) {
        throw new Error('Aucun emprunt actif trouvé pour ce livre');
      }

      // Increase available quantity by 1
      const updateBookStmt = databaseService.prepare(`
        UPDATE livres
        SET quantite_disponible = quantite_disponible + 1,
            disponible = 1
        WHERE id = ?
      `);
      updateBookStmt.run(bookId);

      // Update loan (only one active loan per book at a time)
      const updateLoanStmt = databaseService.prepare(`
        UPDATE emprunts
        SET statut = 'retourne', date_retour_effective = CURRENT_TIMESTAMP
        WHERE livre_id = ? AND statut = 'actif'
        ORDER BY date_emprunt ASC
        LIMIT 1
      `);
      updateLoanStmt.run(bookId);
    });

    transaction();
    return true;
  } catch (error) {
    console.error('Error returning book:', error);
    return false;
  }
});

ipcMain.handle('db:extendLoan', async (event, bookId, newDueDate) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const stmt = databaseService.prepare(`
      UPDATE emprunts
      SET date_retour_prevue = ?, statut = 'actif'
      WHERE livre_id = ? AND statut IN ('actif', 'en_retard')
    `);

    const result = stmt.run(newDueDate, bookId);
    return result.changes > 0;
  } catch (error) {
    console.error('Error extending loan:', error);
    return false;
  }
});

// User management handlers
ipcMain.handle('db:getAllUsers', async () => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        username,
        email,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      ORDER BY username ASC
    `);

    const rows = stmt.all();
    return rows.map(row => ({
      ...row,
      isActive: Boolean(row.isActive)
    }));
  } catch (error) {
    console.error('Error getting users:', error);
    return [];
  }
});

ipcMain.handle('db:getUserByUsername', async (event, username) => {
  if (!databaseService) return null;

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        username,
        email,
        password_hash,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      WHERE username = ?
    `);

    const row = stmt.get(username);
    if (!row) return null;

    return {
      ...row,
      isActive: Boolean(row.isActive)
    };
  } catch (error) {
    console.error('Error getting user by username:', error);
    return null;
  }
});

ipcMain.handle('db:addUser', async (event, username, email, passwordHash, role) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    const stmt = databaseService.prepare(`
      INSERT INTO utilisateurs (username, email, password_hash, role, is_active)
      VALUES (?, ?, ?, ?, 1)
    `);

    const result = stmt.run(username, email, passwordHash, role);

    const newUserStmt = databaseService.prepare(`
      SELECT
        id,
        username,
        email,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      WHERE id = ?
    `);

    const newUser = newUserStmt.get(result.lastInsertRowid);
    return {
      ...newUser,
      isActive: Boolean(newUser.isActive)
    };
  } catch (error) {
    console.error('Error adding user:', error);
    throw error;
  }
});

// Add authentication handler
ipcMain.handle('db:authenticateUser', async (event, credentials) => {
  if (!databaseService) {
    console.error('❌ Database not initialized for authentication');
    throw new Error('Database not initialized');
  }

  try {
    console.log('🔐 Attempting authentication for user:', credentials.username);
    console.log('🔐 Credentials received:', { username: credentials.username, passwordLength: credentials.password?.length });

    // Check if users table exists and has data
    const tableCheck = databaseService.prepare(`
      SELECT COUNT(*) as count FROM sqlite_master
      WHERE type='table' AND name='utilisateurs'
    `).get();

    if (tableCheck.count === 0) {
      console.error('❌ Users table does not exist');
      throw new Error('Users table not found');
    }

    const userCount = databaseService.prepare('SELECT COUNT(*) as count FROM utilisateurs').get();
    console.log('👥 Total users in database:', userCount.count);

    // Get user with password hash
    const userStmt = databaseService.prepare(`
      SELECT
        id,
        username,
        email,
        password_hash,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      WHERE username = ? AND is_active = 1
    `);

    const userWithPassword = userStmt.get(credentials.username);
    if (!userWithPassword) {
      console.log('❌ User not found or inactive:', credentials.username);
      return null;
    }

    console.log('✅ User found:', credentials.username, 'Role:', userWithPassword.role);

    // Verify password using crypto module
    const crypto = require('crypto');
    function verifyPassword(password, hashedPassword) {
      console.log('🔍 Verifying password, hash format:', hashedPassword.substring(0, 10) + '...');

      // Handle legacy bcrypt-style hashes (from migration data)
      if (hashedPassword.startsWith('$2b$')) {
        console.log('🔍 Using bcrypt fallback verification');
        // This is a placeholder hash from migration - need to check if it's the default admin password
        if (password === 'admin123') {
          return true;
        }
        return false;
      }

      // Handle legacy simple hash format
      if (hashedPassword.startsWith('hashed_')) {
        console.log('🔍 Using legacy hash verification');
        return hashedPassword === `hashed_${password}`;
      }

      // New proper PBKDF2 hash format (salt:hash)
      const [salt, hash] = hashedPassword.split(':');
      if (!salt || !hash) {
        console.error('❌ Invalid password hash format:', hashedPassword);
        return false;
      }

      console.log('🔍 Using PBKDF2 hash verification');
      const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
      return hash === verifyHash;
    }

    const passwordValid = verifyPassword(credentials.password, userWithPassword.password_hash);
    if (!passwordValid) {
      console.log('❌ Password verification failed for user:', credentials.username);
      return null;
    }

    console.log('✅ Password verified successfully for user:', credentials.username);

    // Update last login
    const updateStmt = databaseService.prepare(`
      UPDATE utilisateurs
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    updateStmt.run(userWithPassword.id);

    // Return user without password hash
    const { password_hash, ...user } = userWithPassword;
    console.log('✅ Authentication successful for user:', user.username);
    return user;
  } catch (error) {
    console.error('❌ Error authenticating user:', error);
    throw error;
  }
});

// Add create user handler with proper password hashing
ipcMain.handle('db:createUser', async (event, userData) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    // Check if user already exists
    const existingUserStmt = databaseService.prepare(`
      SELECT id FROM utilisateurs WHERE username = ?
    `);
    const existingUser = existingUserStmt.get(userData.username);
    if (existingUser) {
      throw new Error('Un utilisateur avec ce nom d\'utilisateur existe déjà');
    }

    // Hash password properly
    const crypto = require('crypto');
    function hashPassword(password) {
      const salt = crypto.randomBytes(16).toString('hex');
      const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
      return `${salt}:${hash}`;
    }

    const passwordHash = hashPassword(userData.password);

    // Create user
    const stmt = databaseService.prepare(`
      INSERT INTO utilisateurs (username, email, password_hash, role, is_active)
      VALUES (?, ?, ?, ?, 1)
    `);

    const result = stmt.run(userData.username, userData.email, passwordHash, userData.role);

    const newUserStmt = databaseService.prepare(`
      SELECT
        id,
        username,
        email,
        role,
        is_active as isActive,
        last_login as lastLogin,
        created_at as createdAt
      FROM utilisateurs
      WHERE id = ?
    `);

    const newUser = newUserStmt.get(result.lastInsertRowid);
    return {
      ...newUser,
      isActive: Boolean(newUser.isActive)
    };
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
});

ipcMain.handle('db:updateLastLogin', async (event, userId) => {
  if (!databaseService) return false;

  try {
    const stmt = databaseService.prepare(`
      UPDATE utilisateurs
      SET last_login = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const result = stmt.run(userId);
    return result.changes > 0;
  } catch (error) {
    console.error('Error updating last login:', error);
    return false;
  }
});

// Add missing updateUser handler
ipcMain.handle('db:updateUser', async (event, userId, updates) => {
  if (!databaseService) return false;

  try {
    // Build dynamic update query based on provided updates
    const allowedFields = ['email', 'is_active'];
    const updateFields = [];
    const values = [];

    Object.keys(updates).forEach(key => {
      if (allowedFields.includes(key)) {
        updateFields.push(`${key} = ?`);
        values.push(updates[key]);
      } else if (key === 'isActive') {
        // Handle the camelCase version
        updateFields.push('is_active = ?');
        values.push(updates[key]);
      }
    });

    if (updateFields.length === 0) {
      console.warn('No valid fields to update');
      return false;
    }

    values.push(userId); // Add userId for WHERE clause

    const stmt = databaseService.prepare(`
      UPDATE utilisateurs
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    return result.changes > 0;
  } catch (error) {
    console.error('Error updating user:', error);
    return false;
  }
});

// Add missing updateUserPassword handler
ipcMain.handle('db:updateUserPassword', async (event, userId, newPassword) => {
  if (!databaseService) return false;

  try {
    // Hash the new password using the same method as user creation
    const crypto = require('crypto');
    function hashPassword(password) {
      const salt = crypto.randomBytes(16).toString('hex');
      const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
      return `${salt}:${hash}`;
    }

    const passwordHash = hashPassword(newPassword);

    const stmt = databaseService.prepare(`
      UPDATE utilisateurs
      SET password_hash = ?
      WHERE id = ?
    `);

    const result = stmt.run(passwordHash, userId);
    return result.changes > 0;
  } catch (error) {
    console.error('Error updating user password:', error);
    return false;
  }
});

// Add missing deleteUser handler
ipcMain.handle('db:deleteUser', async (event, userId) => {
  if (!databaseService) return false;

  try {
    // Check if user exists
    const userStmt = databaseService.prepare(`
      SELECT id, username, role FROM utilisateurs WHERE id = ?
    `);
    const user = userStmt.get(userId);

    if (!user) {
      console.error('User not found:', userId);
      return false;
    }

    // Prevent deletion of the last super admin
    const superAdminCountStmt = databaseService.prepare(`
      SELECT COUNT(*) as count FROM utilisateurs WHERE role = 'super_admin' AND is_active = 1
    `);
    const superAdminCount = superAdminCountStmt.get();

    if (user.role === 'super_admin' && superAdminCount.count <= 1) {
      console.error('Cannot delete the last super admin');
      return false;
    }

    // Delete the user
    const deleteStmt = databaseService.prepare('DELETE FROM utilisateurs WHERE id = ?');
    const result = deleteStmt.run(userId);

    console.log(`User deletion result for ${user.username}:`, result.changes > 0);
    return result.changes > 0;
  } catch (error) {
    console.error('Error deleting user:', error);
    return false;
  }
});

// Audit log handlers
ipcMain.handle('db:addAuditLog', async (event, userId, username, action, resource, resourceId, details) => {
  if (!databaseService) return false;

  try {
    const stmt = databaseService.prepare(`
      INSERT INTO journaux_audit (user_id, username, action, resource, resource_id, details)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(userId, username, action, resource, resourceId || null, details || '');
    return result.changes > 0;
  } catch (error) {
    console.error('Error adding audit log:', error);
    return false;
  }
});

ipcMain.handle('db:getAuditLogs', async (event, limit = 100, offset = 0) => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        user_id as userId,
        username,
        action,
        resource,
        resource_id as resourceId,
        details,
        timestamp
      FROM journaux_audit
      ORDER BY timestamp DESC
      LIMIT ? OFFSET ?
    `);

    return stmt.all(limit, offset);
  } catch (error) {
    console.error('Error getting audit logs:', error);
    return [];
  }
});

ipcMain.handle('db:getUserAuditLogs', async (event, userId, limit = 50) => {
  if (!databaseService) return [];

  try {
    const stmt = databaseService.prepare(`
      SELECT
        id,
        user_id as userId,
        username,
        action,
        resource,
        resource_id as resourceId,
        details,
        timestamp
      FROM journaux_audit
      WHERE user_id = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `);

    return stmt.all(userId, limit);
  } catch (error) {
    console.error('Error getting user audit logs:', error);
    return [];
  }
});

// Additional utility handlers
ipcMain.handle('db:getDatabasePath', async () => {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, 'bibliotech.db');
});

ipcMain.handle('db:backup', async (event, backupPath) => {
  if (!databaseService) throw new Error('Database not initialized');

  try {
    databaseService.backup(backupPath);
    return true;
  } catch (error) {
    console.error('Error creating backup:', error);
    throw error;
  }
});

ipcMain.handle('db:migrateInitialData', async () => {
  try {
    await migrateInitialDataIfNeeded(databaseService);
    return { success: true, message: 'Migration completed successfully' };
  } catch (error) {
    console.error('Error during migration:', error);
    return { success: false, message: error.message };
  }
});

// Handler to save uploaded user image
ipcMain.handle('save-user-image', async (event, imageData, filename) => {
  try {
    // Ensure user images directory exists
    if (!fs.existsSync(globalUserImagesPath)) {
      fs.mkdirSync(globalUserImagesPath, { recursive: true });
    }

    // Remove data URL prefix if present
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');

    // Generate unique filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      filename = `user_${timestamp}.jpg`;
    }

    // Ensure filename has proper extension
    if (!filename.match(/\.(jpg|jpeg|png|gif)$/i)) {
      filename += '.jpg';
    }

    const imagePath = path.join(globalUserImagesPath, filename);

    // Save the image
    fs.writeFileSync(imagePath, base64Data, 'base64');

    console.log('✅ Image saved successfully:', imagePath);

    // Return the relative path for storage in database
    return `usersimages/${filename}`;
  } catch (error) {
    console.error('❌ Error saving user image:', error);
    return null;
  }
});

// Handler to get user image path for serving images
ipcMain.handle('get-user-image-path', async (event, imagePath) => {
  try {
    // Remove leading slash if present
    const cleanImagePath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;

    // Extract filename from path like 'usersimages/gabriel.jpg'
    const filename = path.basename(cleanImagePath);

    // Check in userData directory first
    const userDataImagePath = path.join(globalUserImagesPath || '', filename);
    if (fs.existsSync(userDataImagePath)) {
      return `file://${userDataImagePath}`;
    }

    // Fallback to public directory
    const publicImagePath = path.join(__dirname, 'usersimages', filename);
    if (fs.existsSync(publicImagePath)) {
      return `file://${publicImagePath}`;
    }

    // Return null if image not found
    return null;
  } catch (error) {
    console.error('Error getting user image path:', error);
    return null;
  }
});

// Handler to reset database to initial presentation data (5 members)
ipcMain.handle('db:resetToInitialData', async () => {
  try {
    console.log('🔄 Resetting database to initial presentation data...');
    await migrateInitialDataIfNeeded(databaseService, true); // Force reset
    return { success: true, message: 'Database reset to initial data successfully' };
  } catch (error) {
    console.error('Error resetting database:', error);
    return { success: false, message: error.message };
  }
});

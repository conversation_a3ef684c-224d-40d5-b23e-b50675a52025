import React, { useEffect } from 'react';
import type { LibraryMember } from '../types/member';
import { calculateSubscriptionEndDate } from '../types/member';
import ModalPortal from './ModalPortal';
import { PrintIcon } from './Icons';
import ProfileImage from './ProfileImage';

interface PrintMemberCardModalProps {
  member: LibraryMember;
  isOpen: boolean;
  onClose: () => void;
}

const PrintMemberCardModal: React.FC<PrintMemberCardModalProps> = ({
  member,
  isOpen,
  onClose
}) => {
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const subscriptionEndDate = calculateSubscriptionEndDate(member.dateInscription);

  const handlePrint = () => {
    window.print();
  };

  // <PERSON>le keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        handlePrint();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <ModalPortal isOpen={isOpen} onClose={onClose}>
      <div className="modal-content print-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header print-hide">
          <h2 className="modal-title">Carte de membre - {member.prenom} {member.nom}</h2>
          <button
            type="button"
            className="modal-close"
            onClick={onClose}
            aria-label="Fermer"
          >
            ×
          </button>
        </div>

        <div className="modal-body">
          <div className="print-actions print-hide">
            <button
              className="btn btn-primary"
              onClick={handlePrint}
            >
              <PrintIcon size={16} />
              Imprimer la carte
            </button>
            <p className="text-sm text-neutral">
              Raccourci clavier: Ctrl+P
            </p>
          </div>

          {/* Member Card */}
          <div className="member-card">
            <div className="member-card-front">
              {/* Header with logo */}
              <div className="card-header">
                <img 
                  src="/assets/BiblioTech_Logo.png" 
                  alt="BiblioTech" 
                  className="card-logo"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
                <div className="card-title">
                  <h3>BiblioTech</h3>
                  <p>Carte de Membre</p>
                </div>
              </div>

              {/* Member Information */}
              <div className="card-body">
                <div className="member-photo-section">
                  <ProfileImage
                    src={member.photo}
                    alt={`${member.prenom} ${member.nom}`}
                    className="member-photo"
                    fallbackName={`${member.prenom} ${member.nom}`}
                    size={150}
                  />
                </div>

                <div className="member-info-section">
                  <div className="member-name">
                    <h4>{member.prenom} {member.nom}</h4>
                  </div>

                  <div className="member-details">
                    <div className="detail-row">
                      <span className="detail-label">N° Membre:</span>
                      <span className="detail-value">{member.numeroMembre}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Fonction:</span>
                      <span className="detail-value">{member.fonction}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Téléphone:</span>
                      <span className="detail-value">{member.telephone}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Email:</span>
                      <span className="detail-value">{member.email || 'Non renseigné'}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Adresse:</span>
                      <span className="detail-value">{member.adresse}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Date d'inscription:</span>
                      <span className="detail-value">{formatDate(member.dateInscription)}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Fin d'abonnement:</span>
                      <span className="detail-value">{formatDate(subscriptionEndDate)}</span>
                    </div>

                    <div className="detail-row">
                      <span className="detail-label">Statut:</span>
                      <span className={`detail-value status-${member.isActive ? 'active' : 'inactive'}`}>
                        {member.isActive ? 'Actif' : 'Inactif'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="card-footer">
                <p className="card-footer-text">
                  Cette carte est personnelle et non transférable. 
                  En cas de perte, veuillez contacter l'administration.
                </p>
                <div className="card-date">
                  <p>Émise le: {formatDate(new Date().toISOString().split('T')[0])}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer print-hide">
          <button
            className="btn btn-secondary"
            onClick={onClose}
          >
            Fermer
          </button>
          <button
            className="btn btn-primary"
            onClick={handlePrint}
          >
            <PrintIcon size={16} />
            Imprimer
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default PrintMemberCardModal;

// Member management component for BiblioTech
import React, { useState, useEffect } from 'react';
import { UsersIcon, PlusIcon, EditIcon, TrashIcon, SearchIcon, PrintIcon, DownloadIcon } from './Icons';
import type { LibraryMember, CreateMemberData, UpdateMemberData } from '../types/member';
import { generateMembershipNumber } from '../types/member';
import EditMemberModal from './EditMemberModal';
import MemberProfile from './MemberProfile';
import PrintMemberCardModal from './PrintMemberCardModal';
import ModalPortal from './ModalPortal';
import ConfirmationModal from './ConfirmationModal';
import ProfileImage from './ProfileImage';
import type { Book } from '../types/library';
import { exportMembersToCSV } from '../services/csv-utils';

interface MemberManagementProps {
  members: LibraryMember[];
  onAddMember: (memberData: CreateMemberData) => Promise<boolean>;
  onUpdateMember: (id: number, updates: UpdateMemberData) => Promise<boolean>;
  onDeleteMember: (id: number) => Promise<boolean>;
  borrowedBooks?: Book[];
  onReturnBook?: (bookId: string) => void;
  onExtendLoan?: (bookId: string, newDueDate: string) => void;
}

const MemberManagement: React.FC<MemberManagementProps> = ({
  members,
  onAddMember,
  onUpdateMember,
  onDeleteMember,
  borrowedBooks: _borrowedBooks = [], // Legacy prop - now using direct database calls
  onReturnBook,
  onExtendLoan
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [editingMember, setEditingMember] = useState<LibraryMember | null>(null);
  const [selectedMember, setSelectedMember] = useState<LibraryMember | null>(null);
  const [printingMember, setPrintingMember] = useState<LibraryMember | null>(null);
  const [customMembershipNumber, setCustomMembershipNumber] = useState('');
  const [useCustomNumber, setUseCustomNumber] = useState(false);
  const [memberBorrowedBooks, setMemberBorrowedBooks] = useState<{[key: number]: Book[]}>({});
  const [newMember, setNewMember] = useState<CreateMemberData>({
    nom: '',
    prenom: '',
    fonction: '',
    telephone: '',
    adresse: '',
    email: ''
  });
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Confirmation modal state
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    memberId: number;
    memberName: string;
  }>({
    isOpen: false,
    memberId: 0,
    memberName: ''
  });


  const filteredMembers = members.filter(member =>
    member.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.prenom.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.fonction.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.numeroMembre.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Generate auto membership number preview
  const getNextMembershipNumber = () => {
    const nextId = Math.max(...members.map(m => m.id), 0) + 1;
    return generateMembershipNumber(nextId);
  };

  const getMemberBorrowedBooks = (memberId: number) => {
    return memberBorrowedBooks[memberId] || [];
  };

  // Fetch borrowed books for a specific member
  const fetchMemberBorrowedBooks = async (memberId: number) => {
    try {
      if (window.electronAPI && window.electronAPI.database.getMemberBorrowedBooks) {
        const books = await window.electronAPI.database.getMemberBorrowedBooks(memberId);
        setMemberBorrowedBooks(prev => ({
          ...prev,
          [memberId]: books
        }));
      }
    } catch (error) {
      console.error('Error fetching member borrowed books:', error);
    }
  };

  // Effect to fetch borrowed books when a member is selected
  useEffect(() => {
    if (selectedMember && !memberBorrowedBooks[selectedMember.id]) {
      fetchMemberBorrowedBooks(selectedMember.id);
    }
  }, [selectedMember]);

  // Photo upload handling
  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Veuillez sélectionner un fichier image (JPG, PNG, etc.)');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('La taille du fichier ne doit pas dépasser 5MB');
        return;
      }

      setPhotoFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removePhoto = () => {
    setPhotoFile(null);
    setPhotoPreview('');
  };

  const getDefaultAvatar = () => {
    if (newMember.nom && newMember.prenom) {
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(newMember.prenom + ' ' + newMember.nom)}&background=0ea5e9&color=fff&size=120`;
    }
    return `https://ui-avatars.com/api/?name=Nouveau+Membre&background=94a3b8&color=fff&size=120`;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required field validation with French messages
    if (!newMember.nom || newMember.nom.trim() === '') {
      newErrors.nom = 'Le nom est requis';
    }

    if (!newMember.prenom || newMember.prenom.trim() === '') {
      newErrors.prenom = 'Le prénom est requis';
    }

    if (!newMember.fonction || newMember.fonction.trim() === '') {
      newErrors.fonction = 'La fonction est requise';
    }

    if (!newMember.telephone || newMember.telephone.trim() === '') {
      newErrors.telephone = 'Le téléphone est requis';
    }

    if (!newMember.adresse || newMember.adresse.trim() === '') {
      newErrors.adresse = 'L\'adresse est requise';
    }

    // Email validation (optional but must be valid if provided)
    if (newMember.email && newMember.email.trim()) {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newMember.email)) {
        newErrors.email = 'Format d\'email invalide';
      }
    }

    // Custom membership number validation
    if (useCustomNumber && customMembershipNumber) {
      const existingMember = members.find(m => m.numeroMembre === customMembershipNumber);
      if (existingMember) {
        newErrors.customMembershipNumber = 'Ce numéro de membre existe déjà';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof CreateMemberData, value: string) => {
    setNewMember(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleCreateMember = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    // Always validate on submit attempt
    const isValid = validateForm();

    if (!isValid) {
      // Focus on the first required field that's empty
      const requiredFields = ['nom', 'prenom', 'fonction', 'telephone', 'adresse'];
      for (const field of requiredFields) {
        const fieldValue = newMember[field as keyof CreateMemberData];
        if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
          const element = document.getElementById(field);
          element?.focus();
          break;
        }
      }
      return;
    }

    // Handle image upload if there's a photo file
    let photoPath = getDefaultAvatar();
    if (photoFile && photoPreview) {
      try {
        // Generate filename based on member name
        const filename = `${newMember.prenom}_${newMember.nom}_${Date.now()}.jpg`
          .toLowerCase()
          .replace(/[^a-z0-9._-]/g, '_');

        // Save the image and get the path
        const savedPath = await window.electronAPI.saveUserImage(photoPreview, filename);
        if (savedPath) {
          photoPath = savedPath;
        }
      } catch (error) {
        console.error('Error saving profile image:', error);
        // Continue with default avatar if image save fails
      }
    }

    const memberData = {
      ...newMember,
      photo: photoPath,
      ...(useCustomNumber && customMembershipNumber ? { numeroMembre: customMembershipNumber } : {})
    };

    const success = await onAddMember(memberData);
    if (success) {
      setNewMember({
        nom: '',
        prenom: '',
        fonction: '',
        telephone: '',
        adresse: '',
        email: ''
      });
      setCustomMembershipNumber('');
      setUseCustomNumber(false);
      setPhotoFile(null);
      setPhotoPreview('');
      setIsCreating(false);
    } else {
      alert('Erreur lors de la création du membre');
    }
  };

  const handleDeleteMemberClick = (memberId: number, memberName: string) => {
    setConfirmationModal({
      isOpen: true,
      memberId,
      memberName
    });
  };

  const handleConfirmDeleteMember = async () => {
    const success = await onDeleteMember(confirmationModal.memberId);
    if (!success) {
      alert('Erreur lors de la suppression du membre');
    }
    setConfirmationModal({
      isOpen: false,
      memberId: 0,
      memberName: ''
    });
  };

  const handleCloseConfirmationModal = () => {
    setConfirmationModal({
      isOpen: false,
      memberId: 0,
      memberName: ''
    });
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title flex items-center gap-2">
              <UsersIcon size={24} />
              Gestion des membres
            </h2>
            <p className="card-subtitle">{filteredMembers.length} membre(s) enregistré(s)</p>
          </div>
          <div className="flex gap-2">
            <button
              className="btn btn-secondary"
              onClick={() => exportMembersToCSV(filteredMembers, 'membres.csv')}
              title="Exporter la liste des membres en CSV"
            >
              <DownloadIcon size={16} />
              Exporter CSV
            </button>
            <button
              className="btn btn-primary"
              onClick={() => setIsCreating(true)}
            >
              <PlusIcon size={16} />
              Nouveau membre
            </button>
          </div>
        </div>
      </div>

      {/* Icon Legend */}
      <div className="legend-section">
        <h3 className="legend-title">Guide des icônes d'action</h3>
        <div className="icon-legend">
          <div className="legend-item">
            <div className="legend-icon legend-icon--primary">
              <PrintIcon size={16} />
            </div>
            <span className="legend-text">Imprimer carte</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--secondary">
              <EditIcon size={16} />
            </div>
            <span className="legend-text">Modifier</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--danger">
              <TrashIcon size={16} />
            </div>
            <span className="legend-text">Supprimer</span>
          </div>
        </div>
      </div>

      <div className="card-content">
        {/* Search Bar */}
        <div className="search-bar">
          <SearchIcon className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Rechercher par nom, prénom, fonction ou numéro de membre..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Members Table */}
        <div className="table-container member-management-table-container">
          <div className="table-responsive">
            <table className="table table-mobile-stack member-management-table">
              <thead>
                <tr>
                  <th>Photo</th>
                  <th>Nom</th>
                  <th>Prénom</th>
                  <th className="hide-mobile">Fonction</th>
                  <th>Numéro de membre</th>
                  <th className="hide-mobile">Téléphone</th>
                  <th>Statut</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredMembers.map((member) => (
                  <tr
                    key={member.id}
                    onClick={() => setSelectedMember(member)}
                    style={{ cursor: 'pointer' }}
                  >
                    <td data-label="Photo">
                      <ProfileImage
                        src={member.photo}
                        alt={`${member.prenom} ${member.nom}`}
                        className="table-avatar"
                        fallbackName={`${member.prenom} ${member.nom}`}
                        size={40}
                      />
                    </td>
                    <td data-label="Nom" className="font-medium">
                      {member.nom}
                    </td>
                    <td data-label="Prénom">
                      {member.prenom}
                    </td>
                    <td data-label="Fonction" className="hide-mobile">
                      {member.fonction}
                    </td>
                    <td data-label="Numéro de membre" className="font-mono text-sm">
                      {member.numeroMembre}
                    </td>
                    <td data-label="Téléphone" className="hide-mobile">
                      {member.telephone}
                    </td>
                    <td data-label="Statut">
                      <span className={`status-badge ${member.isActive ? 'active' : 'inactive'}`}>
                        {member.isActive ? 'Actif' : 'Inactif'}
                      </span>
                    </td>
                    <td data-label="Actions">
                      <div className="action-buttons">
                        <button
                          className="action-btn action-btn--primary action-btn--sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setPrintingMember(member);
                          }}
                          title="Imprimer carte de membre"
                        >
                          <PrintIcon size={16} />
                        </button>
                        <button
                          className="action-btn action-btn--secondary action-btn--sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingMember(member);
                          }}
                          title="Modifier membre"
                        >
                          <EditIcon size={16} />
                        </button>
                        <button
                          className="action-btn action-btn--danger action-btn--sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteMemberClick(member.id, `${member.prenom} ${member.nom}`);
                          }}
                          title="Supprimer membre"
                        >
                          <TrashIcon size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredMembers.length === 0 && (
            <div className="text-center py-8 text-neutral">
              <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
              <p>Aucun membre trouvé.</p>
            </div>
          )}
        </div>
      </div>

      {/* Create Member Modal */}
      <ModalPortal isOpen={isCreating} onClose={() => setIsCreating(false)}>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>
          <div className="modal-header">
            <h3 className="modal-title">Nouveau membre</h3>
            <button className="modal-close" onClick={() => setIsCreating(false)}>×</button>
          </div>
          <div className="modal-body">
            <form onSubmit={handleCreateMember} id="create-member-form" noValidate>
              {/* Photo Upload Section */}
              <div className="form-group">
                <label className="form-label">Photo de profil</label>
                <div className="photo-upload-section">
                  <div className="photo-preview">
                    <img
                      src={photoPreview || getDefaultAvatar()}
                      alt="Aperçu de la photo"
                      className="preview-image"
                    />
                  </div>
                  <div className="photo-controls">
                    <input
                      type="file"
                      id="photo-upload"
                      accept="image/*"
                      onChange={handlePhotoUpload}
                      className="photo-input"
                    />
                    <label htmlFor="photo-upload" className="action-btn action-btn--secondary action-btn--sm">
                      📷 Choisir une photo
                    </label>
                    {photoPreview && (
                      <button
                        type="button"
                        onClick={removePhoto}
                        className="action-btn action-btn--danger action-btn--sm"
                      >
                        🗑️ Supprimer
                      </button>
                    )}
                    <p className="photo-help-text">
                      Formats acceptés: JPG, PNG (max 5MB)
                    </p>
                  </div>
                </div>
              </div>

              {/* Membership Number Section */}
              <div className="form-group">
                <label className="form-label">Numéro de membre</label>
                <div className="membership-number-section">
                  {!useCustomNumber ? (
                    <div className="auto-number-preview">
                      <span className="preview-label">Numéro automatique:</span>
                      <span className="preview-number">{getNextMembershipNumber()}</span>
                    </div>
                  ) : (
                    <input
                      type="text"
                      className="form-input"
                      value={customMembershipNumber}
                      onChange={(e) => setCustomMembershipNumber(e.target.value)}
                      placeholder="Entrez un numéro personnalisé"
                    />
                  )}
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={useCustomNumber}
                      onChange={(e) => setUseCustomNumber(e.target.checked)}
                    />
                    <span className="checkbox-text">Assigner un numéro personnalisé</span>
                  </label>
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">Nom *</label>
                  <input
                    type="text"
                    id="nom"
                    className={`form-input ${errors.nom ? 'error' : ''}`}
                    value={newMember.nom}
                    onChange={(e) => handleInputChange('nom', e.target.value)}
                    placeholder="Nom de famille"
                  />
                  {errors.nom && <span className="error-message">{errors.nom}</span>}
                </div>
                <div className="form-group">
                  <label className="form-label">Prénom *</label>
                  <input
                    type="text"
                    id="prenom"
                    className={`form-input ${errors.prenom ? 'error' : ''}`}
                    value={newMember.prenom}
                    onChange={(e) => handleInputChange('prenom', e.target.value)}
                    placeholder="Prénom"
                  />
                  {errors.prenom && <span className="error-message">{errors.prenom}</span>}
                </div>
              </div>
              <div className="form-group">
                <label className="form-label">Fonction *</label>
                <input
                  type="text"
                  id="fonction"
                  className={`form-input ${errors.fonction ? 'error' : ''}`}
                  value={newMember.fonction}
                  onChange={(e) => handleInputChange('fonction', e.target.value)}
                  placeholder="Ex: Étudiant, Professeur, Personnel..."
                />
                {errors.fonction && <span className="error-message">{errors.fonction}</span>}
              </div>
              <div className="form-group">
                <label className="form-label">Téléphone *</label>
                <input
                  type="tel"
                  id="telephone"
                  className={`form-input ${errors.telephone ? 'error' : ''}`}
                  value={newMember.telephone}
                  onChange={(e) => handleInputChange('telephone', e.target.value)}
                  placeholder="Numéro de téléphone"
                />
                {errors.telephone && <span className="error-message">{errors.telephone}</span>}
              </div>
              <div className="form-group">
                <label className="form-label">Adresse *</label>
                <textarea
                  id="adresse"
                  className={`form-input ${errors.adresse ? 'error' : ''}`}
                  rows={3}
                  value={newMember.adresse}
                  onChange={(e) => handleInputChange('adresse', e.target.value)}
                  placeholder="Adresse complète"
                />
                {errors.adresse && <span className="error-message">{errors.adresse}</span>}
              </div>
              <div className="form-group">
                <label className="form-label">Email (optionnel)</label>
                <input
                  type="email"
                  id="email"
                  className={`form-input ${errors.email ? 'error' : ''}`}
                  value={newMember.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
                {errors.email && <span className="error-message">{errors.email}</span>}
              </div>

              {/* General form error message */}
              {Object.keys(errors).length > 0 && (
                <div className="form-error-summary">
                  <p className="error-message">
                    Veuillez corriger les erreurs ci-dessus avant de soumettre le formulaire.
                  </p>
                </div>
              )}
            </form>
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="action-btn action-btn--secondary"
              onClick={() => {
                setIsCreating(false);
                setUseCustomNumber(false);
                setCustomMembershipNumber('');
                setErrors({});
                setNewMember({
                  nom: '',
                  prenom: '',
                  fonction: '',
                  telephone: '',
                  adresse: '',
                  email: ''
                });
              }}
            >
              Annuler
            </button>
            <button
              type="submit"
              form="create-member-form"
              className="action-btn action-btn--primary"
              onClick={handleCreateMember}
            >
              Créer le membre
            </button>
          </div>
          </div>
      </ModalPortal>

      {/* Edit Member Modal */}
      {editingMember && (
        <EditMemberModal
          member={editingMember}
          isOpen={!!editingMember}
          onClose={() => setEditingMember(null)}
          onSave={async (memberId, updates) => {
            const success = await onUpdateMember(memberId, updates);
            if (success) {
              setEditingMember(null);
            } else {
              alert('Erreur lors de la modification du membre');
            }
          }}
        />
      )}

      {/* Member Profile Modal */}
      {selectedMember && (
        <MemberProfile
          member={selectedMember}
          borrowedBooks={getMemberBorrowedBooks(selectedMember.id)}
          isOpen={!!selectedMember}
          onClose={() => {
            setSelectedMember(null);
            // Clear the borrowed books cache for this member to ensure fresh data next time
            setMemberBorrowedBooks(prev => {
              const updated = { ...prev };
              delete updated[selectedMember.id];
              return updated;
            });
          }}
          onReturnBook={onReturnBook}
          onExtendLoan={onExtendLoan}
        />
      )}

      {/* Print Member Card Modal */}
      {printingMember && (
        <PrintMemberCardModal
          member={printingMember}
          isOpen={!!printingMember}
          onClose={() => setPrintingMember(null)}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={handleCloseConfirmationModal}
        onConfirm={handleConfirmDeleteMember}
        title="Supprimer le membre"
        message={`Êtes-vous sûr de vouloir supprimer le membre ${confirmationModal.memberName} ? Cette action est irréversible et supprimera également tous les prêts associés.`}
        confirmText="Supprimer"
        variant="danger"
      />
    </div>
  );
};

export default MemberManagement;

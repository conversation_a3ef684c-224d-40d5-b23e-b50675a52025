import React from 'react';
import { XIcon, CheckIcon, BookIcon } from './Icons';
import ModalPortal from './ModalPortal';

interface ConfirmReturnModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  bookTitle: string;
  bookAuthor: string;
  memberName: string;
  loanStartDate: string;
  originalDueDate: string;
}

const ConfirmReturnModal: React.FC<ConfirmReturnModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  bookTitle,
  bookAuthor,
  memberName,
  loanStartDate,
  originalDueDate
}) => {
  if (!isOpen) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateLoanDuration = () => {
    const startDate = new Date(loanStartDate);
    const dueDate = new Date(originalDueDate);
    const diffTime = dueDate.getTime() - startDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <ModalPortal isOpen={isOpen} onClose={onClose}>
      <div className="modal-container" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">
            <BookIcon size={24} />
            Confirmer le retour du livre
          </h3>
          <button onClick={onClose} className="modal-close">
            <XIcon size={20} />
          </button>
        </div>

        <div className="modal-body">
          <div className="confirmation-content">
            <div className="confirmation-message">
              <p className="text-lg text-neutral-900 mb-4">
                Êtes-vous sûr de vouloir marquer ce livre comme retourné ?
              </p>
            </div>

            <div className="loan-details">
              <div className="detail-section">
                <h4 className="font-semibold text-neutral-900 mb-3">Détails du livre</h4>
                <div className="detail-grid">
                  <div className="detail-item">
                    <span className="detail-label">Titre :</span>
                    <span className="detail-value font-medium">"{bookTitle}"</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Auteur :</span>
                    <span className="detail-value font-medium">{bookAuthor}</span>
                  </div>
                  <div className="detail-item">
                    <span className="detail-label">Emprunteur :</span>
                    <span className="detail-value font-medium">{memberName}</span>
                  </div>
                </div>
              </div>

              <div className="return-details">
                <h4 className="font-semibold text-neutral-900 mb-3">Informations du prêt</h4>
                <div className="return-grid">
                  <div className="return-item">
                    <span className="return-label">Date d'emprunt :</span>
                    <span className="return-value">
                      {formatDate(loanStartDate)}
                    </span>
                  </div>
                  <div className="return-item">
                    <span className="return-label">Date d'échéance :</span>
                    <span className="return-value">
                      {formatDate(originalDueDate)}
                    </span>
                  </div>
                  <div className="return-item">
                    <span className="return-label">Durée du prêt :</span>
                    <span className="return-value font-medium">
                      {calculateLoanDuration()} jour(s)
                    </span>
                  </div>
                </div>
              </div>

              <div className="return-warning">
                <div className="warning-card">
                  <p className="text-sm text-warning-800">
                    ⚠️ <strong>Attention :</strong> Cette action marquera le livre comme disponible 
                    et le retirera de la liste des livres empruntés par ce membre.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onClose} className="action-btn action-btn--secondary">
            <XIcon size={16} />
            <span className="action-label">Annuler</span>
          </button>
          <button onClick={handleConfirm} className="action-btn action-btn--success">
            <CheckIcon size={16} />
            <span className="action-label">Confirmer le retour</span>
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmReturnModal;

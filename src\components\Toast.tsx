import React, { useEffect } from 'react';
import { CheckIcon, XIcon, AlertTriangleIcon, InfoIcon } from './Icons';

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  onClose
}) => {
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onClose(id);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [id, duration, onClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckIcon size={20} />;
      case 'error':
        return <XIcon size={20} />;
      case 'warning':
        return <AlertTriangleIcon size={20} />;
      case 'info':
        return <InfoIcon size={20} />;
      default:
        return <InfoIcon size={20} />;
    }
  };

  const getTypeClass = () => {
    switch (type) {
      case 'success':
        return 'toast--success';
      case 'error':
        return 'toast--error';
      case 'warning':
        return 'toast--warning';
      case 'info':
        return 'toast--info';
      default:
        return 'toast--info';
    }
  };

  return (
    <div className={`toast ${getTypeClass()}`}>
      <div className="toast-content">
        <div className="toast-icon">
          {getIcon()}
        </div>
        <div className="toast-text">
          <div className="toast-title">{title}</div>
          {message && <div className="toast-message">{message}</div>}
        </div>
        <button
          className="toast-close"
          onClick={() => onClose(id)}
          aria-label="Fermer la notification"
        >
          <XIcon size={16} />
        </button>
      </div>
    </div>
  );
};

export default Toast;

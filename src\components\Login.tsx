import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import UserRegistration from './UserRegistration';
import FirstTimeSetup from './FirstTimeSetup';

const Login: React.FC = () => {
  const [credentials, setCredentials] = useState({ username: '', password: '' });
  const [error, setError] = useState('');
  const [showRegistration, setShowRegistration] = useState(false);
  const [isFirstTimeSetup, setIsFirstTimeSetup] = useState<boolean | null>(null);
  const { login, isLoading, checkFirstTimeSetup, setupFirstSuperAdmin } = useAuth();

  // Check if this is first time setup
  useEffect(() => {
    const checkSetup = async () => {
      const isFirstTime = await checkFirstTimeSetup();
      setIsFirstTimeSetup(isFirstTime);
    };
    checkSetup();
  }, [checkFirstTimeSetup]);

  // Show first-time setup if no users exist
  if (isFirstTimeSetup === true) {
    return (
      <FirstTimeSetup
        onSetupComplete={async (userData) => {
          const success = await setupFirstSuperAdmin(userData);
          if (success) {
            setIsFirstTimeSetup(false);
            return true;
          }
          return false;
        }}
      />
    );
  }

  // Show registration form if requested (only after first-time setup is complete)
  if (showRegistration && isFirstTimeSetup === false) {
    return (
      <UserRegistration
        onSuccess={() => setShowRegistration(false)}
        onCancel={() => setShowRegistration(false)}
      />
    );
  }

  // Show loading state while checking setup status
  if (isFirstTimeSetup === null) {
    return (
      <div className="login-container">
        <div className="login-card">
          <div className="login-header">
            <h1 className="login-title">BiblioTech</h1>
            <p className="login-subtitle">Chargement...</p>
          </div>
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!credentials.username || !credentials.password) {
      setError('Veuillez remplir tous les champs');
      return;
    }

    console.log('🔐 Login form submitted for:', credentials.username);

    try {
      const success = await login(credentials);
      if (!success) {
        setError('Nom d\'utilisateur ou mot de passe incorrect. Vérifiez vos identifiants et réessayez.');
      }
    } catch (error) {
      console.error('❌ Login form error:', error);
      setError('Erreur de connexion. Veuillez réessayer ou contacter l\'administrateur.');
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1 className="login-title">BiblioTech</h1>
          <p className="login-subtitle">Système de gestion de bibliothèque</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="username" className="form-label">
              Nom d'utilisateur
            </label>
            <input
              id="username"
              type="text"
              className="form-input"
              value={credentials.username}
              onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
              placeholder="Entrez votre nom d'utilisateur"
              disabled={isLoading}
              autoComplete="username"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Mot de passe
            </label>
            <input
              id="password"
              type="password"
              className="form-input"
              value={credentials.password}
              onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              placeholder="Entrez votre mot de passe"
              disabled={isLoading}
              autoComplete="current-password"
            />
          </div>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <button
            type="submit"
            className="btn btn-primary login-button"
            disabled={isLoading}
          >
            {isLoading ? 'Connexion...' : 'Se connecter'}
          </button>

          {/* Only show registration link if first-time setup is complete */}
          {isFirstTimeSetup === false && (
            <div className="text-center mt-4">
              <button
                type="button"
                onClick={() => setShowRegistration(true)}
                className="text-blue-600 hover:text-blue-700 font-medium text-sm"
                disabled={isLoading}
              >
                Créer un nouveau compte
              </button>
            </div>
          )}
        </form>


      </div>
    </div>
  );
};

export default Login;

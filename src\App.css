/* Library Management System - Component Styles */

/* Layout Components */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-neutral-50) 0%, var(--color-primary-50) 100%);
}

.app-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
}

.sidebar {
  background: var(--color-neutral-900);
  color: var(--color-neutral-100);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: 10; /* Ensure sidebar is below modals */
}

.sidebar-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-700);
}

.sidebar-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-400);
  margin-bottom: var(--space-2);
}

.sidebar-subtitle {
  font-size: var(--text-sm);
  color: var(--color-neutral-400);
}

.main-content {
  padding: var(--space-8);
  overflow-y: auto;
  /* Create a stable stacking context */
  position: relative;
  z-index: 1;
}

/* Navigation */
.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}



.nav-item {
  margin-bottom: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  color: var(--color-neutral-300);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: var(--font-medium);
}

.nav-link:hover {
  background: var(--color-neutral-800);
  color: var(--color-neutral-100);
  text-decoration: none;
  /* Use will-change instead of transform to prevent modal interference */
  will-change: background-color, color;
}

.nav-link.active {
  background: var(--color-primary-600);
  color: white;
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--color-neutral-600);
}

/* Cards */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
  background: white;
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-1);
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
}

.card-content {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-4) var(--space-6);
  background: var(--color-neutral-50);
  border-top: 1px solid var(--color-neutral-200);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 1px solid transparent;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn-primary {
  background: var(--color-primary-600);
  color: white;
}

.btn-primary:hover {
  background: var(--color-primary-700);
  text-decoration: none;
  color: white;
}

.btn-secondary {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border-color: var(--color-neutral-300);
}

.btn-secondary:hover {
  background: var(--color-neutral-200);
  text-decoration: none;
  color: var(--color-neutral-800);
}

.btn-success {
  background: var(--color-success-600);
  color: white;
}

.btn-success:hover {
  background: var(--color-success-700);
  text-decoration: none;
  color: white;
}

.btn-warning {
  background: var(--color-warning-600);
  color: white;
}

.btn-warning:hover {
  background: var(--color-warning-700);
  text-decoration: none;
  color: white;
}

.btn-error {
  background: var(--color-error-600);
  color: white;
}

.btn-error:hover {
  background: var(--color-error-700);
  text-decoration: none;
  color: white;
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-5);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
  margin-bottom: var(--space-2);
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  background: white;
}

.form-input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.form-input:invalid {
  border-color: var(--color-error-500);
}

.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background: white;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.form-select:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.form-input-readonly {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  font-size: var(--text-base);
  cursor: not-allowed;
}

.form-help {
  font-size: var(--text-xs);
  color: var(--color-neutral-600);
  margin-top: var(--space-1);
}

/* Quantity and status display styles */
.status-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.quantity-info {
  margin-top: var(--space-1);
}

.quantity-info .text-muted {
  font-size: var(--text-xs);
  color: var(--color-neutral-500);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
  padding-top: var(--space-6);
  border-top: 1px solid var(--color-neutral-200);
}

/* Tables */
.table-container {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
  width: 100%; /* Ensure container uses full available width */
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Member management table container - remove horizontal scrolling */
.member-management-table-container {
  width: 100%; /* Use full available width */
}

.member-management-table-container .table-responsive {
  overflow-x: visible; /* Remove horizontal scrolling */
  overflow-y: visible; /* Allow natural content flow */
  width: 100%; /* Ensure responsive container uses full width */
  -webkit-overflow-scrolling: touch;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
  min-width: 100%;
}

/* Responsive table that uses full container width without horizontal scrolling */
.member-management-table {
  width: 100% !important; /* Force full container width */
  min-width: 100% !important; /* Ensure minimum width fills container */
  table-layout: fixed; /* Fixed layout for consistent column distribution */
  border-collapse: collapse;
}

.table--compact {
  min-width: 100%;
}

.table--full-width {
  min-width: 100%;
}

/* Table column width optimization */
.table th,
.table td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Specific column width controls */
.table th:nth-child(1), .table td:nth-child(1) { width: 20%; } /* First column (Photo/Livre) */
.table th:nth-child(2), .table td:nth-child(2) { width: 15%; } /* Second column (Nom/Emprunteur) */
.table th:nth-child(3), .table td:nth-child(3) { width: 15%; } /* Third column */
.table th:nth-child(4), .table td:nth-child(4) { width: 15%; } /* Fourth column */
.table th:nth-child(5), .table td:nth-child(5) { width: 15%; } /* Fifth column */
.table th:nth-child(6), .table td:nth-child(6) { width: 20%; } /* Actions column */

/* Allow text wrapping for specific content */
.table td .font-medium,
.table td .text-sm {
  white-space: normal;
  word-break: break-word;
}

/* Specific content handling for different table types */
.book-management-table td:nth-child(1),
.book-management-table td:nth-child(2) {
  white-space: normal;
  word-break: break-word;
  overflow: visible;
  text-overflow: initial;
}

.book-management-table td:nth-child(5),
.member-management-table td:nth-child(7) {
  white-space: nowrap;
  overflow: visible;
  text-overflow: initial;
}

.member-management-table td:nth-child(1),
.member-management-table td:nth-child(2),
.member-management-table td:nth-child(3) {
  white-space: normal;
  word-break: break-word;
  overflow: visible;
  text-overflow: initial;
}

/* Critical content columns that need full visibility */
.member-management-table td:nth-child(4) { /* Fonction */
  white-space: normal;
  word-break: break-word;
  overflow: visible;
  text-overflow: initial;
}

.member-management-table td:nth-child(5) { /* Numéro de membre */
  white-space: nowrap !important;
  overflow: visible;
  text-overflow: initial;
}

.member-management-table td:nth-child(6) { /* Téléphone */
  white-space: nowrap !important;
  overflow: visible;
  text-overflow: initial;
}

/* Improved text handling for member names */
.member-management-table td:nth-child(2), /* Nom */
.member-management-table td:nth-child(3) { /* Prénom */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 0; /* Forces ellipsis to work with table-layout: fixed */
}

.member-management-table td:nth-child(2):hover,
.member-management-table td:nth-child(3):hover {
  white-space: normal;
  overflow: visible;
  text-overflow: initial;
}

.loan-tracking-table td:nth-child(3), /* Date d'emprunt */
.loan-tracking-table td:nth-child(4) { /* Date de retour */
  white-space: nowrap;
  overflow: visible;
  text-overflow: initial;
}

/* CRITICAL FIX: Ensure table headers display horizontally */
.member-management-table th,
.loan-tracking-table th,
.book-management-table th {
  white-space: nowrap !important;
  overflow: visible;
  text-overflow: initial;
  word-break: normal !important;
}

/* Ensure specific columns in optimized tables don't get truncated */
.member-management-table th:nth-child(4), /* Fonction header */
.member-management-table th:nth-child(5), /* Numéro de membre header */
.member-management-table th:nth-child(6), /* Téléphone header */
.loan-tracking-table th:nth-child(3), /* Date d'emprunt header */
.loan-tracking-table th:nth-child(4) { /* Date de retour header */
  white-space: nowrap !important;
  overflow: visible;
  text-overflow: initial;
  word-break: normal !important;
}

.loan-tracking-table td:nth-child(1) .font-medium,
.loan-tracking-table td:nth-child(1) .text-sm {
  white-space: normal;
  word-break: break-word;
}

.loan-tracking-table td:nth-child(2) {
  white-space: normal;
  overflow: visible;
}

/* User Management table specific styles */
.user-management-table {
  table-layout: fixed;
}

.user-management-table th:nth-child(1),
.user-management-table td:nth-child(1) { width: 20%; } /* Utilisateur */
.user-management-table th:nth-child(2),
.user-management-table td:nth-child(2) { width: 25%; } /* Email */
.user-management-table th:nth-child(3),
.user-management-table td:nth-child(3) { width: 15%; } /* Rôle */
.user-management-table th:nth-child(4),
.user-management-table td:nth-child(4) { width: 10%; } /* Statut */
.user-management-table th:nth-child(5),
.user-management-table td:nth-child(5) { width: 15%; } /* Dernière connexion */
.user-management-table th:nth-child(6),
.user-management-table td:nth-child(6) { width: 15%; } /* Actions */

/* Book management table specific styles */
.book-management-table {
  table-layout: fixed;
}

/* Default column widths for all environments */
.book-management-table th:nth-child(1),
.book-management-table td:nth-child(1) { width: 22%; } /* Titre */
.book-management-table th:nth-child(2),
.book-management-table td:nth-child(2) { width: 18%; } /* Auteur */
.book-management-table th:nth-child(3),
.book-management-table td:nth-child(3) { width: 13%; } /* Catégorie */
.book-management-table th:nth-child(4),
.book-management-table td:nth-child(4) { width: 13%; } /* Genre */
.book-management-table th:nth-child(5),
.book-management-table td:nth-child(5) { width: 16%; } /* Statut - Increased for full text */
.book-management-table th:nth-child(6),
.book-management-table td:nth-child(6) {
  width: 18%; /* Actions */
  vertical-align: middle;
  padding: var(--space-3);
}

/* Browser-specific compact layout optimization */
/* Target web browsers to eliminate excessive white space */
.book-management-table.browser-optimized {
  table-layout: auto;
  width: 100%;
  border-collapse: collapse;
}

/* Aggressive space optimization for browsers */
.book-management-table.browser-optimized th,
.book-management-table.browser-optimized td {
  padding: var(--space-2) var(--space-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.book-management-table.browser-optimized th:nth-child(1),
.book-management-table.browser-optimized td:nth-child(1) {
  width: 26%;
  max-width: 200px;
  white-space: normal; /* Allow title wrapping */
} /* Titre */
.book-management-table.browser-optimized th:nth-child(2),
.book-management-table.browser-optimized td:nth-child(2) {
  width: 20%;
  max-width: 150px;
} /* Auteur */
.book-management-table.browser-optimized th:nth-child(3),
.book-management-table.browser-optimized td:nth-child(3) {
  width: 11%;
  max-width: 100px;
} /* Catégorie */
.book-management-table.browser-optimized th:nth-child(4),
.book-management-table.browser-optimized td:nth-child(4) {
  width: 11%;
  max-width: 100px;
} /* Genre */
.book-management-table.browser-optimized th:nth-child(5),
.book-management-table.browser-optimized td:nth-child(5) {
  width: 14%;
  max-width: 120px;
} /* Statut - Compact */
.book-management-table.browser-optimized th:nth-child(6),
.book-management-table.browser-optimized td:nth-child(6) {
  width: 18%;
  min-width: 140px;
  max-width: 180px;
  padding-left: var(--space-1);
  padding-right: var(--space-1);
  white-space: normal; /* Allow action buttons to wrap if needed */
}

/* Alternative: Use CSS-only browser detection */
/* This targets browsers that support CSS Grid (most modern browsers) */
@supports (display: grid) {
  /* Only apply if not in Electron context */
  .book-management-table:not(.electron-app) {
    table-layout: auto;
  }

  .book-management-table:not(.electron-app) th:nth-child(1),
  .book-management-table:not(.electron-app) td:nth-child(1) { width: 24%; } /* Titre */
  .book-management-table:not(.electron-app) th:nth-child(2),
  .book-management-table:not(.electron-app) td:nth-child(2) { width: 19%; } /* Auteur */
  .book-management-table:not(.electron-app) th:nth-child(3),
  .book-management-table:not(.electron-app) td:nth-child(3) { width: 12%; } /* Catégorie */
  .book-management-table:not(.electron-app) th:nth-child(4),
  .book-management-table:not(.electron-app) td:nth-child(4) { width: 12%; } /* Genre */
  .book-management-table:not(.electron-app) th:nth-child(5),
  .book-management-table:not(.electron-app) td:nth-child(5) { width: 15%; } /* Statut - Compact */
  .book-management-table:not(.electron-app) th:nth-child(6),
  .book-management-table:not(.electron-app) td:nth-child(6) {
    width: 18%;
    min-width: 140px;
    padding-left: var(--space-2);
    padding-right: var(--space-2);
  }
}

/* Member management table specific styles - optimized layout */

/* Optimized column widths for full visibility - total 100% */
.member-management-table th:nth-child(1), /* Photo */
.member-management-table td:nth-child(1) {
  width: 7%;
  text-align: center;
}

.member-management-table th:nth-child(2), /* Nom */
.member-management-table td:nth-child(2) {
  width: 13%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-management-table th:nth-child(3), /* Prénom */
.member-management-table td:nth-child(3) {
  width: 12%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-management-table th:nth-child(4), /* Fonction */
.member-management-table td:nth-child(4) {
  width: 15%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.member-management-table th:nth-child(5), /* Numéro de membre */
.member-management-table td:nth-child(5) {
  width: 18%;
  text-align: center;
  font-family: monospace;
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: initial !important;
}

.member-management-table th:nth-child(6), /* Téléphone */
.member-management-table td:nth-child(6) {
  width: 15%;
  text-align: left;
  font-family: monospace;
  font-size: var(--text-sm);
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow: visible;
  text-overflow: initial;
}

.member-management-table th:nth-child(7), /* Statut */
.member-management-table td:nth-child(7) {
  width: 8%;
  text-align: center;
}

.member-management-table th:nth-child(8), /* Actions */
.member-management-table td:nth-child(8) {
  width: 13%;
  text-align: center;
  background: white;
}

/* Loan tracking table specific styles */
.loan-tracking-table {
  table-layout: fixed;
}

.loan-tracking-table th:nth-child(1),
.loan-tracking-table td:nth-child(1) { width: 21%; } /* Livre - Slightly reduced for action space */
.loan-tracking-table th:nth-child(2),
.loan-tracking-table td:nth-child(2) { width: 17%; } /* Emprunteur - Slightly reduced */
.loan-tracking-table th:nth-child(3),
.loan-tracking-table td:nth-child(3) { width: 15%; } /* Date d'emprunt - Maintained */
.loan-tracking-table th:nth-child(4),
.loan-tracking-table td:nth-child(4) { width: 15%; } /* Date de retour - Maintained */
.loan-tracking-table th:nth-child(5),
.loan-tracking-table td:nth-child(5) { width: 12%; } /* Statut - Maintained */
.loan-tracking-table th:nth-child(6),
.loan-tracking-table td:nth-child(6) { width: 20%; } /* Actions - Increased for "Retourner" text */

/* Browser-specific compact layout optimization for loan tracking table */
/* Target web browsers to eliminate excessive white space and make Statut column fully visible */
.loan-tracking-table.browser-optimized {
  table-layout: auto;
  width: 100%;
  border-collapse: collapse;
}

/* Aggressive space optimization for browsers - focus on Statut column visibility */
.loan-tracking-table.browser-optimized th,
.loan-tracking-table.browser-optimized td {
  padding: var(--space-2) var(--space-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loan-tracking-table.browser-optimized th:nth-child(1),
.loan-tracking-table.browser-optimized td:nth-child(1) {
  width: 22%;
  max-width: 180px;
  white-space: normal; /* Allow book title wrapping */
} /* Livre */
.loan-tracking-table.browser-optimized th:nth-child(2),
.loan-tracking-table.browser-optimized td:nth-child(2) {
  width: 16%;
  max-width: 140px;
  white-space: normal; /* Allow borrower name wrapping */
} /* Emprunteur */
.loan-tracking-table.browser-optimized th:nth-child(3),
.loan-tracking-table.browser-optimized td:nth-child(3) {
  width: 13%;
  max-width: 110px;
} /* Date d'emprunt */
.loan-tracking-table.browser-optimized th:nth-child(4),
.loan-tracking-table.browser-optimized td:nth-child(4) {
  width: 13%;
  max-width: 110px;
} /* Date de retour */
.loan-tracking-table.browser-optimized th:nth-child(5),
.loan-tracking-table.browser-optimized td:nth-child(5) {
  width: 16%;
  max-width: 140px;
  min-width: 120px; /* Ensure Statut column is fully visible */
  white-space: normal; /* Allow status text to wrap if needed */
  padding-left: var(--space-2);
  padding-right: var(--space-2);
} /* Statut - CRITICAL: Ensure full visibility */
.loan-tracking-table.browser-optimized th:nth-child(6),
.loan-tracking-table.browser-optimized td:nth-child(6) {
  width: 20%;
  min-width: 160px;
  max-width: 200px;
  padding-left: var(--space-1);
  padding-right: var(--space-1);
  white-space: normal; /* Allow action buttons to wrap if needed */
}

.table th {
  background: var(--color-neutral-50);
  padding: var(--space-3) var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--color-neutral-700);
  border-bottom: 1px solid var(--color-neutral-200);
  position: relative;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
}

/* Ensure member management table headers align with their content */
.member-management-table th:nth-child(1) { text-align: center; } /* Photo header */
.member-management-table th:nth-child(5) {
  text-align: center; /* Member number header */
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: initial !important;
}
.member-management-table th:nth-child(7) { text-align: center; } /* Status header */
.member-management-table th:nth-child(8) {
  text-align: center; /* Actions header */
  background: var(--color-neutral-50);
}

.table th:hover {
  background: var(--color-neutral-100);
}

.table th.sortable::after {
  content: '↕';
  position: absolute;
  right: var(--space-3);
  opacity: 0.5;
  font-size: var(--text-xs);
}

.table th.sort-asc::after {
  content: '↑';
  opacity: 1;
}

.table th.sort-desc::after {
  content: '↓';
  opacity: 1;
}

.table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--color-neutral-100);
  vertical-align: middle;
  text-align: left; /* Default left alignment for text content */
}

/* Specific alignment overrides for member management table */
.member-management-table td {
  vertical-align: middle;
  height: 60px; /* Consistent row height */
}

/* Ensure proper alignment for different content types */
.member-management-table .table-avatar {
  display: block;
  margin: 0 auto; /* Center the avatar image */
}

.member-management-table .status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.member-management-table .action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  flex-wrap: nowrap;
  min-width: 100px; /* Ensure sufficient width for both action buttons */
}

/* Enhanced visual styling for member management table */
.member-management-table tr {
  transition: all var(--transition-fast);
}

.member-management-table tr:hover {
  background: var(--color-primary-25);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Ensure action column maintains background on hover */
.member-management-table tr:hover td:nth-child(8) {
  background: var(--color-primary-25);
}

/* Expand truncated text on hover */
.member-management-table td:nth-child(2):hover,
.member-management-table td:nth-child(3):hover,
.member-management-table td:nth-child(4):hover {
  position: relative;
  overflow: visible;
  white-space: normal;
  z-index: 10;
}

.member-management-table .table-avatar {
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.member-management-table tr:hover .table-avatar {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Improved typography for member data */
.member-management-table td:nth-child(2),
.member-management-table td:nth-child(3) {
  font-weight: var(--font-medium);
}

/* Consistent typography for all text columns */
.member-management-table td {
  font-family: inherit; /* Use default font family for all columns */
}

.member-management-table td:nth-child(5) {
  font-size: var(--text-sm);
  letter-spacing: 0.5px;
  font-weight: var(--font-medium);
}

/* Ensure telephone numbers display as single lines - consolidated with main column definition */

/* Better spacing for status badges */
.member-management-table .status-badge {
  min-width: 60px;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tr:hover {
  background: var(--color-neutral-50);
}

.table tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-success {
  background: var(--color-success-100);
  color: var(--color-success-700);
}

.badge-warning {
  background: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge-error {
  background: var(--color-error-100);
  color: var(--color-error-700);
}

.badge-neutral {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
}

/* User Cards */
.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
}

.user-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.user-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.enhanced-user-card {
  min-height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto var(--space-4);
  border: 3px solid var(--color-primary-100);
  /* Ensure cross-platform image rendering */
  display: block;
  background-color: var(--color-neutral-100);
  /* Debug: ensure images are visible */
  opacity: 1;
  visibility: visible;
}

.table-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-primary-100);
  /* Ensure cross-platform image rendering */
  display: block;
  background-color: var(--color-neutral-100);
  /* Debug: ensure images are visible */
  opacity: 1;
  visibility: visible;
}

.user-name {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.user-info {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
  margin-bottom: var(--space-3);
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
  justify-content: center;
}

.user-action-hint {
  font-size: var(--text-xs);
  color: var(--color-neutral-500);
  font-style: italic;
  margin-top: auto;
}

/* Search and Filters */
.search-bar {
  position: relative;
  margin-bottom: var(--space-6);
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-4) var(--space-3) var(--space-12);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background: white;
  transition: all var(--transition-fast);
}

.search-input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.search-icon {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--color-neutral-400);
}

.filter-bar {
  display: flex;
  gap: var(--space-4);
  align-items: center;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
  white-space: nowrap;
}

.filter-select {
  min-width: 150px;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background: white;
}

/* Category Pills */
.category-pills {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
  margin-bottom: var(--space-6);
}

.category-pill {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 2px solid var(--color-neutral-200);
  background: white;
  color: var(--color-neutral-700);
}

.category-pill:hover {
  border-color: var(--color-primary-300);
  background: var(--color-primary-50);
}

.category-pill.active {
  border-color: var(--color-primary-500);
  background: var(--color-primary-500);
  color: white;
}

/* Dashboard Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-neutral-200);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-fast);
}

.stat-card--clickable {
  cursor: pointer;
}

.stat-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-300);
}

.stat-card--clickable:active {
  transform: translateY(0);
}

.stat-card--active {
  border-color: var(--color-primary-500);
  background: var(--color-primary-50);
  box-shadow: var(--shadow-lg);
}

.stat-card--active .stat-value {
  color: var(--color-primary-700);
}

/* Filter badge for active filters */
.filter-badge {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  border: 1px solid var(--color-primary-200);
}

.clear-filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-neutral-200);
  color: var(--color-neutral-600);
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all var(--transition-fast);
}

.clear-filter-btn:hover {
  background: var(--color-error-100);
  color: var(--color-error-600);
}

.stat-value {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-600);
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Action Buttons - Base styling (overridden by specific table styles) */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
  align-items: center;
  box-sizing: border-box;
}

.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-300);
  background: white;
  color: var(--color-neutral-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.icon-button:hover {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-400);
  color: var(--color-neutral-700);
}

.icon-button.danger:hover {
  background: var(--color-error-50);
  border-color: var(--color-error-300);
  color: var(--color-error-600);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-bold); }
.font-semibold { font-weight: var(--font-semibold); }
.font-medium { font-weight: var(--font-medium); }

.text-primary { color: var(--color-primary-600); }
.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }
.text-neutral { color: var(--color-neutral-600); }
.text-sm { font-size: var(--text-sm); }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.rounded-full { border-radius: 50%; }
.object-cover { object-fit: cover; }
.mx-auto { margin-left: auto; margin-right: auto; }
.opacity-50 { opacity: 0.5; }
.space-y-1 > * + * { margin-top: var(--space-1); }

/* Keyboard shortcut styling */
kbd {
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-sm);
  padding: var(--space-1) var(--space-2);
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
}

/* Additional utility classes */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.max-h-32 {
  max-height: 8rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

.space-y-3 > * + * {
  margin-top: var(--space-3);
}

.bg-neutral-50 {
  background-color: var(--color-neutral-50);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.border-t {
  border-top-width: 1px;
}

.border-neutral-700 {
  border-color: var(--color-neutral-700);
}

.bg-primary-500 {
  background-color: var(--color-primary-500);
}

.text-white {
  color: white;
}

.text-neutral-100 {
  color: var(--color-neutral-100);
}

.text-neutral-300 {
  color: var(--color-neutral-300);
}

.text-neutral-400 {
  color: var(--color-neutral-400);
}

.hover\:text-neutral-100:hover {
  color: var(--color-neutral-100);
}

.hover\:bg-neutral-800:hover {
  background-color: var(--color-neutral-800);
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.w-full {
  width: 100%;
}

.p-2 {
  padding: var(--space-2);
}

.pt-6 {
  padding-top: var(--space-6);
}

.mt-auto {
  margin-top: auto;
}

.space-y-1 > * + * {
  margin-top: var(--space-1);
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.opacity-50 {
  opacity: 0.5;
}

.font-mono {
  font-family: var(--font-family-mono);
}

/* Login Styles */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-800) 100%);
  padding: var(--space-4);
}

.login-card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-8);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.login-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--color-primary-600);
  margin-bottom: var(--space-2);
}

.login-subtitle {
  color: var(--color-neutral-600);
  font-size: var(--text-base);
}

.login-form {
  margin-bottom: var(--space-6);
}

.login-button {
  width: 100%;
  padding: var(--space-4);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
}

.error-message {
  background: var(--color-error-50);
  color: var(--color-error-700);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-error-200);
  margin-bottom: var(--space-4);
  font-size: var(--text-sm);
}

.login-demo-info {
  border-top: 1px solid var(--color-neutral-200);
  padding-top: var(--space-6);
  font-size: var(--text-sm);
}

.login-demo-info h4 {
  margin-bottom: var(--space-4);
  color: var(--color-neutral-700);
}

.demo-accounts {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.demo-account {
  background: var(--color-neutral-50);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-200);
}

.demo-account code {
  background: var(--color-neutral-200);
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
}

/* Import Modal Styles - Removed duplicate, using unified modal styles below */

.modal {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
}

.modal-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-2);
}

.modal-subtitle {
  color: var(--color-neutral-600);
  font-size: var(--text-sm);
}

.modal-content {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-neutral-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* File Upload Styles */
.file-upload-area {
  border: 2px dashed var(--color-neutral-300);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--color-primary-400);
  background: var(--color-primary-50);
}

.file-upload-area.dragover {
  border-color: var(--color-primary-500);
  background: var(--color-primary-100);
}

.file-upload-icon {
  margin: 0 auto var(--space-4);
  color: var(--color-neutral-400);
}

.file-upload-text {
  color: var(--color-neutral-600);
  margin-bottom: var(--space-2);
}

.file-upload-hint {
  color: var(--color-neutral-500);
  font-size: var(--text-sm);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-neutral-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--color-primary-500);
  transition: width var(--transition-normal);
}

/* Import Preview Table */
.preview-table-container {
  max-height: 400px;
  overflow: auto;
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.preview-table th,
.preview-table td {
  padding: var(--space-2) var(--space-3);
  border-bottom: 1px solid var(--color-neutral-100);
  text-align: left;
}

.preview-table th {
  background: var(--color-neutral-50);
  font-weight: var(--font-semibold);
  position: sticky;
  top: 0;
}

.preview-table .error-row {
  background: var(--color-error-50);
}

.preview-table .error-cell {
  color: var(--color-error-600);
  font-weight: var(--font-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .app-layout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    display: none;
  }

  .main-content {
    padding: var(--space-4);
  }
}

/* Large screens - maintain responsive percentage-based layout */
@media (min-width: 1400px) {
  .member-management-table {
    width: 100%; /* Keep responsive behavior on large screens */
  }

  /* Column widths optimized for large screens */
  .member-management-table th:nth-child(1),
  .member-management-table td:nth-child(1) { width: 7%; }
  .member-management-table th:nth-child(2),
  .member-management-table td:nth-child(2) { width: 16%; }
  .member-management-table th:nth-child(3),
  .member-management-table td:nth-child(3) { width: 10%; }
  .member-management-table th:nth-child(4),
  .member-management-table td:nth-child(4) { width: 18%; }
  .member-management-table th:nth-child(5),
  .member-management-table td:nth-child(5) { width: 12%; }
  .member-management-table th:nth-child(6),
  .member-management-table td:nth-child(6) { width: 16%; }
  .member-management-table th:nth-child(7),
  .member-management-table td:nth-child(7) { width: 8%; }
  .member-management-table th:nth-child(8),
  .member-management-table td:nth-child(8) { width: 13%; }
}

/* Medium screens - optimize table layout while keeping telephone visible */
@media (max-width: 1200px) and (min-width: 1025px) {
  .table th,
  .table td {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }

  /* Member management table responsive adjustments - medium screens */
  .member-management-table th:nth-child(1),
  .member-management-table td:nth-child(1) { width: 8%; }
  .member-management-table th:nth-child(2),
  .member-management-table td:nth-child(2) { width: 11%; }
  .member-management-table th:nth-child(3),
  .member-management-table td:nth-child(3) { width: 10%; }
  .member-management-table th:nth-child(4),
  .member-management-table td:nth-child(4) { width: 13%; }
  .member-management-table th:nth-child(5),
  .member-management-table td:nth-child(5) { width: 15%; }
  .member-management-table th:nth-child(6),
  .member-management-table td:nth-child(6) { width: 13%; } /* Keep telephone visible */
  .member-management-table th:nth-child(7),
  .member-management-table td:nth-child(7) { width: 8%; }
  .member-management-table th:nth-child(8),
  .member-management-table td:nth-child(8) {
    width: 22%;
    background: white;
  }

  /* Adjust column widths for other tables */
  .table th:nth-child(1), .table td:nth-child(1) { width: 25%; }
  .table th:nth-child(2), .table td:nth-child(2) { width: 20%; }
  .table th:nth-child(3), .table td:nth-child(3) { width: 15%; }
  .table th:nth-child(4), .table td:nth-child(4) { width: 15%; }
  .table th:nth-child(5), .table td:nth-child(5) { width: 10%; }
  .table th:nth-child(6), .table td:nth-child(6) { width: 15%; }
}

/* Tablet responsive design */
@media (max-width: 1024px) {
  /* Member management table tablet optimizations */
  .member-management-table th:nth-child(4),
  .member-management-table td:nth-child(4) {
    display: none; /* Hide function column on tablets */
  }

  /* Member management table tablet adjustments */
  .member-management-table th:nth-child(1),
  .member-management-table td:nth-child(1) { width: 10%; }
  .member-management-table th:nth-child(2),
  .member-management-table td:nth-child(2) { width: 15%; }
  .member-management-table th:nth-child(3),
  .member-management-table td:nth-child(3) { width: 13%; }
  .member-management-table th:nth-child(5),
  .member-management-table td:nth-child(5) { width: 18%; }
  .member-management-table th:nth-child(6),
  .member-management-table td:nth-child(6) { width: 16%; } /* Keep telephone visible on tablets */
  .member-management-table th:nth-child(7),
  .member-management-table td:nth-child(7) { width: 10%; }
  .member-management-table th:nth-child(8),
  .member-management-table td:nth-child(8) {
    width: 18%;
    background: white;
  }
}

/* Small tablet responsive design - hide telephone column */
@media (max-width: 900px) {
  .member-management-table th:nth-child(6),
  .member-management-table td:nth-child(6) {
    display: none; /* Hide telephone column on small tablets */
  }

  /* Redistribute widths when telephone is hidden */
  .member-management-table th:nth-child(1),
  .member-management-table td:nth-child(1) { width: 12%; }
  .member-management-table th:nth-child(2),
  .member-management-table td:nth-child(2) { width: 18%; }
  .member-management-table th:nth-child(3),
  .member-management-table td:nth-child(3) { width: 16%; }
  .member-management-table th:nth-child(5),
  .member-management-table td:nth-child(5) { width: 22%; }
  .member-management-table th:nth-child(7),
  .member-management-table td:nth-child(7) { width: 12%; }
  .member-management-table th:nth-child(8),
  .member-management-table td:nth-child(8) { width: 20%; }
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .user-grid {
    grid-template-columns: 1fr;
  }

  .modal {
    margin: var(--space-4);
    max-height: calc(100vh - 2rem);
  }

  .modal-container {
    width: 95%;
    max-width: 95vw;
    max-height: 90vh;
    /* Maintain perfect centering on mobile */
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  .modal-content {
    width: 95%;
    max-width: 95vw;
    max-height: 90vh;
    /* Maintain perfect centering on mobile */
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  .login-card {
    padding: var(--space-6);
  }

  /* Responsive table styles */
  .table-container {
    margin: 0 -var(--space-4);
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .table {
    min-width: 100%;
    font-size: var(--text-xs);
  }

  .table th,
  .table td {
    padding: var(--space-2) var(--space-3);
  }

  /* Hide less important columns on mobile */
  .table .hide-mobile {
    display: none;
  }

  /* Member management table mobile specific adjustments */
  .member-management-table td {
    height: auto;
    padding: var(--space-3) var(--space-2);
  }

  .member-management-table .table-avatar {
    width: 32px;
    height: 32px;
  }

  /* Action buttons in tables */
  .action-buttons {
    gap: var(--space-1);
    flex-wrap: wrap;
    justify-content: center;
  }

  .action-btn {
    min-width: 32px;
    height: 32px;
    padding: var(--space-1);
  }

  /* Loan tracking table mobile optimizations */

  .loan-tracking-table .action-btn--md {
    min-width: 32px;
    width: auto;
    padding: var(--space-1);
  }

  /* Book management table mobile optimizations */
  .book-management-table .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  /* Member management table mobile optimizations */
  .member-management-table .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  /* Loan tracking table mobile optimizations */
  .loan-tracking-table .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  /* Stack action buttons vertically on very small screens */
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Table-specific action button optimizations */
/* Industry best practice: Use CSS Grid for predictable cross-platform layout */
.book-management-table .action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(32px, max-content));
  gap: var(--space-1);
  align-items: center;
  justify-content: start;
  width: 100%;
  min-height: 36px;
  /* Fallback for older browsers */
  display: flex;
  flex-wrap: wrap;
}

/* Modern browsers will use grid, older browsers will use flex */
@supports (display: grid) {
  .book-management-table .action-buttons {
    display: grid;
  }
}

.book-management-table .action-btn {
  min-width: 32px;
  height: 32px;
  padding: var(--space-1);
  flex-shrink: 0;
  box-sizing: border-box;
}

/* Browser-specific action button optimization */
.book-management-table.browser-optimized .action-buttons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: var(--space-1);
  flex-wrap: nowrap; /* Prevent wrapping to save space */
  width: 100%;
}

.book-management-table.browser-optimized .action-btn {
  min-width: 28px;
  height: 28px;
  padding: 4px;
  font-size: 14px;
  flex-shrink: 0;
}

.member-management-table .action-buttons {
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: var(--space-1);
}

.member-management-table .action-btn {
  min-width: 32px;
  height: 32px;
  padding: var(--space-1);
}

.loan-tracking-table .action-buttons {
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: var(--space-1);
}

.loan-tracking-table .action-btn {
  min-width: 32px;
  height: 32px;
  padding: var(--space-1);
}

/* Ensure action button text doesn't get truncated */
.loan-tracking-table .action-btn,
.member-management-table .action-btn,
.book-management-table .action-btn {
  white-space: nowrap;
  overflow: visible;
  text-overflow: initial;
}

.loan-tracking-table .action-buttons {
  justify-content: flex-start;
  gap: var(--space-1);
}

.loan-tracking-table .action-btn--md {
  padding: var(--space-1) var(--space-2);
  min-width: auto;
  height: 32px;
  font-size: var(--text-xs);
}



/* Browser-specific action button optimization for loan tracking table */
.loan-tracking-table.browser-optimized .action-buttons {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: var(--space-1);
  flex-wrap: nowrap; /* Prevent wrapping to save space */
  width: 100%;
}

.loan-tracking-table.browser-optimized .action-btn--md {
  min-width: 70px;
  height: 28px;
  padding: 4px 6px;
  font-size: 12px;
  flex-shrink: 0;
}



/* Browser-specific status badge optimization for loan tracking table */
.loan-tracking-table.browser-optimized .status-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure status badges are fully visible in browser */
.loan-tracking-table.browser-optimized .status-badge.overdue {
  background-color: var(--color-error-100);
  color: var(--color-error-700);
  border: 1px solid var(--color-error-200);
}

.loan-tracking-table.browser-optimized .status-badge.warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-700);
  border: 1px solid var(--color-warning-200);
}

.loan-tracking-table.browser-optimized .status-badge.active {
  background-color: var(--color-success-100);
  color: var(--color-success-700);
  border: 1px solid var(--color-success-200);
}

/* Medium screen optimizations */
@media (max-width: 1024px) {
  .loan-tracking-table .action-buttons {
    flex-direction: column;
    gap: var(--space-1);
  }

  .loan-tracking-table .action-btn--md {
    width: 100%;
    justify-content: flex-start;
    height: 28px;
    padding: var(--space-1);
  }

  .book-management-table .action-buttons {
    flex-direction: column;
    gap: var(--space-1);
  }

  .member-management-table .action-buttons {
    flex-direction: column;
    gap: var(--space-1);
  }

  .loan-tracking-table .action-buttons {
    flex-direction: column;
    gap: var(--space-1);
  }
}

/* Very small screens - stack table cells */
@media (max-width: 480px) {
  .table-mobile-stack {
    display: block;
  }

  .table-mobile-stack thead {
    display: none;
  }

  .table-mobile-stack tbody,
  .table-mobile-stack tr,
  .table-mobile-stack td {
    display: block;
    width: 100%;
  }

  .table-mobile-stack tr {
    border: 1px solid var(--color-neutral-200);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    background: white;
    box-shadow: var(--shadow-sm);
  }

  .table-mobile-stack td {
    border: none;
    padding: var(--space-2) 0;
    text-align: left;
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
    display: flex;
    align-items: center;
    min-height: 40px;
  }

  .table-mobile-stack td:before {
    content: attr(data-label) ": ";
    font-weight: var(--font-semibold);
    color: var(--color-neutral-600);
    display: inline-block;
    min-width: 120px;
    flex-shrink: 0;
  }

  /* Special handling for photo column in mobile stack */
  .table-mobile-stack td[data-label="Photo"] {
    justify-content: flex-start;
    gap: var(--space-3);
  }

  .table-mobile-stack td[data-label="Photo"] .table-avatar {
    margin: 0;
  }

  /* Special handling for status column in mobile stack */
  .table-mobile-stack td[data-label="Statut"] {
    align-items: center;
  }

  .table-mobile-stack td[data-label="Statut"] .status-badge {
    margin: 0;
  }

  /* Special handling for actions in mobile stack */
  .table-mobile-stack td[data-label="Actions"] {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }

  .table-mobile-stack td[data-label="Actions"]:before {
    margin-bottom: var(--space-2);
  }

  .table-mobile-stack .action-buttons {
    margin-top: 0;
    justify-content: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .table-mobile-stack .action-btn {
    flex: 1;
    min-width: 80px;
    justify-content: center;
  }

  /* Mobile stack action buttons remain accessible */
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(2px);
  animation: fadeIn 0.2s ease-out;
  /* Ensure modal overlay creates its own stacking context */
  isolation: isolate;
  /* Force modal to cover entire viewport, not just content area */
  margin: 0;
  box-sizing: border-box;
}



/* Higher z-index for nested modals */
.modal-overlay--nested {
  z-index: 1070;
  background: rgba(0, 0, 0, 0.7);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: fadeInCenter 0.2s ease-out;
  /* Perfect centering regardless of sidebar */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
}



/* Higher z-index for nested modal containers */
.modal-container--nested {
  z-index: 1071;
  box-shadow: var(--shadow-2xl);
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Browser-specific modal positioning fix for consistent behavior */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  /* Webkit browsers (Chrome, Safari, Edge) - ensure center positioning */
  .modal-container {
    animation: fadeInCenter 0.2s ease-out;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@keyframes fadeInCenter {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Firefox-specific modal positioning */
@-moz-document url-prefix() {
  .modal-container {
    animation: fadeInCenter 0.2s ease-out;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* Additional browser compatibility for modal overlay positioning */
@supports (-webkit-appearance: none) {
  /* WebKit browsers - ensure proper modal overlay */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
  }
}

/* Ensure consistent modal positioning across all browsers */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  /* IE/Edge legacy support */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
  }

  .modal-container {
    animation: fadeInCenter 0.2s ease-out;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* Force consistent modal behavior for all browsers */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
}

.modal-container {
  animation: fadeInCenter 0.2s ease-out !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 10000 !important;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* Perfect centering regardless of sidebar */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
}

/* Profile modal specific styles */
.modal-content.user-profile-modal {
  max-width: 900px;
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-neutral-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-neutral-50);
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-2xl);
  color: var(--color-neutral-500);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
}

.modal-body {
  padding: var(--space-6);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--color-neutral-200);
  background: var(--color-neutral-50);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* User Profile Styles */
.user-profile-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-8);
}

.profile-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.profile-header {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--color-primary-100);
  flex-shrink: 0;
  /* Ensure cross-platform image rendering */
  display: block;
  background-color: var(--color-neutral-100);
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-1);
}

.profile-function {
  font-size: var(--text-lg);
  color: var(--color-neutral-600);
  margin-bottom: var(--space-3);
}

.profile-status {
  margin-bottom: var(--space-2);
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--color-neutral-50);
  border-radius: var(--radius-md);
}

.detail-label {
  font-weight: var(--font-medium);
  color: var(--color-neutral-700);
  min-width: 120px;
}

.detail-value {
  color: var(--color-neutral-900);
  flex: 1;
}

.borrowed-books-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--color-neutral-200);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin: 0;
}

.borrowed-books-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  max-height: 400px;
  overflow-y: auto;
}

.borrowed-book-card {
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border: 1px solid var(--color-neutral-200);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.book-info {
  flex: 1;
}

.book-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-1);
}

.book-author {
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
  margin-bottom: var(--space-2);
}

.book-dates {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.date-item {
  font-size: var(--text-xs);
  color: var(--color-neutral-500);
}

.book-status {
  display: flex;
  align-items: center;
  justify-content: center;
}

.book-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: flex-end;
}

.empty-state {
  text-align: center;
  padding: var(--space-8);
  color: var(--color-neutral-500);
}

.empty-icon {
  margin: 0 auto var(--space-4);
  opacity: 0.5;
}

/* Modern Action Button System */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
  box-sizing: border-box;
}

/* Base action button styles */
.action-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  padding: var(--space-2);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  outline: none;
  overflow: hidden;
  box-sizing: border-box;
}

.action-btn:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Action button variants */
.action-btn--primary {
  background: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.action-btn--primary:hover {
  background: var(--color-primary-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.action-btn--secondary {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
}

.action-btn--secondary:hover {
  background: var(--color-neutral-200);
  border-color: var(--color-neutral-400);
  color: var(--color-neutral-800);
  transform: translateY(-1px);
}

.action-btn--success {
  background: var(--color-success-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.action-btn--success:hover {
  background: var(--color-success-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.action-btn--warning {
  background: var(--color-warning-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.action-btn--warning:hover {
  background: var(--color-warning-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.action-btn--danger {
  background: var(--color-error-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.action-btn--danger:hover {
  background: var(--color-error-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.action-btn--ghost {
  background: transparent;
  color: var(--color-neutral-600);
  border: 1px solid transparent;
}

.action-btn--ghost:hover {
  background: var(--color-neutral-100);
  color: var(--color-neutral-800);
}

/* Action button sizes */
.action-btn--sm {
  min-width: 28px;
  height: 28px;
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
}



.action-btn--lg {
  min-width: 44px;
  height: 44px;
  padding: var(--space-3);
  font-size: var(--text-base);
}

/* Loading state */
.action-btn--loading {
  pointer-events: none;
}

.action-btn--loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Tooltip system - REMOVED to eliminate black tooltip boxes */

/* Membership Number Section */
.membership-number-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.auto-number-preview {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-md);
}

.preview-label {
  font-size: var(--text-sm);
  color: var(--color-primary-700);
  font-weight: var(--font-medium);
}

.preview-number {
  font-family: var(--font-mono);
  font-size: var(--text-base);
  font-weight: var(--font-bold);
  color: var(--color-primary-900);
  background: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-primary-300);
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  white-space: nowrap;
  min-width: fit-content;
  justify-content: center;
}

.status-badge.active {
  background: var(--color-success-100);
  color: var(--color-success-800);
  border: 1px solid var(--color-success-300);
}

.status-badge.inactive {
  background: var(--color-neutral-100);
  color: var(--color-neutral-600);
  border: 1px solid var(--color-neutral-300);
}

.status-badge.overdue {
  background: var(--color-error-100);
  color: var(--color-error-800);
  border: 1px solid var(--color-error-300);
}

.status-badge.warning {
  background: var(--color-warning-100);
  color: var(--color-warning-800);
  border: 1px solid var(--color-warning-300);
}

/* Alert components */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--space-4);
}

.alert-error {
  background: var(--color-error-50);
  border-color: var(--color-error-200);
  color: var(--color-error-800);
}

.alert-warning {
  background: var(--color-warning-50);
  border-color: var(--color-warning-200);
  color: var(--color-warning-800);
}

.alert-success {
  background: var(--color-success-50);
  border-color: var(--color-success-200);
  color: var(--color-success-800);
}

.alert-info {
  background: var(--color-primary-50);
  border-color: var(--color-primary-200);
  color: var(--color-primary-800);
}

/* Enhanced stat cards */
.stat-card--alert {
  border: 2px solid var(--color-error-300);
  background: var(--color-error-50);
}

.stat-card--warning {
  border: 2px solid var(--color-warning-300);
  background: var(--color-warning-50);
}

/* Enhanced action buttons with labels */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  flex-direction: row;
}

/* Icon legend styling for management pages */
.legend-section {
  margin-bottom: var(--space-6);
}

.legend-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-800);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.legend-title::before {
  content: "ℹ️";
  font-size: var(--text-lg);
}

.icon-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--border-radius-lg);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-neutral-700);
}

.legend-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: var(--border-radius-md);
  border: 1px solid transparent;
  font-size: var(--text-sm);
}

/* Color-matched legend icons */
.legend-icon--primary {
  background: var(--color-primary-500);
  color: var(--color-white);
  border-color: var(--color-primary-600);
}

.legend-icon--secondary {
  background: var(--color-neutral-500);
  color: var(--color-white);
  border-color: var(--color-neutral-600);
}

.legend-icon--success {
  background: var(--color-success-500);
  color: var(--color-white);
  border-color: var(--color-success-600);
}

.legend-icon--warning {
  background: var(--color-warning-500);
  color: var(--color-white);
  border-color: var(--color-warning-600);
}

.legend-icon--danger {
  background: var(--color-error-500);
  color: var(--color-white);
  border-color: var(--color-error-600);
}

.legend-text {
  font-weight: var(--font-medium);
  white-space: nowrap;
}

/* Responsive legend */
@media (max-width: 768px) {
  .icon-legend {
    gap: var(--space-3);
    padding: var(--space-3);
  }

  .legend-item {
    font-size: var(--text-xs);
  }

  .legend-title {
    font-size: var(--text-sm);
  }
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-width: 400px;
  width: 100%;
}

.toast {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-neutral-200);
  overflow: hidden;
  animation: toast-slide-in 0.3s ease-out;
  transition: all 0.2s ease;
}

.toast:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 6px 10px rgba(0, 0, 0, 0.08);
}

.toast-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.toast-text {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  line-height: 1.4;
  margin-bottom: var(--space-1);
}

.toast-message {
  font-size: var(--text-xs);
  line-height: 1.4;
  color: var(--color-neutral-600);
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  color: var(--color-neutral-400);
  transition: all 0.2s ease;
  margin-top: -2px;
}

.toast-close:hover {
  background: var(--color-neutral-100);
  color: var(--color-neutral-600);
}

/* Toast variants */
.toast--success {
  border-left: 4px solid var(--color-success-500);
}

.toast--success .toast-icon {
  color: var(--color-success-600);
}

.toast--success .toast-title {
  color: var(--color-success-800);
}

.toast--error {
  border-left: 4px solid var(--color-error-500);
}

.toast--error .toast-icon {
  color: var(--color-error-600);
}

.toast--error .toast-title {
  color: var(--color-error-800);
}

.toast--warning {
  border-left: 4px solid var(--color-warning-500);
}

.toast--warning .toast-icon {
  color: var(--color-warning-600);
}

.toast--warning .toast-title {
  color: var(--color-warning-800);
}

.toast--info {
  border-left: 4px solid var(--color-primary-500);
}

.toast--info .toast-icon {
  color: var(--color-primary-600);
}

.toast--info .toast-title {
  color: var(--color-primary-800);
}

@keyframes toast-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.action-btn--md {
  padding: var(--space-2) var(--space-3);
  min-width: auto;
}

/* Responsive action buttons */
@media (max-width: 768px) {
  .action-btn--md {
    padding: var(--space-2);
    min-width: 36px;
  }
}

/* Enhanced tooltips - REMOVED to eliminate black tooltip boxes */

/* Photo upload styles */
.photo-upload-section {
  display: flex;
  gap: var(--space-4);
  align-items: flex-start;
  padding: var(--space-4);
  border: 2px dashed var(--color-neutral-300);
  border-radius: var(--radius-lg);
  background: var(--color-neutral-50);
}

.photo-preview {
  flex-shrink: 0;
}

.preview-image {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 3px solid var(--color-primary-200);
  background: var(--color-neutral-100);
}

.photo-controls {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.photo-input {
  display: none;
}

.photo-help-text {
  font-size: var(--text-xs);
  color: var(--color-neutral-600);
  margin: 0;
}

/* Responsive photo upload */
@media (max-width: 768px) {
  .photo-upload-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .preview-image {
    width: 100px;
    height: 100px;
  }
}

/* Loan extension modal styles */
.loan-extension-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.info-section {
  padding: var(--space-4);
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-neutral-200);
}

.info-grid {
  display: grid;
  gap: var(--space-3);
  margin-top: var(--space-3);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-neutral-200);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--color-neutral-600);
  font-size: var(--text-sm);
}

.info-value {
  color: var(--color-neutral-900);
  font-size: var(--text-sm);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.extension-preview {
  margin-top: var(--space-4);
}

.preview-card {
  padding: var(--space-4);
  background: var(--color-success-50);
  border: 1px solid var(--color-success-200);
  border-radius: var(--radius-lg);
}

.preview-details {
  display: grid;
  gap: var(--space-2);
  margin-top: var(--space-3);
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-sm);
}

/* Form input error state */
.form-input--error,
.form-input.error,
.form-select.error {
  border-color: var(--color-error-500);
  background-color: var(--color-error-50);
}

/* Enhanced form validation error styling */
.form-error {
  color: var(--color-error-600);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

/* Individual field error messages */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-error-700);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  margin-top: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  border-radius: var(--radius-md);
  position: relative;
  animation: error-fade-in 0.2s ease-out;
}

.error-message::before {
  content: "⚠";
  color: var(--color-error-600);
  font-size: var(--text-sm);
  font-weight: var(--font-bold);
}

/* Enhanced form error summary */
.form-error-summary {
  margin: var(--space-6) 0 var(--space-4) 0;
  padding: var(--space-4);
  background: linear-gradient(135deg, var(--color-error-50) 0%, var(--color-error-100) 100%);
  border: 1px solid var(--color-error-300);
  border-left: 4px solid var(--color-error-500);
  border-radius: var(--radius-lg);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
  position: relative;
  animation: error-summary-slide-in 0.3s ease-out;
}

.form-error-summary::before {
  content: "⚠️";
  position: absolute;
  top: var(--space-4);
  left: var(--space-4);
  font-size: var(--text-lg);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.form-error-summary .error-message {
  margin: 0;
  padding: 0 0 0 var(--space-8);
  background: transparent;
  border: none;
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  color: var(--color-error-800);
  animation: none;
}

.form-error-summary .error-message::before {
  display: none;
}

/* Enhanced error field styling */
.form-input.error,
.form-select.error {
  border-color: var(--color-error-400);
  background-color: var(--color-error-25);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  transition: all 0.2s ease;
}

.form-input.error:focus,
.form-select.error:focus {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
  outline: none;
}

/* Animations */
@keyframes error-fade-in {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes error-summary-slide-in {
  from {
    opacity: 0;
    transform: translateY(-8px);
    box-shadow: 0 0 0 rgba(239, 68, 68, 0);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
  }
}





/* Assign book modal styles */
.modal-container--large {
  max-width: 800px;
  width: 90vw;
}

.assign-book-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.book-info-section .book-card {
  padding: var(--space-4);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-lg);
}

.book-details .book-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--color-primary-900);
  margin: 0 0 var(--space-1) 0;
}

.book-details .book-author {
  color: var(--color-primary-700);
  margin: 0 0 var(--space-3) 0;
}

.book-meta {
  display: flex;
  gap: var(--space-4);
  font-size: var(--text-sm);
  color: var(--color-primary-600);
}

.search-box {
  position: relative;
  margin-bottom: var(--space-4);
}

.search-box svg {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-neutral-500);
}

.search-input {
  width: 100%;
  padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
}

.members-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-md);
}

.member-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-bottom: 1px solid var(--color-neutral-100);
  cursor: pointer;
  transition: background-color 0.2s;
}

.member-item:hover {
  background-color: var(--color-neutral-50);
}

.member-item.selected {
  background-color: var(--color-primary-50);
  border-color: var(--color-primary-200);
}

.member-item.has-book-warning {
  background-color: var(--color-warning-50);
  border-color: var(--color-warning-200);
}

.member-item.has-book-warning:hover {
  background-color: var(--color-warning-100);
}

.duplicate-warning {
  color: var(--color-warning-600);
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: var(--font-medium);
  color: var(--color-neutral-900);
}

.member-details {
  display: flex;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--color-neutral-600);
  margin-top: var(--space-1);
}

.selection-indicator {
  color: var(--color-success-600);
  font-weight: var(--font-bold);
  font-size: var(--text-lg);
}

.date-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.loan-summary {
  margin-top: var(--space-4);
}

.summary-card {
  padding: var(--space-4);
  background: var(--color-success-50);
  border: 1px solid var(--color-success-200);
  border-radius: var(--radius-lg);
}

.summary-title {
  font-weight: var(--font-semibold);
  color: var(--color-success-900);
  margin: 0 0 var(--space-3) 0;
}

.summary-details {
  display: grid;
  gap: var(--space-2);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-sm);
  color: var(--color-success-800);
}

/* Responsive design for assign book modal */
@media (max-width: 768px) {
  .modal-container--large {
    width: 95vw;
    max-height: 90vh;
    overflow-y: auto;
    /* Maintain perfect centering on mobile for large modals */
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  .date-inputs {
    grid-template-columns: 1fr;
  }

  .member-details {
    flex-direction: column;
    gap: var(--space-1);
  }
}

/* Confirmation modal styles */
.confirmation-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.confirmation-message {
  text-align: center;
  padding: var(--space-4);
  background: var(--color-warning-50);
  border: 1px solid var(--color-warning-200);
  border-radius: var(--radius-lg);
}

.confirmation-message.bg-error-50 {
  background: var(--color-error-50);
}

.confirmation-message.border-error-200 {
  border-color: var(--color-error-200);
}

.confirmation-message.bg-primary-50 {
  background: var(--color-primary-50);
}

.confirmation-message.border-primary-200 {
  border-color: var(--color-primary-200);
}

.confirmation-message p {
  margin: 0;
  line-height: 1.5;
}

/* Icon color utilities for confirmation modal */
.text-error-600 {
  color: var(--color-error-600);
}

.text-warning-600 {
  color: var(--color-warning-600);
}

.text-primary-600 {
  color: var(--color-primary-600);
}

.loan-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-5);
}

.detail-section {
  padding: var(--space-4);
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-neutral-200);
}

.detail-grid {
  display: grid;
  gap: var(--space-3);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-200);
  margin-bottom: var(--space-2);
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: var(--color-neutral-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  flex-shrink: 0;
  margin-right: var(--space-3);
}

.detail-value {
  color: var(--color-neutral-900);
  font-size: var(--text-sm);
  text-align: right;
  word-break: break-word;
}

.extension-details {
  padding: var(--space-4);
  background: var(--color-success-50);
  border: 1px solid var(--color-success-200);
  border-radius: var(--radius-lg);
}

.extension-grid {
  display: grid;
  gap: var(--space-3);
}

.extension-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-success-200);
  margin-bottom: var(--space-2);
}

.extension-item:last-child {
  margin-bottom: 0;
}

.extension-label {
  color: var(--color-success-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  flex-shrink: 0;
  margin-right: var(--space-3);
}

.extension-value {
  font-size: var(--text-sm);
  text-align: right;
  word-break: break-word;
}

/* Return confirmation modal styles */
.return-details {
  padding: var(--space-4);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-200);
  border-radius: var(--radius-lg);
}

.return-grid {
  display: grid;
  gap: var(--space-3);
}

.return-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-primary-200);
  margin-bottom: var(--space-2);
}

.return-item:last-child {
  margin-bottom: 0;
}

.return-label {
  color: var(--color-primary-700);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  flex-shrink: 0;
  margin-right: var(--space-3);
}

.return-value {
  font-size: var(--text-sm);
  color: var(--color-primary-900);
  text-align: right;
  word-break: break-word;
}

.return-warning {
  margin-top: var(--space-4);
}

.warning-card {
  padding: var(--space-4);
  background: var(--color-warning-50);
  border: 1px solid var(--color-warning-200);
  border-radius: var(--radius-lg);
}

/* Loan extension modal specific styles */
.loan-extension-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.info-section {
  padding: var(--space-4);
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
}

.info-grid {
  display: grid;
  gap: var(--space-3);
  margin-top: var(--space-3);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-neutral-200);
}

.info-label {
  color: var(--color-neutral-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  flex-shrink: 0;
  margin-right: var(--space-3);
}

.info-value {
  color: var(--color-neutral-900);
  font-size: var(--text-sm);
  text-align: right;
  word-break: break-word;
}

/* Cross-platform compatibility fixes for action buttons */
/* Industry best practices for Electron/Browser consistency */
.book-management-table td[data-label="Actions"] {
  vertical-align: middle;
  text-align: left;
  padding: var(--space-3);
}

/* Cross-platform flexbox normalization */
.action-buttons {
  /* Force consistent flexbox behavior using standard CSS */
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  /* Ensure consistent box model */
  box-sizing: border-box;
}

.action-buttons > * {
  box-sizing: border-box;
}

/* Legacy support for existing icon-button class */
.icon-button {
  background: none;
  border: 1px solid var(--color-neutral-300);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--color-neutral-600);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-button:hover {
  background: var(--color-neutral-100);
  border-color: var(--color-neutral-400);
  color: var(--color-neutral-800);
}

.icon-button.danger {
  border-color: var(--color-error-300);
  color: var(--color-error-600);
}

.icon-button.danger:hover {
  background: var(--color-error-50);
  border-color: var(--color-error-400);
  color: var(--color-error-700);
}

/* Responsive adjustments for user profile */
@media (max-width: 768px) {
  .user-profile-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .detail-label {
    min-width: auto;
    font-size: var(--text-sm);
  }
}

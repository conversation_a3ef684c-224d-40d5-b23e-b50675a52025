import React, { useState } from 'react';
import { EyeIcon, EyeOffIcon, LockIcon } from './Icons';
import { useDebouncedCallback } from '../hooks/useDebounce';
import ModalPortal from './ModalPortal';

interface ChangePasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPasswordChange: (currentPassword: string, newPassword: string) => Promise<boolean>;
  username: string;
}

const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
  isOpen,
  onClose,
  onPasswordChange,
  username
}) => {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Current password validation
    if (!formData.currentPassword.trim()) {
      newErrors.currentPassword = 'Le mot de passe actuel est requis';
    }

    // New password validation
    if (!formData.newPassword) {
      newErrors.newPassword = 'Le nouveau mot de passe est requis';
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = 'Le nouveau mot de passe doit contenir au moins 8 caractères';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.newPassword)) {
      newErrors.newPassword = 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre';
    } else if (formData.newPassword === formData.currentPassword) {
      newErrors.newPassword = 'Le nouveau mot de passe doit être différent de l\'actuel';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Debounced validation for real-time feedback
  const debouncedValidation = useDebouncedCallback((field: string, value: string) => {
    const newErrors = { ...errors };

    if (field === 'newPassword' && value) {
      if (value.length < 8) {
        newErrors.newPassword = 'Le nouveau mot de passe doit contenir au moins 8 caractères';
      } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
        newErrors.newPassword = 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre';
      } else if (value === formData.currentPassword) {
        newErrors.newPassword = 'Le nouveau mot de passe doit être différent de l\'actuel';
      } else {
        delete newErrors.newPassword;
      }
    }

    if (field === 'confirmPassword' && value) {
      if (value !== formData.newPassword) {
        newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
      } else {
        delete newErrors.confirmPassword;
      }
    }

    setErrors(newErrors);
  }, 300);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error immediately when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Trigger debounced validation
    debouncedValidation(field, value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const success = await onPasswordChange(formData.currentPassword, formData.newPassword);
      
      if (success) {
        // Reset form and close modal
        setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' });
        setErrors({});
        onClose();
      } else {
        setErrors({ general: 'Erreur lors du changement de mot de passe. Vérifiez votre mot de passe actuel.' });
      }
    } catch (error) {
      console.error('Password change error:', error);
      setErrors({ general: 'Une erreur inattendue s\'est produite. Veuillez réessayer.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setErrors({});
      onClose();
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  // Debug logging
  console.log('🔐 ChangePasswordModal render - isOpen:', isOpen, 'username:', username);

  if (!isOpen) {
    console.log('🔐 ChangePasswordModal not rendering - isOpen is false');
    return null;
  }

  console.log('🔐 ChangePasswordModal rendering modal content');

  return (
    <ModalPortal isOpen={isOpen} onClose={handleClose}>
      <div className="modal-overlay" onClick={handleClose}>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>
          {/* Header */}
          <div className="modal-header">
            <div className="flex items-center">
              <LockIcon size={24} className="text-blue-600 mr-2" />
              <div>
                <h2 className="modal-title">Changer le mot de passe</h2>
                <p className="text-sm text-gray-600">Utilisateur: {username}</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="modal-close"
              disabled={isLoading}
            >
              ×
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="modal-body space-y-4">
            {/* General Error */}
            {errors.general && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-600 text-sm">{errors.general}</p>
              </div>
            )}

            {/* Current Password Field */}
            <div>
              <label htmlFor="currentPassword" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
                Mot de passe actuel *
              </label>
              <div className="relative">
                <input
                  type={showPasswords.current ? 'text' : 'password'}
                  id="currentPassword"
                  value={formData.currentPassword}
                  onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                  className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                    errors.currentPassword ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="Entrez votre mot de passe actuel"
                  disabled={isLoading}
                  autoComplete="current-password"
                  style={{ fontSize: '16px' }}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('current')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                  disabled={isLoading}
                >
                  {showPasswords.current ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
                </button>
              </div>
              {errors.currentPassword && <p className="text-red-600 text-sm mt-1">{errors.currentPassword}</p>}
            </div>

            {/* New Password Field */}
            <div>
              <label htmlFor="newPassword" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
                Nouveau mot de passe *
              </label>
              <div className="relative">
                <input
                  type={showPasswords.new ? 'text' : 'password'}
                  id="newPassword"
                  value={formData.newPassword}
                  onChange={(e) => handleInputChange('newPassword', e.target.value)}
                  className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                    errors.newPassword ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="Créez un nouveau mot de passe sécurisé"
                  disabled={isLoading}
                  autoComplete="new-password"
                  style={{ fontSize: '16px' }}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('new')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                  disabled={isLoading}
                >
                  {showPasswords.new ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
                </button>
              </div>
              {errors.newPassword && <p className="text-red-600 text-sm mt-1">{errors.newPassword}</p>}
              <p className="text-xs mt-1" style={{ color: '#64748b' }}>
                Minimum 8 caractères avec au moins une minuscule, une majuscule et un chiffre
              </p>
            </div>

            {/* Confirm Password Field */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
                Confirmer le nouveau mot de passe *
              </label>
              <div className="relative">
                <input
                  type={showPasswords.confirm ? 'text' : 'password'}
                  id="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                    errors.confirmPassword ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                  placeholder="Confirmez votre nouveau mot de passe"
                  disabled={isLoading}
                  autoComplete="new-password"
                  style={{ fontSize: '16px' }}
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('confirm')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                  disabled={isLoading}
                >
                  {showPasswords.confirm ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
                </button>
              </div>
              {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>}
            </div>
          </form>

          {/* Footer */}
          <div className="modal-footer">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-secondary"
              disabled={isLoading}
            >
              Annuler
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              className="btn btn-primary"
              disabled={isLoading || Object.keys(errors).length > 0}
            >
              {isLoading ? 'Changement en cours...' : 'Changer le mot de passe'}
            </button>
          </div>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ChangePasswordModal;

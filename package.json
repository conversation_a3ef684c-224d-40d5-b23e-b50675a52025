{"name": "bibliotech", "private": true, "version": "1.0.3", "description": "Système de gestion de bibliothèque moderne - BiblioTech", "author": {"name": "BiblioTech Team", "email": "<EMAIL>"}, "main": "dist/electron.cjs", "homepage": "./", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron:prod": "set NODE_ENV=production&& set BIBLIOTECH_PRODUCTION=true&& electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3001 && set NODE_ENV=development&& electron .\"", "build-electron": "npm run build && electron .", "dist": "npm run build && electron-builder", "dist:portable": "npm run build && electron-builder --config.portable", "dist-win": "npm run build && electron-builder --win", "dist-portable": "npm run build && npx electron-forge make --platform=win32 --arch=x64", "dist-win-all": "npm run build && electron-builder --win --x64 --ia32", "clean": "rimraf dist dist-electron out node_modules/.cache", "rebuild": "npm run clean && npm install && npm run dist-win-all", "postinstall": "electron-builder install-app-deps", "pack-win": "npm run build && npx electron-packager . BiblioTech --platform=win32 --arch=x64 --out=out --overwrite --ignore=\"^/(src|tsconfig|vite.config|eslint.config|BiblioTech-Demo-Distribution|BiblioTech-Professional-Demo|BiblioTech-v1.0.2-Portable|BiblioTech-Production-Builds|BiblioTech-Production-v1.0.3-Auth-Update|Documentation|Guide|TESTING|DATABASE|COMMERCIAL|inspect-|test-|database-viewer|nul)\" --prune=true --app-version=1.0.3 --build-version=1.0.3 --executable-name=BiblioTech", "build-installer": "npm run pack-win && npx electron-builder --prepackaged out/BiblioTech-win32-x64 --win", "build-production-installer": "npm run clean && npm run build && npm run pack-win && npx electron-builder --prepackaged out/BiblioTech-win32-x64 --win", "build-portable": "npm run clean && npm run build && npm run pack-win", "build-all": "npm run build-production-installer && npm run build-portable", "build-production-clean": "npm run clean && npm install && npm run build-all"}, "dependencies": {"better-sqlite3": "^11.10.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron/packager": "^18.3.6", "@eslint/js": "^9.25.0", "@types/better-sqlite3": "^7.6.13", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "electron": "^36.4.0", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "electron-packager": "^17.1.2", "electron-rebuild": "^3.2.9", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rimraf": "^6.0.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.bibliotech.desktop", "productName": "BiblioTech", "copyright": "Copyright © 2025 BiblioTech Team", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "package.json", "node_modules/**/*"], "extraResources": [{"from": "public/usersimages", "to": "usersimages", "filter": ["**/*"]}], "asarUnpack": ["node_modules/better-sqlite3/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "dir", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "BiblioTech-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "BiblioTech", "artifactName": "BiblioTech-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Productivity"}}, "config": {"forge": {"packagerConfig": {"name": "BiblioTech", "executableName": "BiblioTech", "icon": "public/assets/icon.png", "extraResource": ["public/assets", "public/usersimages"]}, "makers": [{"name": "@electron-forge/maker-zip", "config": {}}, {"name": "@electron-forge/maker-squirrel", "config": {"name": "BiblioTech"}}]}}}
import React from 'react';
import { XIcon, AlertTriangleIcon, CheckIcon } from './Icons';
import ModalPortal from './ModalPortal';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'danger' | 'warning' | 'info';
  icon?: React.ReactNode;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirmer',
  cancelText = 'Annuler',
  variant = 'warning',
  icon
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'danger':
        return {
          iconColor: 'text-error-600',
          messageBackground: 'bg-error-50',
          messageBorder: 'border-error-200',
          confirmButton: 'action-btn--danger'
        };
      case 'warning':
        return {
          iconColor: 'text-warning-600',
          messageBackground: 'bg-warning-50',
          messageBorder: 'border-warning-200',
          confirmButton: 'action-btn--warning'
        };
      case 'info':
        return {
          iconColor: 'text-primary-600',
          messageBackground: 'bg-primary-50',
          messageBorder: 'border-primary-200',
          confirmButton: 'action-btn--primary'
        };
      default:
        return {
          iconColor: 'text-warning-600',
          messageBackground: 'bg-warning-50',
          messageBorder: 'border-warning-200',
          confirmButton: 'action-btn--warning'
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <ModalPortal isOpen={isOpen} onClose={onClose}>
      <div className="modal-container" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title flex items-center gap-2">
            {icon || <AlertTriangleIcon size={24} className={styles.iconColor} />}
            {title}
          </h3>
          <button onClick={onClose} className="modal-close">
            <XIcon size={20} />
          </button>
        </div>

        <div className="modal-body">
          <div className="confirmation-content">
            <div className={`confirmation-message ${styles.messageBackground} ${styles.messageBorder}`}>
              <p className="text-lg text-neutral-900">
                {message}
              </p>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button onClick={onClose} className="action-btn action-btn--secondary">
            <XIcon size={16} />
            <span className="action-label">{cancelText}</span>
          </button>
          <button onClick={handleConfirm} className={`action-btn ${styles.confirmButton}`}>
            <CheckIcon size={16} />
            <span className="action-label">{confirmText}</span>
          </button>
        </div>
      </div>
    </ModalPortal>
  );
};

export default ConfirmationModal;

import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { getPermissions } from '../types/auth';
import type { AuditLog } from '../types/auth';
import { exportAuditLogsToCSV } from '../services/csv-utils';

const AuditLogs: React.FC = () => {
  const { user: currentUser, getAuditLogs } = useAuth();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [filterResource, setFilterResource] = useState<string>('all');

  const permissions = currentUser ? getPermissions(currentUser.role) : null;

  // Translation functions for French localization
  const translateAction = (action: string): string => {
    const actionTranslations: Record<string, string> = {
      'LOGIN': 'Connexion',
      'LOGOUT': 'Déconnexion',
      'CREATE': 'Création',
      'UPDATE': 'Modification',
      'DELETE': 'Suppression',
      'REGISTER': 'Inscription',
      'VIEW': 'Consultation',
      'BULK_IMPORT': 'Import',
      'REORDER': 'Réorganisation',
      'RESET_PASSWORD': 'Réinit. MDP'
    };
    return actionTranslations[action] || action;
  };

  const translateResource = (resource: string): string => {
    const resourceTranslations: Record<string, string> = {
      'AUTH': 'Authentification',
      'BOOK': 'Livre',
      'BOOKS': 'Livres',
      'MEMBER': 'Membre',
      'MEMBERS': 'Membres',
      'USER': 'Utilisateur',
      'USERS': 'Utilisateurs',
      'LOAN': 'Prêt',
      'LOANS': 'Prêts',
      'AUDIT_LOGS': 'Journaux',
      'CATEGORY': 'Catégorie',
      'CATEGORIES': 'Catégories'
    };
    return resourceTranslations[resource] || resource;
  };

  const getActionBadgeClass = (action: string): string => {
    switch (action) {
      case 'LOGIN':
      case 'CREATE':
      case 'REGISTER':
        return 'badge-success';
      case 'UPDATE':
      case 'REORDER':
      case 'VIEW':
        return 'badge-warning';
      case 'DELETE':
      case 'LOGOUT':
        return 'badge-error';
      default:
        return 'badge-neutral';
    }
  };

  const getActionIcon = (action: string): string => {
    switch (action) {
      case 'LOGIN': return '🔐';
      case 'LOGOUT': return '🚪';
      case 'CREATE': return '➕';
      case 'UPDATE': return '✏️';
      case 'DELETE': return '🗑️';
      case 'REGISTER': return '👤';
      case 'VIEW': return '👁️';
      case 'BULK_IMPORT': return '📥';
      case 'REORDER': return '🔄';
      case 'RESET_PASSWORD': return '🔑';
      default: return '📝';
    }
  };

  // Debug: Log component render
  console.log('🔍 AuditLogs component rendering...');
  console.log('👤 Current user:', currentUser?.username, 'Role:', currentUser?.role);
  console.log('🔐 Permissions:', permissions);
  console.log('⏳ Loading state:', isLoading);
  console.log('📋 Logs count:', logs.length);

  // Load audit logs
  useEffect(() => {
    const loadLogs = async () => {
      console.log('🔍 AuditLogs useEffect triggered');

      if (permissions?.canViewAuditLogs && currentUser) {
        try {
          console.log('✅ User has permissions, loading audit logs...');
          setIsLoading(true);
          const auditLogs = await getAuditLogs();
          console.log('📋 Audit logs loaded:', auditLogs.length, 'entries');
          setLogs(auditLogs || []);
        } catch (error) {
          console.error('❌ Error loading audit logs:', error);
          setLogs([]);
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('❌ No permissions to view audit logs or no current user');
        setIsLoading(false);
      }
    };

    if (currentUser) {
      loadLogs();
    } else {
      setIsLoading(false);
    }
  }, [getAuditLogs, permissions?.canViewAuditLogs, currentUser]);

  // Show access denied message if user doesn't have permissions
  if (!permissions?.canViewAuditLogs) {
    console.log('🚫 Rendering access denied message');
    return (
      <div className="card">
        <div className="card-content text-center py-8">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Accès refusé</h3>
          <p className="text-neutral">
            Vous n'avez pas les permissions nécessaires pour consulter les journaux d'audit.
            <br />
            Seuls les Administrateurs peuvent accéder à cette fonctionnalité.
          </p>
          <div className="mt-4 text-sm text-gray-500">
            Rôle actuel: {currentUser?.role || 'Non défini'}
          </div>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    console.log('⏳ Rendering loading state');
    return (
      <div className="card">
        <div className="card-content text-center py-8">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-neutral">Chargement des journaux d'audit...</p>
            <div className="mt-2 text-sm text-gray-500">
              Utilisateur: {currentUser?.username} ({currentUser?.role})
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Filter logs based on search and filter criteria
  const filteredLogs = logs.filter(log => {
    // Search filter (searches in French translations)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        log.username.toLowerCase().includes(searchLower) ||
        translateAction(log.action).toLowerCase().includes(searchLower) ||
        translateResource(log.resource).toLowerCase().includes(searchLower) ||
        log.details.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Action filter
    if (filterAction !== 'all' && log.action !== filterAction) {
      return false;
    }

    // Resource filter
    if (filterResource !== 'all' && log.resource !== filterResource) {
      return false;
    }

    return true;
  });

  // Get unique actions and resources for filter dropdowns
  const uniqueActions = [...new Set(logs.map(log => log.action))];
  const uniqueResources = [...new Set(logs.map(log => log.resource))];

  // Export function with French headers and UTF-8 BOM for Excel compatibility
  const exportLogs = () => {
    // Transform logs to include translated actions and resources
    const transformedLogs = filteredLogs.map(log => ({
      ...log,
      action: translateAction(log.action),
      resource: translateResource(log.resource)
    }));

    exportAuditLogsToCSV(transformedLogs);
  };

  // Main audit logs display
  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title">Journaux d'audit</h2>
            <p className="card-subtitle">Historique des actions administratives ({filteredLogs.length}/{logs.length} entrées)</p>
          </div>
          <button onClick={exportLogs} className="btn btn-secondary">
            📥 Exporter CSV
          </button>
        </div>
      </div>
      <div className="card-content">
        {/* Search and Filters */}
        <div className="mb-6">
          {/* Search Bar */}
          <div className="search-bar mb-4">
            <input
              type="text"
              className="search-input"
              placeholder="Rechercher dans les journaux..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filter Controls */}
          <div className="filter-bar">
            <div className="filter-group">
              <span className="filter-label">Action :</span>
              <select
                className="filter-select"
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
              >
                <option value="all">Toutes les actions</option>
                {uniqueActions.map(action => (
                  <option key={action} value={action}>{translateAction(action)}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <span className="filter-label">Ressource :</span>
              <select
                className="filter-select"
                value={filterResource}
                onChange={(e) => setFilterResource(e.target.value)}
              >
                <option value="all">Toutes les ressources</option>
                {uniqueResources.map(resource => (
                  <option key={resource} value={resource}>{translateResource(resource)}</option>
                ))}
              </select>
            </div>

            {(searchTerm || filterAction !== 'all' || filterResource !== 'all') && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setFilterAction('all');
                  setFilterResource('all');
                }}
                className="btn btn-secondary"
              >
                Réinitialiser
              </button>
            )}
          </div>
        </div>

        {/* Statistics */}
        <div className="stats-grid mb-6">
          <div className="stat-card">
            <div className="stat-value">{logs.length}</div>
            <div className="stat-label">Total entrées</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{filteredLogs.length}</div>
            <div className="stat-label">Résultats filtrés</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{uniqueActions.length}</div>
            <div className="stat-label">Types d'actions</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{[...new Set(logs.map(log => log.username))].length}</div>
            <div className="stat-label">Utilisateurs actifs</div>
          </div>
        </div>

        {filteredLogs.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {logs.length === 0 ? 'Aucun journal d\'audit' : 'Aucun résultat trouvé'}
            </h3>
            <p className="text-gray-500">
              {logs.length === 0
                ? 'Les actions du système seront enregistrées ici pour traçabilité et sécurité.'
                : 'Aucune entrée ne correspond aux critères de recherche sélectionnés.'
              }
            </p>
          </div>
        ) : (
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>Date/Heure</th>
                  <th>Utilisateur</th>
                  <th>Action</th>
                  <th>Ressource</th>
                  <th>Détails</th>
                </tr>
              </thead>
              <tbody>
                {filteredLogs.slice(0, 100).map((log) => (
                  <tr key={log.id}>
                    <td>
                      <div className="text-sm">
                        {new Date(log.timestamp).toLocaleDateString('fr-FR')}
                      </div>
                      <div className="text-xs text-neutral">
                        {new Date(log.timestamp).toLocaleTimeString('fr-FR')}
                      </div>
                    </td>
                    <td>
                      <div className="font-medium">{log.username}</div>
                      <div className="text-xs text-neutral">ID: {log.userId}</div>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <span>{getActionIcon(log.action)}</span>
                        <span className={`badge ${getActionBadgeClass(log.action)}`}>
                          {translateAction(log.action)}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div className="font-medium">{translateResource(log.resource)}</div>
                      {log.resourceId && (
                        <div className="text-xs text-neutral">ID: {log.resourceId}</div>
                      )}
                    </td>
                    <td>
                      <div className="text-sm">{log.details}</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* French Legend */}
        <div className="mt-6 p-4 bg-neutral-50 rounded-lg">
          <h4 className="font-semibold mb-3">Légende des actions :</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <span>🔐</span>
              <span>Connexion</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🚪</span>
              <span>Déconnexion</span>
            </div>
            <div className="flex items-center gap-2">
              <span>➕</span>
              <span>Création</span>
            </div>
            <div className="flex items-center gap-2">
              <span>✏️</span>
              <span>Modification</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🗑️</span>
              <span>Suppression</span>
            </div>
            <div className="flex items-center gap-2">
              <span>👤</span>
              <span>Inscription</span>
            </div>
            <div className="flex items-center gap-2">
              <span>👁️</span>
              <span>Consultation</span>
            </div>
            <div className="flex items-center gap-2">
              <span>🔑</span>
              <span>Réinit. MDP</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditLogs;

// Contexte de base de données pour BiblioTech
// Database context for managing database operations in React components

import React, { createContext, useContext, useEffect, useState } from 'react';
import type { Book, Category } from '../types/library';
import type { LibraryMember, MemberProfile } from '../types/member';
import '../types/electron'; // Import global type declarations

interface DatabaseContextType {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;

  // Méthodes pour les livres
  books: Book[];
  loadBooks: () => Promise<void>;
  addBook: (book: Omit<Book, 'id' | 'createdAt' | 'updatedAt' | 'disponible'>) => Promise<Book>;
  updateBook: (id: number, updates: Partial<Book>) => Promise<boolean>;
  deleteBook: (id: number) => Promise<boolean>;

  // Méthodes pour les catégories
  categories: Category[];
  loadCategories: () => Promise<void>;
  addCategory: (name: string, description?: string) => Promise<Category>;
  updateCategory: (id: number, name: string, description?: string) => Promise<boolean>;
  deleteCategory: (id: number) => Promise<boolean>;

  // Méthodes pour les membres
  members: LibraryMember[];
  memberProfiles: MemberProfile[];
  loadMembers: () => Promise<void>;
  addMember: (member: Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'>) => Promise<LibraryMember>;
  updateMember: (id: number, updates: Partial<LibraryMember>) => Promise<boolean>;
  deleteMember: (id: number) => Promise<boolean>;

  // Méthodes pour les emprunts
  createLoan: (bookId: number, memberId: number, startDate: string, dueDate: string) => Promise<boolean>;
  returnBook: (bookId: number) => Promise<boolean>;
  extendLoan: (bookId: number, newDueDate: string) => Promise<boolean>;

  // Utility
  refreshData: () => Promise<void>;
  getStats: () => Promise<{ totalBooks: number; totalMembers: number; borrowedBooks: number; overdueBooks: number; dueSoonBooks: number; }>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

interface DatabaseProviderProps {
  children: React.ReactNode;
}

export const DatabaseProvider: React.FC<DatabaseProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // États pour les données en cache
  const [books, setBooks] = useState<Book[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [members, setMembers] = useState<LibraryMember[]>([]);
  const [memberProfiles, setMemberProfiles] = useState<MemberProfile[]>([]);

  // Check if we're in Electron environment
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  // Initialisation de la base de données
  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!isElectron) {
          throw new Error('Application must be run in Electron environment');
        }

        console.log('🔄 Initialisation de la base de données via IPC...');

        // Migrer les données initiales si nécessaire
        const migrationResult = await window.electronAPI.database.migrateInitialData();
        if (!migrationResult.success) {
          throw new Error(migrationResult.message);
        }

        console.log('✅ Base de données initialisée:', migrationResult.message);

        // Charger les données initiales
        await loadAllData();

        setIsInitialized(true);
        console.log('🎉 Contexte de base de données prêt !');

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue lors de l\'initialisation';
        console.error('❌ Erreur d\'initialisation de la base de données:', errorMessage);
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    initializeDatabase();
  }, [isElectron]);

  // Fonction pour charger toutes les données
  const loadAllData = async () => {
    if (!isElectron) return;

    try {
      const [booksData, categoriesData, membersData, memberProfilesData] = await Promise.all([
        window.electronAPI.database.getAllBooks(),
        window.electronAPI.database.getAllCategories(),
        window.electronAPI.database.getAllMembers(),
        window.electronAPI.database.getAllMemberProfiles()
      ]);

      setBooks(booksData);
      setCategories(categoriesData);
      setMembers(membersData);
      setMemberProfiles(memberProfilesData);
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err);
      throw err;
    }
  };

  // Méthodes pour charger les données
  const loadBooks = async () => {
    if (!isElectron) return;
    try {
      const booksData = await window.electronAPI.database.getAllBooks();
      setBooks(booksData);
    } catch (err) {
      console.error('Error loading books:', err);
      setError('Failed to load books');
    }
  };

  const loadCategories = async () => {
    if (!isElectron) return;
    try {
      const categoriesData = await window.electronAPI.database.getAllCategories();
      setCategories(categoriesData);
    } catch (err) {
      console.error('Error loading categories:', err);
      setError('Failed to load categories');
    }
  };

  const loadMembers = async () => {
    if (!isElectron) return;
    try {
      const [membersData, memberProfilesData] = await Promise.all([
        window.electronAPI.database.getAllMembers(),
        window.electronAPI.database.getAllMemberProfiles()
      ]);
      setMembers(membersData);
      setMemberProfiles(memberProfilesData);
    } catch (err) {
      console.error('Error loading members:', err);
      setError('Failed to load members');
    }
  };

  // Méthodes pour les livres
  const addBook = async (book: Omit<Book, 'id' | 'createdAt' | 'updatedAt' | 'disponible'>): Promise<Book> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const newBook = await window.electronAPI.database.addBook(book);
      await loadBooks(); // Refresh books list
      return newBook;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add book';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateBook = async (id: number, updates: Partial<Book>): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      console.log('[DEPLOYMENT DEBUG] DatabaseContext.updateBook called with:', { id, updates });

      const success = await window.electronAPI.database.updateBook(id, updates);
      console.log('[DEPLOYMENT DEBUG] DatabaseContext.updateBook result:', success);

      if (success) {
        console.log('[DEPLOYMENT DEBUG] Refreshing books and members data...');
        await Promise.all([loadBooks(), loadMembers()]); // Refresh both books and members
        console.log('[DEPLOYMENT DEBUG] Data refresh completed');
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update book';
      console.error('[DEPLOYMENT DEBUG] DatabaseContext.updateBook error:', err);
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteBook = async (id: number): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.deleteBook(id);
      if (success) {
        await loadBooks(); // Refresh books list
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete book';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Méthodes pour les catégories
  const addCategory = async (name: string, description?: string): Promise<Category> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const newCategory = await window.electronAPI.database.addCategory(name, description);
      await loadCategories(); // Refresh categories list
      return newCategory;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add category';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateCategory = async (id: number, name: string, description?: string): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.updateCategory(id, name, description);
      if (success) {
        await loadCategories(); // Refresh categories list
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update category';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteCategory = async (id: number): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.deleteCategory(id);
      if (success) {
        await Promise.all([loadCategories(), loadBooks()]); // Refresh both categories and books
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete category';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Méthodes pour les membres
  const addMember = async (member: Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'>): Promise<LibraryMember> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const newMember = await window.electronAPI.database.addMember(member);
      await loadMembers(); // Refresh members list
      return newMember;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add member';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateMember = async (id: number, updates: Partial<LibraryMember>): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.updateMember(id, updates);
      if (success) {
        await loadMembers(); // Refresh members list
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update member';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteMember = async (id: number): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.deleteMember(id);
      if (success) {
        await loadMembers(); // Refresh members list
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete member';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Méthodes pour les emprunts
  const createLoan = async (bookId: number, memberId: number, startDate: string, dueDate: string): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.createLoan(bookId, memberId, startDate, dueDate);
      if (success) {
        await Promise.all([loadBooks(), loadMembers()]); // Refresh both books and members
      }
      return success;
    } catch (err) {
      // Don't set global error state for validation errors (like duplicate book borrowing)
      // These should be handled by the UI components that call this function
      const errorMessage = err instanceof Error ? err.message : 'Failed to create loan';

      // Only set global error state for critical system errors, not validation errors
      if (!errorMessage.includes('déjà emprunté ce livre') &&
          !errorMessage.includes('Aucune copie disponible')) {
        setError(errorMessage);
      }

      // Re-throw the original error to preserve specific validation messages
      throw err;
    }
  };

  const returnBook = async (bookId: number): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.returnBook(bookId);
      if (success) {
        await Promise.all([loadBooks(), loadMembers()]); // Refresh both books and members
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to return book';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const extendLoan = async (bookId: number, newDueDate: string): Promise<boolean> => {
    if (!isElectron) throw new Error('Application must be run in Electron environment');

    try {
      const success = await window.electronAPI.database.extendLoan(bookId, newDueDate);
      if (success) {
        await Promise.all([loadBooks(), loadMembers()]); // Refresh both books and members
      }
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to extend loan';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Utility methods
  const refreshData = async () => {
    if (!isElectron) return;
    await loadAllData();
  };

  const clearError = () => {
    setError(null);
  };

  const getStats = async (): Promise<{ totalBooks: number; totalMembers: number; borrowedBooks: number; overdueBooks: number; dueSoonBooks: number; }> => {
    // Use database queries for consistent statistics
    if (!window.electronAPI) {
      // Fallback to cached data calculations
      const totalBooks = books.length;
      const totalMembers = members.length;
      const borrowedBooks = books.filter(book => !book.disponible).length;
      const overdueLoans = memberProfiles.reduce((count, profile) => {
        return count + profile.livresEmpruntes.filter(book => book.isOverdue).length;
      }, 0);

      return {
        totalBooks,
        totalMembers,
        borrowedBooks,
        overdueBooks: overdueLoans,
        dueSoonBooks: 0 // Cannot calculate without database queries
      };
    }

    try {
      // Get statistics from database for consistency
      return await window.electronAPI.database.getStats();
    } catch (error) {
      console.error('Error fetching database statistics:', error);
      // Fallback to cached data calculations
      const totalBooks = books.length;
      const totalMembers = members.length;
      const borrowedBooks = books.filter(book => !book.disponible).length;
      const overdueLoans = memberProfiles.reduce((count, profile) => {
        return count + profile.livresEmpruntes.filter(book => book.isOverdue).length;
      }, 0);

      return {
        totalBooks,
        totalMembers,
        borrowedBooks,
        overdueBooks: overdueLoans,
        dueSoonBooks: 0
      };
    }
  };

  const contextValue: DatabaseContextType = {
    isInitialized,
    isLoading,
    error,
    clearError,

    // Données en cache
    books,
    categories,
    members,
    memberProfiles,

    // Méthodes de chargement
    loadBooks,
    loadCategories,
    loadMembers,

    // Méthodes CRUD
    addBook,
    updateBook,
    deleteBook,
    addCategory,
    updateCategory,
    deleteCategory,
    addMember,
    updateMember,
    deleteMember,
    createLoan,
    returnBook,
    extendLoan,

    // Utility
    refreshData,
    getStats
  };

  return (
    <DatabaseContext.Provider value={contextValue}>
      {children}
    </DatabaseContext.Provider>
  );
};

export const useDatabase = (): DatabaseContextType => {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error('useDatabase must be used within a DatabaseProvider');
  }
  return context;
};

export default DatabaseContext;

// Composant de chargement de la base de données
// Database loading component for BiblioTech

import React, { useState } from 'react';
import { useDatabase } from '../contexts/DatabaseContext';

interface DatabaseLoaderProps {
  isLoading: boolean;
  error: string | null;
  children: React.ReactNode;
}

const DatabaseLoader: React.FC<DatabaseLoaderProps> = ({ isLoading, error, children }) => {
  const [isDismissed, setIsDismissed] = useState(false);
  const { clearError } = useDatabase();

  // If error is dismissed, show children (allow user to continue)
  if (error && isDismissed) {
    return <>{children}</>;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Erreur de base de données
            </h3>
            <p className="text-sm text-gray-500 mb-6">
              {error}
            </p>
            <div className="space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                Réessayer
              </button>
              <button
                onClick={() => {
                  clearError();
                  setIsDismissed(true);
                }}
                className="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
              >
                Continuer malgré l'erreur
              </button>
            </div>
            <p className="text-xs text-gray-400 mt-4">
              Si vous continuez, certaines fonctionnalités peuvent ne pas fonctionner correctement.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
              <svg className="animate-spin h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Initialisation de BiblioTech
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Préparation de la base de données...
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
            <p className="text-xs text-gray-400 mt-2">
              Cela peut prendre quelques secondes lors du premier lancement
            </p>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default DatabaseLoader;

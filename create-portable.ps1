# BiblioTech Portable Version Creator
# This script creates a portable version of BiblioTech

Write-Host "🚀 Creating BiblioTech Portable Version..." -ForegroundColor Green

# Define paths
$sourceDir = "dist-electron\win-unpacked"
$portableDir = "BiblioTech-Portable-v1.0.3"
$distDir = "dist"

# Create portable directory
if (Test-Path $portableDir) {
    Remove-Item $portableDir -Recurse -Force
}
New-Item -ItemType Directory -Path $portableDir | Out-Null

# Copy Electron runtime files (we need to get them from node_modules)
$electronPath = "node_modules\electron\dist"
if (Test-Path $electronPath) {
    Write-Host "📦 Copying Electron runtime..." -ForegroundColor Yellow
    Copy-Item "$electronPath\*" -Destination $portableDir -Recurse -Force
    
    # Rename electron.exe to BiblioTech.exe
    if (Test-Path "$portableDir\electron.exe") {
        Rename-Item "$portableDir\electron.exe" "BiblioTech.exe"
        Write-Host "✅ Renamed electron.exe to BiblioTech.exe" -ForegroundColor Green
    }
}

# Copy application files
Write-Host "📁 Copying application files..." -ForegroundColor Yellow
$appResourcesDir = "$portableDir\resources\app"
New-Item -ItemType Directory -Path $appResourcesDir -Force | Out-Null

# Copy dist folder
Copy-Item $distDir -Destination $appResourcesDir -Recurse -Force

# Copy package.json
Copy-Item "package.json" -Destination $appResourcesDir -Force

# Copy node_modules (only essential ones)
$nodeModulesDir = "$appResourcesDir\node_modules"
New-Item -ItemType Directory -Path $nodeModulesDir -Force | Out-Null

# Copy better-sqlite3
if (Test-Path "node_modules\better-sqlite3") {
    Copy-Item "node_modules\better-sqlite3" -Destination $nodeModulesDir -Recurse -Force
    Write-Host "✅ Copied better-sqlite3" -ForegroundColor Green
}

# Copy public folder with images
Copy-Item "public" -Destination $appResourcesDir -Recurse -Force

# Create a simple README for portable version
$readmeContent = @"
# BiblioTech Portable v1.0.3

## Profile Images Fixed Version

This is a portable version of BiblioTech that includes fixes for profile image display issues.

### What's Fixed:
- ✅ Profile images now display correctly in all sections
- ✅ Automatic image path resolution in Electron environment  
- ✅ Fallback to generated avatars when images are missing
- ✅ Initial profile images are automatically copied to user data directory
- ✅ Support for both development and production environments

### How to Run:
1. Extract this folder to any location
2. Double-click BiblioTech.exe to start the application
3. No installation required!

### Default Login:
- Username: superadmin
- Password: admin123

### Features:
- Complete library management system
- Member management with profile photos
- Book catalog and loan tracking
- Offline operation (no internet required)
- Data stored in user's AppData folder

### System Requirements:
- Windows 10/11 (64-bit)
- Minimum 4GB RAM (8GB recommended)
- 500MB free disk space

### Support:
For technical support or questions, please refer to the documentation included with the application.

---
BiblioTech Team © 2025
"@

Set-Content -Path "$portableDir\README.txt" -Value $readmeContent -Encoding UTF8

Write-Host "✅ BiblioTech Portable Version Created Successfully!" -ForegroundColor Green
Write-Host "📁 Location: $portableDir" -ForegroundColor Cyan
Write-Host "🚀 Run BiblioTech.exe to start the application" -ForegroundColor Yellow

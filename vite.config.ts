import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { copyFileSync, existsSync, mkdirSync } from 'fs'

// Plugin to copy electron files after build
const copyElectronFiles = () => {
  return {
    name: 'copy-electron-files',
    writeBundle() {
      // Copy electron main and preload files to dist
      const files = [
        { src: 'public/electron.cjs', dest: 'dist/electron.cjs' },
        { src: 'public/preload.cjs', dest: 'dist/preload.cjs' }
      ]

      files.forEach(({ src, dest }) => {
        if (existsSync(src)) {
          copyFileSync(src, dest)
          console.log(`Copied ${src} to ${dest}`)
        }
      })

      // Copy assets and user images
      const copyDir = (srcDir: string, destDir: string) => {
        if (existsSync(srcDir)) {
          if (!existsSync(destDir)) {
            mkdirSync(destDir, { recursive: true })
          }
          // This is handled by the public directory copy in Vite
        }
      }

      copyDir('public/assets', 'dist/assets')
      copyDir('public/usersimages', 'dist/usersimages')
    }
  }
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), copyElectronFiles()],
  base: './',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  },
  server: {
    port: 3000,
    strictPort: false
  },
  publicDir: 'public',
  // Ensure proper title is set during build
  define: {
    __APP_TITLE__: '"BiblioTech"'
  }
})

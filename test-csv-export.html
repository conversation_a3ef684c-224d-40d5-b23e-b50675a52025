<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CSV Export - BiblioTech</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .sample-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test CSV Export avec caractères français</h1>
    <p>Ce test vérifie que les caractères français (é, è, à, ç, etc.) s'exportent correctement en CSV avec BOM UTF-8 pour Excel.</p>

    <div class="test-section">
        <h2>Test 1: Export de livres</h2>
        <div class="sample-data">Données de test:
- "L'Étranger" par Albert Camus
- "À la recherche du temps perdu" par Marcel Proust  
- "Thérèse Raquin" par Émile Zola
- "Français" comme catégorie</div>
        <button onclick="testBookExport()">Exporter livres (CSV)</button>
    </div>

    <div class="test-section">
        <h2>Test 2: Export de membres</h2>
        <div class="sample-data">Données de test:
- Prénom: "François", Nom: "Müller"
- Fonction: "Bibliothécaire"
- Adresse: "123 rue de la Paix, Montréal"</div>
        <button onclick="testMemberExport()">Exporter membres (CSV)</button>
    </div>

    <div class="test-section">
        <h2>Test 3: Export de prêts</h2>
        <div class="sample-data">Données de test:
- Livre: "L'Étranger"
- Emprunteur: "François Müller"
- Statut: "À échéance"</div>
        <button onclick="testLoanExport()">Exporter prêts (CSV)</button>
    </div>

    <div class="test-section">
        <h2>Test 4: Export de logs d'audit</h2>
        <div class="sample-data">Données de test:
- Action: "Création"
- Ressource: "Bibliothèque"
- Détails: "Ajout du livre 'L'Étranger'"</div>
        <button onclick="testAuditExport()">Exporter logs (CSV)</button>
    </div>

    <script type="module">
        // Import the CSV utilities
        import { 
            exportBooksToCSV, 
            exportMembersToCSV, 
            exportLoansToCSV, 
            exportAuditLogsToCSV 
        } from './src/services/csv-utils.js';

        // Make functions available globally for button clicks
        window.testBookExport = function() {
            const testBooks = [
                {
                    titre: "L'Étranger",
                    auteur: "Albert Camus",
                    categorie: "Français",
                    genre: "Philosophie",
                    isbn: "9782070360024",
                    anneePublication: 1942,
                    description: "Roman existentialiste français",
                    quantiteTotale: 3,
                    disponible: true
                },
                {
                    titre: "À la recherche du temps perdu",
                    auteur: "Marcel Proust",
                    categorie: "Français",
                    genre: "Littérature",
                    isbn: "9782070108039",
                    anneePublication: 1913,
                    description: "Œuvre majeure de la littérature française",
                    quantiteTotale: 2,
                    disponible: false
                },
                {
                    titre: "Thérèse Raquin",
                    auteur: "Émile Zola",
                    categorie: "Français",
                    genre: "Réalisme",
                    isbn: "9782070360031",
                    anneePublication: 1867,
                    description: "Roman naturaliste français",
                    quantiteTotale: 1,
                    disponible: true
                }
            ];
            
            exportBooksToCSV(testBooks, 'test_livres_francais.csv');
        };

        window.testMemberExport = function() {
            const testMembers = [
                {
                    nom: "Müller",
                    prenom: "François",
                    fonction: "Bibliothécaire",
                    numeroMembre: "BT-2025-0001",
                    telephone: "************",
                    adresse: "123 rue de la Paix, Montréal",
                    email: "<EMAIL>",
                    isActive: true
                },
                {
                    nom: "Dubé",
                    prenom: "Élise",
                    fonction: "Étudiante",
                    numeroMembre: "BT-2025-0002",
                    telephone: "************",
                    adresse: "456 avenue des Érables, Québec",
                    email: "<EMAIL>",
                    isActive: true
                }
            ];
            
            exportMembersToCSV(testMembers, 'test_membres_francais.csv');
        };

        window.testLoanExport = function() {
            const testLoans = [
                {
                    titre: "L'Étranger",
                    auteur: "Albert Camus",
                    user: { prenom: "François", nom: "Müller" },
                    dateEmprunt: "2025-01-15",
                    dateRetour: "2025-01-29"
                },
                {
                    titre: "À la recherche du temps perdu",
                    auteur: "Marcel Proust",
                    user: { prenom: "Élise", nom: "Dubé" },
                    dateEmprunt: "2025-01-10",
                    dateRetour: "2025-01-20"
                }
            ];
            
            exportLoansToCSV(testLoans, 'test_prets_francais.csv');
        };

        window.testAuditExport = function() {
            const testLogs = [
                {
                    timestamp: new Date().toISOString(),
                    username: "François Müller",
                    action: "Création",
                    resource: "Bibliothèque",
                    resourceId: 1,
                    details: "Ajout du livre 'L'Étranger'"
                },
                {
                    timestamp: new Date().toISOString(),
                    username: "Élise Dubé",
                    action: "Modification",
                    resource: "Membre",
                    resourceId: 2,
                    details: "Mise à jour de l'adresse"
                }
            ];
            
            exportAuditLogsToCSV(testLogs, 'test_logs_francais.csv');
        };
    </script>
</body>
</html>

// Authentication and user management types

export interface User {
  id: number;
  username: string;
  email: string;
  role: 'super_admin' | 'administrator';
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface CreateUserData {
  username: string;
  email: string;
  password: string;
  role: 'super_admin' | 'administrator';
}

export interface AuditLog {
  id: number;
  userId: number;
  username: string;
  action: string;
  resource: string;
  resourceId?: number;
  details: string;
  timestamp: string;
}

export interface Permission {
  canManageBooks: boolean;
  canManageCategories: boolean;
  canManageUsers: boolean;
  canManageAdministrators: boolean;
  canViewAuditLogs: boolean;
  canImportBooks: boolean;
}

export const getPermissions = (role: User['role']): Permission => {
  switch (role) {
    case 'super_admin':
      return {
        canManageBooks: true,
        canManageCategories: true,
        canManageUsers: true,
        canManageAdministrators: true,
        canViewAuditLogs: true,
        canImportBooks: true,
      };
    case 'administrator':
      return {
        canManageBooks: true,
        canManageCategories: true,
        canManageUsers: true,
        canManageAdministrators: false,
        canViewAuditLogs: true, // ✅ Now administrators can view audit logs
        canImportBooks: true,
      };
    default:
      return {
        canManageBooks: false,
        canManageCategories: false,
        canManageUsers: false,
        canManageAdministrators: false,
        canViewAuditLogs: false,
        canImportBooks: false,
      };
  }
};

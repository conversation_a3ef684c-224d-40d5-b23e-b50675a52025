// Script de test pour vérifier l'intégration de la base de données
// Database integration test script for BiblioTech

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

console.log('🧪 Test de l\'intégration de la base de données BiblioTech\n');

// Fonction pour obtenir le chemin de la base de données
function getDatabasePath() {
  const dataDir = path.join(process.cwd(), 'data');
  
  // Créer le dossier data s'il n'existe pas
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  return path.join(dataDir, 'bibliotech-test.db');
}

// Test de création et initialisation de la base de données
function testDatabaseCreation() {
  console.log('📁 Test 1: Création de la base de données...');
  
  const dbPath = getDatabasePath();
  console.log(`   Chemin: ${dbPath}`);
  
  try {
    const db = new Database(dbPath);
    console.log('   ✅ Base de données créée avec succès');
    
    // Test de création des tables
    db.exec(`
      CREATE TABLE IF NOT EXISTS test_livres (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        titre TEXT NOT NULL,
        auteur TEXT NOT NULL,
        disponible BOOLEAN NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('   ✅ Table de test créée');
    
    db.close();
    return true;
  } catch (error) {
    console.log(`   ❌ Erreur: ${error.message}`);
    return false;
  }
}

// Test des opérations CRUD
function testCRUDOperations() {
  console.log('\n📚 Test 2: Opérations CRUD...');
  
  const dbPath = getDatabasePath();
  
  try {
    const db = new Database(dbPath);
    
    // Test INSERT
    console.log('   📝 Test INSERT...');
    const insertStmt = db.prepare(`
      INSERT INTO test_livres (titre, auteur, disponible) 
      VALUES (?, ?, ?)
    `);
    
    const result = insertStmt.run('Test Livre Database', 'Testeur BiblioTech', 1);
    console.log(`   ✅ Livre inséré avec ID: ${result.lastInsertRowid}`);
    
    // Test SELECT
    console.log('   📖 Test SELECT...');
    const selectStmt = db.prepare('SELECT * FROM test_livres WHERE id = ?');
    const book = selectStmt.get(result.lastInsertRowid);
    
    if (book && book.titre === 'Test Livre Database') {
      console.log(`   ✅ Livre récupéré: "${book.titre}" par ${book.auteur}`);
    } else {
      console.log('   ❌ Erreur lors de la récupération');
      return false;
    }
    
    // Test UPDATE
    console.log('   ✏️ Test UPDATE...');
    const updateStmt = db.prepare('UPDATE test_livres SET titre = ? WHERE id = ?');
    const updateResult = updateStmt.run('Test Livre Database - Modifié', result.lastInsertRowid);
    
    if (updateResult.changes > 0) {
      console.log('   ✅ Livre modifié avec succès');
    } else {
      console.log('   ❌ Erreur lors de la modification');
      return false;
    }
    
    // Vérifier la modification
    const updatedBook = selectStmt.get(result.lastInsertRowid);
    if (updatedBook.titre === 'Test Livre Database - Modifié') {
      console.log('   ✅ Modification vérifiée');
    } else {
      console.log('   ❌ Modification non persistée');
      return false;
    }
    
    // Test DELETE
    console.log('   🗑️ Test DELETE...');
    const deleteStmt = db.prepare('DELETE FROM test_livres WHERE id = ?');
    const deleteResult = deleteStmt.run(result.lastInsertRowid);
    
    if (deleteResult.changes > 0) {
      console.log('   ✅ Livre supprimé avec succès');
    } else {
      console.log('   ❌ Erreur lors de la suppression');
      return false;
    }
    
    db.close();
    return true;
  } catch (error) {
    console.log(`   ❌ Erreur CRUD: ${error.message}`);
    return false;
  }
}

// Test de performance
function testPerformance() {
  console.log('\n⚡ Test 3: Performance...');
  
  const dbPath = getDatabasePath();
  
  try {
    const db = new Database(dbPath);
    
    // Insérer 1000 livres de test
    console.log('   📊 Insertion de 1000 livres de test...');
    const insertStmt = db.prepare(`
      INSERT INTO test_livres (titre, auteur, disponible) 
      VALUES (?, ?, ?)
    `);
    
    const startTime = Date.now();
    
    const transaction = db.transaction(() => {
      for (let i = 1; i <= 1000; i++) {
        insertStmt.run(`Livre Test ${i}`, `Auteur ${i}`, i % 2 === 0 ? 1 : 0);
      }
    });
    
    transaction();
    
    const insertTime = Date.now() - startTime;
    console.log(`   ✅ 1000 livres insérés en ${insertTime}ms`);
    
    // Test de recherche
    console.log('   🔍 Test de recherche...');
    const searchStart = Date.now();
    const searchStmt = db.prepare('SELECT * FROM test_livres WHERE titre LIKE ?');
    const results = searchStmt.all('%Test 5%');
    const searchTime = Date.now() - searchStart;
    
    console.log(`   ✅ Recherche de ${results.length} résultats en ${searchTime}ms`);
    
    // Compter le total
    const countStmt = db.prepare('SELECT COUNT(*) as total FROM test_livres');
    const count = countStmt.get();
    console.log(`   📈 Total de livres dans la base: ${count.total}`);
    
    db.close();
    return true;
  } catch (error) {
    console.log(`   ❌ Erreur de performance: ${error.message}`);
    return false;
  }
}

// Test de persistance
function testPersistence() {
  console.log('\n💾 Test 4: Persistance des données...');
  
  const dbPath = getDatabasePath();
  
  try {
    // Première connexion - ajouter des données
    let db = new Database(dbPath);
    const insertStmt = db.prepare(`
      INSERT INTO test_livres (titre, auteur, disponible) 
      VALUES (?, ?, ?)
    `);
    insertStmt.run('Livre Persistance', 'Test Auteur', 1);
    db.close();
    console.log('   ✅ Données ajoutées et connexion fermée');
    
    // Deuxième connexion - vérifier les données
    db = new Database(dbPath);
    const selectStmt = db.prepare('SELECT * FROM test_livres WHERE titre = ?');
    const book = selectStmt.get('Livre Persistance');
    
    if (book) {
      console.log('   ✅ Données persistées correctement après reconnexion');
    } else {
      console.log('   ❌ Données perdues après reconnexion');
      return false;
    }
    
    db.close();
    return true;
  } catch (error) {
    console.log(`   ❌ Erreur de persistance: ${error.message}`);
    return false;
  }
}

// Nettoyage
function cleanup() {
  console.log('\n🧹 Nettoyage...');
  const dbPath = getDatabasePath();
  
  try {
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
      console.log('   ✅ Fichier de test supprimé');
    }
  } catch (error) {
    console.log(`   ⚠️ Impossible de supprimer le fichier de test: ${error.message}`);
  }
}

// Exécution des tests
async function runAllTests() {
  console.log('🚀 Début des tests de la base de données BiblioTech\n');
  
  const tests = [
    { name: 'Création de la base de données', fn: testDatabaseCreation },
    { name: 'Opérations CRUD', fn: testCRUDOperations },
    { name: 'Performance', fn: testPerformance },
    { name: 'Persistance', fn: testPersistence }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    if (test.fn()) {
      passed++;
    } else {
      failed++;
    }
  }
  
  console.log('\n📊 Résultats des tests:');
  console.log(`   ✅ Tests réussis: ${passed}`);
  console.log(`   ❌ Tests échoués: ${failed}`);
  console.log(`   📈 Taux de réussite: ${Math.round((passed / tests.length) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 Tous les tests sont passés ! La base de données fonctionne parfaitement.');
  } else {
    console.log('\n⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.');
  }
  
  cleanup();
}

// Lancer les tests
runAllTests().catch(console.error);

# BiblioTech Production Installer Build Script
# Automates the complete build process for Windows installer

param(
    [switch]$Clean = $false,
    [switch]$Test = $false,
    [string]$Version = ""
)

Write-Host "🚀 BiblioTech Production Installer Build Script" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Verify prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "❌ npm not found. Please install npm" -ForegroundColor Red
    exit 1
}

# Check Node.js version
$nodeVersion = node --version
Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green

# Check if package.json exists
if (-not (Test-Path "package.json")) {
    Write-Host "❌ package.json not found. Please run this script from the BiblioTech project root." -ForegroundColor Red
    exit 1
}

# Update version if specified
if ($Version -ne "") {
    Write-Host "📝 Updating version to $Version..." -ForegroundColor Yellow
    npm version $Version --no-git-tag-version
}

# Clean build if requested
if ($Clean) {
    Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
    npm run clean
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Clean failed" -ForegroundColor Red
        exit 1
    }
}

# Install/update dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ npm install failed" -ForegroundColor Red
    exit 1
}

# Rebuild native dependencies
Write-Host "🔧 Rebuilding native dependencies..." -ForegroundColor Yellow
npx electron-rebuild
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ electron-rebuild had issues, continuing..." -ForegroundColor Yellow
}

# Build the application
Write-Host "🏗️ Building React application..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ React build failed" -ForegroundColor Red
    exit 1
}

# Package the Electron app
Write-Host "📦 Packaging Electron application..." -ForegroundColor Yellow
npm run pack-win
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Electron packaging failed" -ForegroundColor Red
    exit 1
}

# Create the installer
Write-Host "🎁 Creating NSIS installer..." -ForegroundColor Yellow
npx electron-builder --prepackaged out/BiblioTech-win32-x64 --win
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Installer creation failed" -ForegroundColor Red
    exit 1
}

# Get build information
$packageJson = Get-Content "package.json" | ConvertFrom-Json
$version = $packageJson.version
$installerPath = "dist-electron\BiblioTech-Setup-$version.exe"

if (Test-Path $installerPath) {
    $installerSize = [math]::Round((Get-Item $installerPath).Length / 1MB, 2)
    Write-Host "✅ Installer created successfully!" -ForegroundColor Green
    Write-Host "📁 Location: $installerPath" -ForegroundColor Cyan
    Write-Host "📏 Size: $installerSize MB" -ForegroundColor Cyan
} else {
    Write-Host "❌ Installer file not found at expected location" -ForegroundColor Red
    exit 1
}

# Test the installer if requested
if ($Test) {
    Write-Host "🧪 Testing installer..." -ForegroundColor Yellow
    
    # Check if installer runs without errors
    $testResult = Start-Process -FilePath $installerPath -ArgumentList "/S" -Wait -PassThru -NoNewWindow
    if ($testResult.ExitCode -eq 0) {
        Write-Host "✅ Installer test passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Installer test had issues (Exit code: $($testResult.ExitCode))" -ForegroundColor Yellow
    }
}

# Display summary
Write-Host ""
Write-Host "🎉 Build Summary" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
Write-Host "Version: $version" -ForegroundColor White
Write-Host "Installer: $installerPath" -ForegroundColor White
Write-Host "Size: $installerSize MB" -ForegroundColor White
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Test the installer on a clean Windows system" -ForegroundColor White
Write-Host "2. Verify all features work correctly" -ForegroundColor White
Write-Host "3. Distribute to end users" -ForegroundColor White
Write-Host ""
Write-Host "🔐 Security Note:" -ForegroundColor Yellow
Write-Host "The installer is unsigned and will show Windows security warnings." -ForegroundColor White
Write-Host "Users should click 'More info' → 'Run anyway' to install." -ForegroundColor White
Write-Host ""
Write-Host "✅ Build completed successfully!" -ForegroundColor Green

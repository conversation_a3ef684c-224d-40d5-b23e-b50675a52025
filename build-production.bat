@echo off
REM BiblioTech Production Build Script (Batch Wrapper)
REM This script provides an easy way to run the PowerShell build script

echo.
echo ========================================
echo   BiblioTech Production Build Script
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

REM Check execution policy
echo Checking PowerShell execution policy...
for /f "tokens=*" %%i in ('powershell -Command "Get-ExecutionPolicy"') do set EXEC_POLICY=%%i
echo Current execution policy: %EXEC_POLICY%

if "%EXEC_POLICY%"=="Restricted" (
    echo.
    echo WARNING: PowerShell execution policy is Restricted
    echo This may prevent the build script from running
    echo.
    echo To fix this, run PowerShell as Administrator and execute:
    echo Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
    echo.
    pause
)

REM Parse command line arguments
set CLEAN_BUILD=
set SKIP_TESTS=
set OUTPUT_DIR=

:parse_args
if "%1"=="" goto run_script
if /i "%1"=="--clean" set CLEAN_BUILD=-CleanBuild
if /i "%1"=="--skip-tests" set SKIP_TESTS=-SkipTests
if /i "%1"=="--output" (
    shift
    set OUTPUT_DIR=-OutputDir "%1"
)
shift
goto parse_args

:run_script
echo.
echo Running PowerShell build script...
echo Command: powershell -ExecutionPolicy Bypass -File "build-production-complete.ps1" %CLEAN_BUILD% %SKIP_TESTS% %OUTPUT_DIR%
echo.

REM Run the PowerShell script with bypass execution policy
powershell -ExecutionPolicy Bypass -File "build-production-complete.ps1" %CLEAN_BUILD% %SKIP_TESTS% %OUTPUT_DIR%

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above for details
    pause
    exit /b 1
) else (
    echo.
    echo BUILD COMPLETED SUCCESSFULLY!
    echo.
    echo Available options:
    echo - Press any key to exit
    echo - Open production-builds folder to see the results
    pause
)

exit /b 0

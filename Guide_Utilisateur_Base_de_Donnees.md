# 📚 BiblioTech - Guide de la Base de Données

## 🎯 Qu'est-ce que la Base de Données ?

La base de données de BiblioTech est comme un **classeur électronique ultra-organisé** qui stocke toutes les informations de votre bibliothèque :
- 📖 Tous vos livres
- 👥 Tous vos membres
- 📋 Tous les emprunts et retours
- 👤 Les comptes administrateurs
- 📊 L'historique de toutes les actions

## 📍 Où sont Stockées vos Données ?

Vos données sont sauvegardées automatiquement dans un fichier sécurisé sur votre ordinateur :

**📁 Emplacement :** `C:\Users\<USER>\AppData\Roaming\bibliotech\bibliotech.db`

> **🛡️ Sécurité :** Vos données restent sur VOTRE ordinateur, jamais sur internet !

## 🔄 Comment ça Fonctionne ?

### ✅ Sauvegarde Automatique
- Chaque action est **immédiatement sauvegardée**
- Ajout d'un livre → Sauvegardé instantanément
- Création d'un membre → Sauvegardé instantanément  
- Enregistrement d'un emprunt → Sauvegardé instantanément

### 🔒 Sécurité des Données
- **Accès protégé** : Seuls les administrateurs autorisés peuvent accéder
- **Stockage local** : Aucune donnée n'est envoyée sur internet
- **Sauvegarde fiable** : Même si l'application se ferme, vos données sont en sécurité

### 🏗️ Organisation Intelligente
La base de données organise vos informations en **6 sections principales** :

1. **📚 LIVRES** - Tous vos ouvrages avec détails complets
2. **🏷️ CATÉGORIES** - Classification de vos livres
3. **👥 MEMBRES** - Informations des emprunteurs
4. **📋 EMPRUNTS** - Suivi des prêts en cours et historique
5. **👤 UTILISATEURS** - Comptes administrateurs
6. **📊 JOURNAUX** - Historique de toutes les actions

## 🛠️ Pour les Administrateurs Techniques

### Accéder à la Base de Données

**Option 1 : DB Browser for SQLite (Recommandé)**
1. Téléchargez : https://sqlitebrowser.org/dl/
2. Installez l'application
3. Ouvrez le fichier : `bibliotech.db`
4. Explorez les tables et données

**Option 2 : Visualiseur en ligne**
- Site : https://sqliteviewer.app/
- ⚠️ **Attention** : Ne jamais télécharger de données sensibles !

### Structure Technique

```
📊 TABLES PRINCIPALES :
├── livres (Catalogue complet)
├── categories (Classification)
├── membres (Base des emprunteurs)
├── emprunts (Gestion des prêts)
├── utilisateurs (Comptes admin)
└── journaux_audit (Traçabilité)
```

### Requêtes Utiles

**Statistiques rapides :**
```sql
-- Nombre total de livres
SELECT COUNT(*) FROM livres;

-- Livres disponibles
SELECT COUNT(*) FROM livres WHERE disponible = 1;

-- Emprunts actifs
SELECT COUNT(*) FROM emprunts WHERE statut = 'actif';
```

**Rapport d'activité :**
```sql
-- Emprunts par membre
SELECT m.prenom, m.nom, COUNT(e.id) as nb_emprunts
FROM membres m
LEFT JOIN emprunts e ON m.id = e.membre_id
GROUP BY m.id
ORDER BY nb_emprunts DESC;
```

## 🚨 Précautions Importantes

### ✅ À FAIRE :
- **Sauvegarde régulière** : Copiez le fichier `bibliotech.db` ailleurs
- **Fermer l'app** avant d'ouvrir la base avec un autre outil
- **Mode lecture seule** pour consulter sans risque
- **Vérifier les permissions** d'accès au fichier

### ❌ À ÉVITER :
- Modifier directement la base pendant que BiblioTech fonctionne
- Supprimer ou déplacer le fichier `bibliotech.db`
- Partager la base de données sans chiffrement
- Ouvrir avec plusieurs outils simultanément

## 🔧 Résolution de Problèmes

### Problème : "Base de données verrouillée"
**Solution :** Fermez complètement BiblioTech et réessayez

### Problème : "Fichier introuvable"
**Solution :** Vérifiez le chemin : `%APPDATA%\bibliotech\bibliotech.db`

### Problème : "Accès refusé"
**Solution :** Exécutez en tant qu'administrateur

## 📞 Support Technique

Pour toute question technique sur la base de données :
1. Consultez d'abord ce guide
2. Vérifiez les journaux d'audit dans l'application
3. Contactez l'administrateur système

---

## 🎓 Formation des Utilisateurs

### Message Simple pour les Utilisateurs :

> **"BiblioTech sauvegarde automatiquement tout ce que vous faites. Vos livres, membres et emprunts sont en sécurité sur votre ordinateur. Vous n'avez rien de spécial à faire - l'application s'occupe de tout !"**

### Points Clés à Retenir :
- ✅ Sauvegarde automatique et instantanée
- ✅ Données sécurisées localement
- ✅ Aucune action manuelle requise
- ✅ Accès protégé par mot de passe
- ✅ Historique complet de toutes les actions

---

*📅 Dernière mise à jour : Janvier 2025*
*🔧 Version BiblioTech : 1.0.2*

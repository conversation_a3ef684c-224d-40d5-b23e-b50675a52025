import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { PlusIcon, EditIcon, TrashIcon, CheckIcon } from './Icons';
import type { Category } from '../types/library';
import ConfirmationModal from './ConfirmationModal';
import { useToast } from '../hooks/useToast';

interface CategoryManagementProps {
  categories: Category[];
  onAddCategory: (name: string, description?: string) => Promise<boolean>;
  onUpdateCategory: (id: number, name: string, description?: string) => Promise<boolean>;
  onDeleteCategory: (id: number) => Promise<boolean>;
  onReorderCategories: (categories: Category[]) => Promise<boolean>;
  booksInCategory: (categoryName: string) => Promise<number>;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  categories,
  onAddCategory,
  onUpdateCategory,
  onDeleteCategory,
  onReorderCategories,
  booksInCategory
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [newCategory, setNewCategory] = useState({ name: '', description: '' });
  const [editCategory, setEditCategory] = useState({ name: '', description: '' });
  const [draggedItem, setDraggedItem] = useState<number | null>(null);
  const [addErrors, setAddErrors] = useState<Record<string, string>>({});
  const [editErrors, setEditErrors] = useState<Record<string, string>>({});
  const [bookCounts, setBookCounts] = useState<Record<string, number>>({});
  const { logAction } = useAuth();
  const { showSuccess } = useToast();

  // Load book counts when categories change
  useEffect(() => {
    const loadBookCounts = async () => {
      const counts: Record<string, number> = {};
      for (const category of categories) {
        try {
          counts[category.name] = await booksInCategory(category.name);
        } catch (error) {
          console.error(`Error loading book count for ${category.name}:`, error);
          counts[category.name] = 0;
        }
      }
      setBookCounts(counts);
    };

    if (categories.length > 0) {
      loadBookCounts();
    }
  }, [categories, booksInCategory]);

  // Confirmation modal state
  const [confirmationModal, setConfirmationModal] = useState<{
    isOpen: boolean;
    categoryId: number;
    categoryName: string;
    bookCount: number;
  }>({
    isOpen: false,
    categoryId: 0,
    categoryName: '',
    bookCount: 0
  });

  const validateAddForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!newCategory.name || newCategory.name.trim() === '') {
      newErrors.name = 'Le nom de la catégorie est requis';
    } else if (newCategory.name.trim().length < 2) {
      newErrors.name = 'Le nom de la catégorie doit contenir au moins 2 caractères';
    } else if (categories.some(cat => cat.name.toLowerCase().trim() === newCategory.name.toLowerCase().trim())) {
      newErrors.name = 'Une catégorie avec ce nom existe déjà';
    }

    setAddErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateEditForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!editCategory.name || editCategory.name.trim() === '') {
      newErrors.name = 'Le nom de la catégorie est requis';
    } else if (editCategory.name.trim().length < 2) {
      newErrors.name = 'Le nom de la catégorie doit contenir au moins 2 caractères';
    } else if (editingId && categories.some(cat =>
      cat.id !== editingId && cat.name.toLowerCase().trim() === editCategory.name.toLowerCase().trim()
    )) {
      newErrors.name = 'Une catégorie avec ce nom existe déjà';
    }

    setEditErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddInputChange = (field: string, value: string) => {
    setNewCategory(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (addErrors[field]) {
      setAddErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleEditInputChange = (field: string, value: string) => {
    setEditCategory(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (editErrors[field]) {
      setEditErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleAddCategory = async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (!validateAddForm()) {
      const element = document.getElementById('add-category-name');
      element?.focus();
      return;
    }

    try {
      const success = await onAddCategory(newCategory.name.trim(), newCategory.description.trim() || undefined);
      if (success) {
        setNewCategory({ name: '', description: '' });
        setAddErrors({});
        setIsAdding(false);
        logAction('CREATE', 'CATEGORY', undefined, `Catégorie créée: ${newCategory.name}`);
        showSuccess(`La catégorie "${newCategory.name}" a été créée avec succès`);
      } else {
        // Afficher une erreur générique si la fonction retourne false sans lever d'exception
        setAddErrors({ name: 'Erreur lors de la création de la catégorie' });
      }
    } catch (error) {
      // Afficher l'erreur spécifique retournée par la base de données
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue lors de la création';
      setAddErrors({ name: errorMessage });

      // Mettre le focus sur le champ nom si l'erreur concerne un nom dupliqué
      if (errorMessage.includes('existe déjà')) {
        const element = document.getElementById('add-category-name');
        element?.focus();
      }
    }
  };

  const handleUpdateCategory = async (id: number, e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (!validateEditForm()) {
      const element = document.getElementById('edit-category-name');
      element?.focus();
      return;
    }

    try {
      const success = await onUpdateCategory(id, editCategory.name.trim(), editCategory.description.trim() || undefined);
      if (success) {
        setEditingId(null);
        setEditCategory({ name: '', description: '' });
        setEditErrors({});
        logAction('UPDATE', 'CATEGORY', id, `Catégorie modifiée: ${editCategory.name}`);
        showSuccess(`La catégorie "${editCategory.name}" a été modifiée avec succès`);
      } else {
        // Afficher une erreur générique si la fonction retourne false sans lever d'exception
        setEditErrors({ name: 'Erreur lors de la modification de la catégorie' });
      }
    } catch (error) {
      // Afficher l'erreur spécifique retournée par la base de données
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue lors de la modification';
      setEditErrors({ name: errorMessage });

      // Mettre le focus sur le champ nom si l'erreur concerne un nom dupliqué
      if (errorMessage.includes('existe déjà')) {
        const element = document.getElementById('edit-category-name');
        element?.focus();
      }
    }
  };

  const handleDeleteCategoryClick = async (id: number, name: string) => {
    try {
      const bookCount = await booksInCategory(name);
      setConfirmationModal({
        isOpen: true,
        categoryId: id,
        categoryName: name,
        bookCount
      });
    } catch (error) {
      console.error('Error getting book count:', error);
      setConfirmationModal({
        isOpen: true,
        categoryId: id,
        categoryName: name,
        bookCount: 0
      });
    }
  };

  const handleConfirmDeleteCategory = async () => {
    const success = await onDeleteCategory(confirmationModal.categoryId);
    if (success) {
      logAction('DELETE', 'CATEGORY', confirmationModal.categoryId, `Catégorie supprimée: ${confirmationModal.categoryName}`);
    } else {
      alert('Erreur lors de la suppression de la catégorie');
    }
    setConfirmationModal({
      isOpen: false,
      categoryId: 0,
      categoryName: '',
      bookCount: 0
    });
  };

  const handleCloseConfirmationModal = () => {
    setConfirmationModal({
      isOpen: false,
      categoryId: 0,
      categoryName: '',
      bookCount: 0
    });
  };

  const startEdit = (category: Category) => {
    setEditingId(category.id);
    setEditCategory({ name: category.name, description: category.description || '' });
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditCategory({ name: '', description: '' });
    setEditErrors({});
  };

  const handleDragStart = (e: React.DragEvent, id: number) => {
    setDraggedItem(id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, targetId: number) => {
    e.preventDefault();
    
    if (draggedItem === null || draggedItem === targetId) {
      setDraggedItem(null);
      return;
    }

    const draggedIndex = categories.findIndex(cat => cat.id === draggedItem);
    const targetIndex = categories.findIndex(cat => cat.id === targetId);

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedItem(null);
      return;
    }

    const newCategories = [...categories];
    const [draggedCategory] = newCategories.splice(draggedIndex, 1);
    newCategories.splice(targetIndex, 0, draggedCategory);

    // Update order values
    const reorderedCategories = newCategories.map((cat, index) => ({
      ...cat,
      order: index
    }));

    const success = await onReorderCategories(reorderedCategories);
    if (success) {
      logAction('REORDER', 'CATEGORY', undefined, 'Catégories réorganisées');
    }

    setDraggedItem(null);
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title">Gestion des catégories</h2>
            <p className="card-subtitle">Organisez et gérez les catégories de livres</p>
          </div>
          <button 
            onClick={() => setIsAdding(true)} 
            className="btn btn-primary"
            disabled={isAdding}
          >
            <PlusIcon size={16} />
            Nouvelle catégorie
          </button>
        </div>
      </div>

      {/* Icon Legend */}
      <div className="legend-section">
        <h3 className="legend-title">Guide des icônes d'action</h3>
        <div className="icon-legend">
          <div className="legend-item">
            <div className="legend-icon legend-icon--secondary">
              <EditIcon size={16} />
            </div>
            <span className="legend-text">Modifier</span>
          </div>
          <div className="legend-item">
            <div className="legend-icon legend-icon--danger">
              <TrashIcon size={16} />
            </div>
            <span className="legend-text">Supprimer</span>
          </div>
        </div>
      </div>

      <div className="card-content">
        {/* Add new category form */}
        {isAdding && (
          <div className="card mb-6" style={{ border: '2px solid var(--color-primary-200)' }}>
            <div className="card-content">
              <form onSubmit={handleAddCategory} noValidate>
                <div className="form-row">
                  <div className="form-group">
                    <label className="form-label">Nom de la catégorie *</label>
                    <input
                      type="text"
                      id="add-category-name"
                      className={`form-input ${addErrors.name ? 'error' : ''}`}
                      value={newCategory.name}
                      onChange={(e) => handleAddInputChange('name', e.target.value)}
                      placeholder="Ex: Science-Fiction"
                      autoFocus
                    />
                    {addErrors.name && <span className="error-message">{addErrors.name}</span>}
                  </div>
                  <div className="form-group">
                    <label className="form-label">Description (optionnel)</label>
                    <input
                      type="text"
                      className="form-input"
                      value={newCategory.description}
                      onChange={(e) => handleAddInputChange('description', e.target.value)}
                      placeholder="Description de la catégorie"
                    />
                  </div>
                </div>

                {/* General form error message */}
                {Object.keys(addErrors).length > 0 && (
                  <div className="form-error-summary">
                    <p className="error-message">
                      Veuillez corriger les erreurs ci-dessus avant de soumettre le formulaire.
                    </p>
                  </div>
                )}

                <div className="flex gap-3 justify-end mt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setIsAdding(false);
                      setNewCategory({ name: '', description: '' });
                      setAddErrors({});
                    }}
                    className="btn btn-secondary"
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="btn btn-primary"
                  >
                    <CheckIcon size={16} />
                    Créer
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Categories list */}
        <div className="space-y-3">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`card ${draggedItem === category.id ? 'opacity-50' : ''}`}
              draggable
              onDragStart={(e) => handleDragStart(e, category.id)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, category.id)}
              style={{ cursor: 'move' }}
            >
              <div className="card-content">
                {editingId === category.id ? (
                  // Edit mode
                  <div>
                    <form onSubmit={(e) => handleUpdateCategory(category.id, e)} noValidate>
                      <div className="form-row">
                        <div className="form-group">
                          <label className="form-label">Nom de la catégorie *</label>
                          <input
                            type="text"
                            id="edit-category-name"
                            className={`form-input ${editErrors.name ? 'error' : ''}`}
                            value={editCategory.name}
                            onChange={(e) => handleEditInputChange('name', e.target.value)}
                            autoFocus
                          />
                          {editErrors.name && <span className="error-message">{editErrors.name}</span>}
                        </div>
                        <div className="form-group">
                          <label className="form-label">Description</label>
                          <input
                            type="text"
                            className="form-input"
                            value={editCategory.description}
                            onChange={(e) => handleEditInputChange('description', e.target.value)}
                          />
                        </div>
                      </div>

                      {/* General form error message */}
                      {Object.keys(editErrors).length > 0 && (
                        <div className="form-error-summary">
                          <p className="error-message">
                            Veuillez corriger les erreurs ci-dessus avant de soumettre le formulaire.
                          </p>
                        </div>
                      )}

                      <div className="flex gap-3 justify-end mt-4">
                        <button type="button" onClick={cancelEdit} className="btn btn-secondary">
                          Annuler
                        </button>
                        <button
                          type="submit"
                          className="btn btn-primary"
                        >
                          <CheckIcon size={16} />
                          Sauvegarder
                        </button>
                      </div>
                    </form>
                  </div>
                ) : (
                  // View mode
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div className="text-lg font-semibold">{category.name}</div>
                        <div className="badge badge-neutral">
                          {bookCounts[category.name] || 0} livre(s)
                        </div>
                      </div>
                      {category.description && (
                        <div className="text-sm text-neutral mt-1">{category.description}</div>
                      )}
                      <div className="text-xs text-neutral mt-1">
                        Créée le {new Date(category.createdAt).toLocaleDateString('fr-FR')}
                      </div>
                    </div>
                    <div className="action-buttons">
                      <button
                        onClick={() => startEdit(category)}
                        className="action-btn action-btn--secondary action-btn--sm"
                      >
                        <EditIcon size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteCategoryClick(category.id, category.name)}
                        className="action-btn action-btn--danger action-btn--sm"
                      >
                        <TrashIcon size={16} />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {categories.length === 0 && (
          <div className="text-center py-8 text-neutral">
            <p>Aucune catégorie créée. Commencez par ajouter une nouvelle catégorie.</p>
          </div>
        )}

        <div className="mt-6 p-4 bg-neutral-50 rounded-lg">
          <h4 className="font-semibold mb-2">💡 Conseils :</h4>
          <ul className="text-sm text-neutral space-y-1">
            <li>• Glissez-déposez les catégories pour les réorganiser</li>
            <li>• Les catégories avec des livres nécessitent une confirmation pour être supprimées</li>
            <li>• Les noms de catégories doivent être uniques</li>
          </ul>
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmationModal.isOpen}
        onClose={handleCloseConfirmationModal}
        onConfirm={handleConfirmDeleteCategory}
        title="Supprimer la catégorie"
        message={
          confirmationModal.bookCount > 0
            ? `Cette catégorie "${confirmationModal.categoryName}" contient ${confirmationModal.bookCount} livre(s). Êtes-vous sûr de vouloir la supprimer ? Les livres seront déplacés vers "Non classé".`
            : `Êtes-vous sûr de vouloir supprimer la catégorie "${confirmationModal.categoryName}" ?`
        }
        confirmText="Supprimer"
        variant="danger"
      />
    </div>
  );
};

export default CategoryManagement;

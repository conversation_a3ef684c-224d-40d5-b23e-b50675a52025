// Enhanced member management types for BiblioTech

export interface LibraryMember {
  id: number;
  nom: string; // Last name
  prenom: string; // First name
  fonction: string; // Role/Function
  numeroMembre: string; // Unique membership number
  telephone: string;
  adresse: string;
  email?: string;
  photo: string; // URL to profile picture
  dateInscription: string; // Registration date
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BorrowedBook {
  id: number;
  livreId: number;
  titre: string;
  auteur: string;
  dateEmprunt: string;
  dateRetour: string; // Due date
  isOverdue: boolean;
  daysUntilDue: number;
}

export interface MemberProfile extends LibraryMember {
  livresEmpruntes: BorrowedBook[];
  nombreLivresEmpruntes: number;
  nombreLivresEnRetard: number;
}

export interface CreateMemberData {
  nom: string;
  prenom: string;
  fonction: string;
  telephone: string;
  adresse: string;
  email?: string;
  photo?: string;
}

export interface UpdateMemberData extends Partial<CreateMemberData> {
  isActive?: boolean;
}

// Utility function to generate membership number
export const generateMembershipNumber = (id: number): string => {
  const year = new Date().getFullYear();
  const paddedId = id.toString().padStart(4, '0');
  return `BT${year}${paddedId}`;
};

// Utility function to calculate overdue status
export const calculateOverdueStatus = (dateRetour: string): { isOverdue: boolean; daysUntilDue: number } => {
  const dueDate = new Date(dateRetour);
  const today = new Date();
  const diffTime = dueDate.getTime() - today.getTime();
  const daysUntilDue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return {
    isOverdue: daysUntilDue < 0,
    daysUntilDue
  };
};

// Utility function to calculate subscription end date (1 year from registration)
export const calculateSubscriptionEndDate = (dateInscription: string): string => {
  const registrationDate = new Date(dateInscription);
  const endDate = new Date(registrationDate);
  endDate.setFullYear(endDate.getFullYear() + 1);
  return endDate.toISOString().split('T')[0];
};

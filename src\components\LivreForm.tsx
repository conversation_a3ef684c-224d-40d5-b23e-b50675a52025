// Composant pour ajouter un nouveau livre
import React, { useState } from 'react';
import { PlusIcon } from './Icons';
import type { BookImportData } from '../types/library';

interface Props {
  onAdd: (livre: BookImportData) => void;
  categories: string[];
}

const LivreForm: React.FC<Props> = ({ onAdd, categories }) => {
  const [formData, setFormData] = useState<BookImportData>({
    titre: '',
    auteur: '',
    categorie: categories[0] || '',
    genre: '',
    isbn: '',
    anneePublication: undefined,
    description: '',
    quantite: 1 // Default quantity is 1
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required field validation with French messages
    if (!formData.titre || formData.titre.trim() === '') {
      newErrors.titre = 'Le titre est requis';
    }

    if (!formData.auteur || formData.auteur.trim() === '') {
      newErrors.auteur = 'L\'auteur est requis';
    }

    if (!formData.categorie || formData.categorie.trim() === '') {
      newErrors.categorie = 'La catégorie est requise';
    }

    if (!formData.genre || formData.genre.trim() === '') {
      newErrors.genre = 'Le genre est requis';
    }

    // Quantity validation
    if (formData.quantite !== undefined) {
      if (!Number.isInteger(formData.quantite) || formData.quantite < 1) {
        newErrors.quantite = 'La quantité doit être un nombre entier positif';
      } else if (formData.quantite > 1000) {
        newErrors.quantite = 'La quantité ne peut pas dépasser 1000';
      }
    }

    // Optional field validation
    if (formData.isbn && formData.isbn.trim()) {
      // More flexible ISBN validation
      const cleanIsbn = formData.isbn.replace(/[-\s]/g, '');
      if (!/^[\dX]+$/.test(cleanIsbn) || (cleanIsbn.length !== 10 && cleanIsbn.length !== 13)) {
        newErrors.isbn = 'Format ISBN invalide (10 ou 13 chiffres)';
      }
    }

    if (formData.anneePublication) {
      const currentYear = new Date().getFullYear();
      if (formData.anneePublication < 1000 || formData.anneePublication > currentYear) {
        newErrors.anneePublication = `Année invalide (entre 1000 et ${currentYear})`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Always validate on submit attempt
    const isValid = validateForm();

    if (!isValid) {
      // Focus on the first required field that's empty or invalid
      const requiredFields = ['titre', 'auteur', 'categorie', 'genre', 'quantite'];
      for (const field of requiredFields) {
        if (errors[field]) {
          const element = document.getElementById(field);
          element?.focus();
          break;
        }
      }
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Prepare data for submission
      const bookData: BookImportData = {
        titre: formData.titre.trim(),
        auteur: formData.auteur.trim(),
        categorie: formData.categorie.trim(),
        genre: formData.genre.trim(),
        isbn: formData.isbn?.trim() || undefined,
        anneePublication: formData.anneePublication || undefined,
        description: formData.description?.trim() || undefined,
        quantite: formData.quantite || 1
      };

      onAdd(bookData);

      // Reset form
      setFormData({
        titre: '',
        auteur: '',
        categorie: categories[0] || '',
        genre: '',
        isbn: '',
        anneePublication: undefined,
        description: '',
        quantite: 1
      });
      setErrors({});
    } catch (error) {
      console.error('Erreur lors de l\'ajout du livre:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof BookImportData, value: string | number | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  return (
    <div className="card mb-8">
      <div className="card-header">
        <h2 className="card-title">Ajouter un nouveau livre</h2>
        <p className="card-subtitle">Enregistrez un nouveau livre dans votre collection</p>
      </div>
      <div className="card-content">
        <form onSubmit={handleSubmit} noValidate>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="titre" className="form-label">Titre *</label>
              <input
                id="titre"
                type="text"
                className={`form-input ${errors.titre ? 'error' : ''}`}
                placeholder="Entrez le titre du livre"
                value={formData.titre}
                onChange={e => handleInputChange('titre', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.titre && <span className="error-message">{errors.titre}</span>}
            </div>
            <div className="form-group">
              <label htmlFor="auteur" className="form-label">Auteur *</label>
              <input
                id="auteur"
                type="text"
                className={`form-input ${errors.auteur ? 'error' : ''}`}
                placeholder="Nom de l'auteur"
                value={formData.auteur}
                onChange={e => handleInputChange('auteur', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.auteur && <span className="error-message">{errors.auteur}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="categorie" className="form-label">Catégorie *</label>
              <select
                id="categorie"
                className={`form-select ${errors.categorie ? 'error' : ''}`}
                value={formData.categorie}
                onChange={e => handleInputChange('categorie', e.target.value)}
                disabled={isSubmitting}
              >
                <option value="">Sélectionner une catégorie</option>
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
              {errors.categorie && <span className="error-message">{errors.categorie}</span>}
            </div>
            <div className="form-group">
              <label htmlFor="genre" className="form-label">Genre *</label>
              <input
                id="genre"
                type="text"
                className={`form-input ${errors.genre ? 'error' : ''}`}
                placeholder="Genre littéraire"
                value={formData.genre}
                onChange={e => handleInputChange('genre', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.genre && <span className="error-message">{errors.genre}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="quantite" className="form-label">Quantité *</label>
              <input
                id="quantite"
                type="number"
                className={`form-input ${errors.quantite ? 'error' : ''}`}
                placeholder="1"
                value={formData.quantite || 1}
                onChange={e => handleInputChange('quantite', e.target.value ? parseInt(e.target.value) : 1)}
                min="1"
                max="1000"
                disabled={isSubmitting}
              />
              {errors.quantite && <span className="error-message">{errors.quantite}</span>}
              <small className="form-help">Nombre de copies de ce livre à ajouter à la collection</small>
            </div>
            <div className="form-group">
              <label htmlFor="isbn" className="form-label">ISBN (optionnel)</label>
              <input
                id="isbn"
                type="text"
                className={`form-input ${errors.isbn ? 'error' : ''}`}
                placeholder="978-2-123456-78-9"
                value={formData.isbn || ''}
                onChange={e => handleInputChange('isbn', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.isbn && <span className="error-message">{errors.isbn}</span>}
            </div>
            <div className="form-group">
              <label htmlFor="anneePublication" className="form-label">Année de publication (optionnel)</label>
              <input
                id="anneePublication"
                type="number"
                className={`form-input ${errors.anneePublication ? 'error' : ''}`}
                placeholder="2023"
                value={formData.anneePublication || ''}
                onChange={e => handleInputChange('anneePublication', e.target.value ? parseInt(e.target.value) : undefined)}
                min="1000"
                max={new Date().getFullYear()}
                disabled={isSubmitting}
              />
              {errors.anneePublication && <span className="error-message">{errors.anneePublication}</span>}
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="description" className="form-label">Description (optionnel)</label>
            <textarea
              id="description"
              className="form-input"
              rows={3}
              placeholder="Description du livre..."
              value={formData.description || ''}
              onChange={e => handleInputChange('description', e.target.value)}
              disabled={isSubmitting}
            />
          </div>

          {/* General form error message */}
          {Object.keys(errors).length > 0 && (
            <div className="form-error-summary">
              <p className="error-message">
                Veuillez corriger les erreurs ci-dessus avant de soumettre le formulaire.
              </p>
            </div>
          )}

          <div className="form-actions">
            <button
              type="submit"
              className={`btn btn-primary ${isSubmitting ? 'btn-loading' : ''}`}
              disabled={isSubmitting}
            >
              <PlusIcon size={16} />
              {isSubmitting ? 'Ajout en cours...' : 'Ajouter le livre'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LivreForm;

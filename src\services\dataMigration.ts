// Script de migration des données pour BiblioTech
// Data migration script to populate database with existing mock data

import { getDatabase } from './database';
import type { Book } from '../types/library';
import type { LibraryMember } from '../types/member';


// Données initiales des catégories
const INITIAL_CATEGORIES = [
  { name: 'Fiction', description: 'Romans, nouvelles et œuvres littéraires' },
  { name: 'Science', description: 'Ouvrages scientifiques et techniques' },
  { name: 'His<PERSON>', description: 'Livres d\'histoire et biographies' },
  { name: 'Philosophie', description: 'Ouvrages philosophiques et de réflexion' },
  { name: 'Art', description: 'Livres sur l\'art, la musique et la culture' },
  { name: '<PERSON><PERSON><PERSON>', description: 'Livres pour enfants et adolescents' },
  { name: 'Non classé', description: 'Livres sans catégorie spécifique' }
];

// Données initiales des livres
const INITIAL_BOOKS: Omit<Book, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    titre: '<PERSON>',
    auteur: '<PERSON>',
    categorie: 'Fiction',
    genre: 'Conte',
    isbn: '9782070408504',
    anneePublication: 1943,
    description: 'Un conte poétique et philosophique',
    disponible: false,
    quantiteTotale: 3,
    quantiteDisponible: 2,
    emprunteurId: 1,
    dateEmprunt: '2025-06-01',
    dateRetour: '2025-06-15'
  },
  {
    titre: 'Une brève histoire du temps',
    auteur: 'Stephen Hawking',
    categorie: 'Science',
    genre: 'Physique',
    isbn: '9782070368228',
    anneePublication: 1988,
    disponible: true,
    quantiteTotale: 2,
    quantiteDisponible: 2
  },
  {
    titre: 'L\'Étranger',
    auteur: 'Albert Camus',
    categorie: 'Fiction',
    genre: 'Roman',
    isbn: '9782070360024',
    anneePublication: 1942,
    description: 'Roman existentialiste emblématique',
    disponible: false,
    quantiteTotale: 1,
    quantiteDisponible: 0,
    emprunteurId: 2,
    dateEmprunt: '2025-05-20',
    dateRetour: '2025-06-20'
  },
  {
    titre: 'Sapiens: Une brève histoire de l\'humanité',
    auteur: 'Yuval Noah Harari',
    categorie: 'Histoire',
    genre: 'Essai',
    isbn: '9782226257017',
    anneePublication: 2011,
    description: 'Une perspective fascinante sur l\'évolution humaine',
    disponible: true,
    quantiteTotale: 4,
    quantiteDisponible: 4
  },
  {
    titre: 'L\'Art de la guerre',
    auteur: 'Sun Tzu',
    categorie: 'Philosophie',
    genre: 'Traité',
    anneePublication: -500,
    description: 'Traité de stratégie militaire et philosophique',
    disponible: true,
    quantiteTotale: 1,
    quantiteDisponible: 1
  },
  {
    titre: 'Harry Potter à l\'école des sorciers',
    auteur: 'J.K. Rowling',
    categorie: 'Jeunesse',
    genre: 'Fantasy',
    isbn: '9782070541270',
    anneePublication: 1997,
    description: 'Premier tome de la saga Harry Potter',
    disponible: true,
    quantiteTotale: 5,
    quantiteDisponible: 5
  },
  {
    titre: 'La Joconde',
    auteur: 'Leonardo da Vinci',
    categorie: 'Art',
    genre: 'Biographie',
    anneePublication: 1503,
    description: 'L\'histoire du chef-d\'œuvre de Léonard de Vinci',
    disponible: true,
    quantiteTotale: 1,
    quantiteDisponible: 1
  },
  {
    titre: '1984',
    auteur: 'George Orwell',
    categorie: 'Fiction',
    genre: 'Dystopie',
    isbn: '9782070368228',
    anneePublication: 1949,
    description: 'Roman dystopique sur la surveillance totalitaire',
    disponible: true,
    quantiteTotale: 3,
    quantiteDisponible: 3
  }
];

// Données initiales des membres - Updated to match available images in public/usersimages
const INITIAL_MEMBERS: Omit<LibraryMember, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    nom: 'Ushindi',
    prenom: 'Gabriel',
    fonction: 'Ingenieur et Chercheur dans Les Nouvelles Technologies',
    numeroMembre: 'BT20250001',
    telephone: '+243 123 456 789',
    adresse: '123 Avenue de la Paix, Kinshasa, RDC',
    email: '<EMAIL>',
    photo: '/usersimages/gabriel.jpg',
    dateInscription: '2024-01-15',
    isActive: true
  },
  {
    nom: 'Nyota',
    prenom: 'Grace',
    fonction: 'Professeure de Littérature',
    numeroMembre: 'BT20250002',
    telephone: '+243 987 654 321',
    adresse: '456 Boulevard de la Liberté, Lubumbashi, RDC',
    email: '<EMAIL>',
    photo: '/usersimages/grace_nyota.jpg',
    dateInscription: '2024-02-10',
    isActive: true
  },
  {
    nom: 'Abusa',
    prenom: 'Jocy',
    fonction: 'Médecin Généraliste',
    numeroMembre: 'BT20250003',
    telephone: '+243 555 123 456',
    adresse: '789 Rue de la Santé, Goma, RDC',
    email: '<EMAIL>',
    photo: '/usersimages/jocyabusa.jpg',
    dateInscription: '2024-03-05',
    isActive: true
  },
  {
    nom: 'Mukendi',
    prenom: 'Joseph',
    fonction: 'Architecte',
    numeroMembre: 'BT20250004',
    telephone: '+243 777 888 999',
    adresse: '321 Avenue de l\'Architecture, Bukavu, RDC',
    email: '<EMAIL>',
    photo: '/usersimages/joseph.jpg',
    dateInscription: '2024-04-12',
    isActive: true
  },
  {
    nom: 'Tshisekedi',
    prenom: 'Aaron',
    fonction: 'Avocat',
    numeroMembre: 'BT20250005',
    telephone: '+243 444 555 666',
    adresse: '654 Rue de la Justice, Mbuji-Mayi, RDC',
    email: '<EMAIL>',
    photo: '/usersimages/aaron.png',
    dateInscription: '2024-05-20',
    isActive: true
  }
];

// Fonction utilitaire pour hacher un mot de passe
function hashPassword(password: string): string {
  const crypto = require('crypto');
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
}

// Données initiales des utilisateurs système
const INITIAL_USERS = [
  {
    username: 'superadmin',
    email: '<EMAIL>',
    passwordHash: hashPassword('admin123'), // Properly hashed password
    role: 'super_admin' as const
  },
  {
    username: 'admin1',
    email: '<EMAIL>',
    passwordHash: hashPassword('admin123'), // Properly hashed password
    role: 'administrator' as const
  }
];

/**
 * Vérifie si la base de données est vide
 */
function isDatabaseEmpty(): boolean {
  const db = getDatabase();
  const stats = db.getStats();
  return stats.totalBooks === 0 && stats.totalMembers === 0;
}

/**
 * Migre les données initiales vers la base de données
 */
export async function migrateInitialData(): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    // Vérifier si la migration est nécessaire
    if (!isDatabaseEmpty()) {
      return {
        success: true,
        message: 'La base de données contient déjà des données. Migration ignorée.'
      };
    }

    const db = getDatabase();
    
    console.log('🚀 Début de la migration des données...');

    // 1. Migrer les catégories
    console.log('📚 Migration des catégories...');
    const categories = [];
    for (const categoryData of INITIAL_CATEGORIES) {
      const category = db.addCategory(categoryData.name, categoryData.description);
      categories.push(category);
    }
    console.log(`✅ ${categories.length} catégories migrées`);

    // 2. Migrer les membres
    console.log('👥 Migration des membres...');
    const members = [];
    for (const memberData of INITIAL_MEMBERS) {
      const member = db.addMember(memberData);
      members.push(member);
    }
    console.log(`✅ ${members.length} membres migrés`);

    // 3. Migrer les livres
    console.log('📖 Migration des livres...');
    const books = [];
    for (const bookData of INITIAL_BOOKS) {
      const { emprunteurId, dateEmprunt, dateRetour, ...bookWithoutLoan } = bookData;
      const book = db.addBook(bookWithoutLoan);
      books.push(book);

      // Créer l'emprunt si le livre n'est pas disponible
      if (!bookData.disponible && emprunteurId && dateEmprunt && dateRetour) {
        db.createLoan(book.id, emprunteurId, dateEmprunt, dateRetour);
      }
    }
    console.log(`✅ ${books.length} livres migrés`);

    // 4. Migrer les utilisateurs système
    console.log('🔐 Migration des utilisateurs système...');
    const users = [];
    for (const userData of INITIAL_USERS) {
      const user = db.addUser(userData.username, userData.email, userData.passwordHash, userData.role);
      users.push(user);
    }
    console.log(`✅ ${users.length} utilisateurs système migrés`);

    // 5. Mettre à jour les statuts des emprunts en retard
    db.updateOverdueLoans();

    console.log('🎉 Migration terminée avec succès !');

    return {
      success: true,
      message: 'Migration des données initiales terminée avec succès',
      details: {
        categories: categories.length,
        members: members.length,
        books: books.length,
        users: users.length
      }
    };

  } catch (error) {
    console.error('❌ Erreur lors de la migration:', error);
    return {
      success: false,
      message: `Erreur lors de la migration: ${error instanceof Error ? error.message : 'Erreur inconnue'}`,
      details: error
    };
  }
}

/**
 * Réinitialise la base de données (supprime toutes les données)
 */
export async function resetDatabase(): Promise<boolean> {
  try {
    const db = getDatabase();
    
    // Supprimer toutes les données dans l'ordre inverse des dépendances
    db.db.exec('DELETE FROM journaux_audit');
    db.db.exec('DELETE FROM emprunts');
    db.db.exec('DELETE FROM livres');
    db.db.exec('DELETE FROM membres');
    db.db.exec('DELETE FROM utilisateurs');
    db.db.exec('DELETE FROM categories');
    
    // Réinitialiser les compteurs d'auto-incrémentation
    db.db.exec('DELETE FROM sqlite_sequence');
    
    console.log('🗑️ Base de données réinitialisée');
    return true;
  } catch (error) {
    console.error('❌ Erreur lors de la réinitialisation:', error);
    return false;
  }
}

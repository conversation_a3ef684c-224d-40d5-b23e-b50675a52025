// Preload script pour BiblioTech
// Provides secure IPC bridge between main and renderer processes

const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose database API to renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  database: {
    // Books
    getAllBooks: () => ipcRenderer.invoke('db:getAllBooks'),
    getBookById: (id) => ipcRenderer.invoke('db:getBookById', id),
    addBook: (book) => ipcRenderer.invoke('db:addBook', book),
    updateBook: (id, updates) => ipcRenderer.invoke('db:updateBook', id, updates),
    deleteBook: (id) => ipcRenderer.invoke('db:deleteBook', id),

    // Categories
    getAllCategories: () => ipcRenderer.invoke('db:getAllCategories'),
    getCategoryById: (id) => ipcRenderer.invoke('db:getCategoryById', id),
    addCategory: (name, description) => ipcRenderer.invoke('db:addCategory', name, description),
    updateCategory: (id, name, description) => ipcRenderer.invoke('db:updateCategory', id, name, description),
    deleteCategory: (id) => ipcRenderer.invoke('db:deleteCategory', id),
    reorderCategories: (categoryIds) => ipcRenderer.invoke('db:reorderCategories', categoryIds),
    getBooksCountInCategory: (categoryName) => ipcRenderer.invoke('db:getBooksCountInCategory', categoryName),

    // Members
    getAllMembers: () => ipcRenderer.invoke('db:getAllMembers'),
    getMemberById: (id) => ipcRenderer.invoke('db:getMemberById', id),
    addMember: (member) => ipcRenderer.invoke('db:addMember', member),
    updateMember: (id, updates) => ipcRenderer.invoke('db:updateMember', id, updates),
    deleteMember: (id) => ipcRenderer.invoke('db:deleteMember', id),
    getAllMemberProfiles: () => ipcRenderer.invoke('db:getAllMemberProfiles'),
    getMemberProfile: (id) => ipcRenderer.invoke('db:getMemberProfile', id),

    // Loans
    createLoan: (bookId, memberId, startDate, dueDate) => ipcRenderer.invoke('db:createLoan', bookId, memberId, startDate, dueDate),
    returnBook: (bookId) => ipcRenderer.invoke('db:returnBook', bookId),
    extendLoan: (bookId, newDueDate) => ipcRenderer.invoke('db:extendLoan', bookId, newDueDate),
    getMemberBorrowedBooks: (memberId) => ipcRenderer.invoke('db:getMemberBorrowedBooks', memberId),
    updateOverdueLoans: () => ipcRenderer.invoke('db:updateOverdueLoans'),

    // Users
    getAllUsers: () => ipcRenderer.invoke('db:getAllUsers'),
    getUserByUsername: (username) => ipcRenderer.invoke('db:getUserByUsername', username),
    addUser: (username, email, passwordHash, role) => ipcRenderer.invoke('db:addUser', username, email, passwordHash, role),
    updateLastLogin: (userId) => ipcRenderer.invoke('db:updateLastLogin', userId),

    // Authentication
    authenticateUser: (credentials) => ipcRenderer.invoke('db:authenticateUser', credentials),
    createUser: (userData) => ipcRenderer.invoke('db:createUser', userData),

    // Audit logs
    addAuditLog: (userId, username, action, resource, resourceId, details) => ipcRenderer.invoke('db:addAuditLog', userId, username, action, resource, resourceId, details),
    getAuditLogs: (limit, offset) => ipcRenderer.invoke('db:getAuditLogs', limit, offset),
    getUserAuditLogs: (userId, limit) => ipcRenderer.invoke('db:getUserAuditLogs', userId, limit),

    // Statistics and utilities
    getStats: () => ipcRenderer.invoke('db:getStats'),
    getDatabasePath: () => ipcRenderer.invoke('db:getDatabasePath'),
    backup: (backupPath) => ipcRenderer.invoke('db:backup', backupPath),
    
    // Migration
    migrateInitialData: () => ipcRenderer.invoke('db:migrateInitialData')
  },

  // App utilities
  app: {
    getVersion: () => ipcRenderer.invoke('app:getVersion'),
    quit: () => ipcRenderer.invoke('app:quit'),
    minimize: () => ipcRenderer.invoke('app:minimize'),
    maximize: () => ipcRenderer.invoke('app:maximize'),
    unmaximize: () => ipcRenderer.invoke('app:unmaximize'),
    isMaximized: () => ipcRenderer.invoke('app:isMaximized')
  },

  // File operations
  file: {
    showOpenDialog: (options) => ipcRenderer.invoke('file:showOpenDialog', options),
    showSaveDialog: (options) => ipcRenderer.invoke('file:showSaveDialog', options),
    readFile: (filePath) => ipcRenderer.invoke('file:readFile', filePath),
    writeFile: (filePath, data) => ipcRenderer.invoke('file:writeFile', filePath, data)
  },

  // Image operations
  getUserImagePath: (imagePath) => ipcRenderer.invoke('get-user-image-path', imagePath),
  saveUserImage: (imageData, filename) => ipcRenderer.invoke('save-user-image', imageData, filename),

  // Database reset operations
  resetToInitialData: () => ipcRenderer.invoke('db:resetToInitialData')
});

// Log that preload script loaded successfully
console.log('🔗 Preload script loaded successfully - IPC bridge ready');

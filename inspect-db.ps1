# Script PowerShell pour inspecter la base de données BiblioTech
# PowerShell script to inspect BiblioTech database

param(
    [switch]$ShowTables,
    [switch]$ShowStats,
    [switch]$ShowAll
)

# Chemin vers la base de données
$dbPath = "$env:APPDATA\bibliotech\bibliotech.db"

Write-Host "🔍 Inspection de la base de données BiblioTech" -ForegroundColor Cyan
Write-Host "📁 Chemin: $dbPath" -ForegroundColor Yellow
Write-Host ("=" * 60) -ForegroundColor Gray

# Vérifier si le fichier existe
if (-not (Test-Path $dbPath)) {
    Write-Host "❌ Erreur: Le fichier de base de données n'existe pas!" -ForegroundColor Red
    Write-Host "💡 Assurez-vous que BiblioTech a été lancé au moins une fois." -ForegroundColor Yellow
    exit 1
}

# Afficher les informations du fichier
$fileInfo = Get-Item $dbPath
Write-Host "📊 Taille du fichier: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green
Write-Host "📅 Dernière modification: $($fileInfo.LastWriteTime)" -ForegroundColor Green
Write-Host ""

# Fonction pour exécuter des requêtes SQLite (simulation)
function Show-DatabaseInfo {
    Write-Host "🗂️  STRUCTURE DE LA BASE DE DONNÉES" -ForegroundColor Cyan
    Write-Host ("=" * 40) -ForegroundColor Gray
    
    $tables = @(
        @{Name="livres"; Description="Catalogue des livres"; Columns="id, titre, auteur, categorie, genre, isbn, disponible"},
        @{Name="categories"; Description="Catégories de livres"; Columns="id, nom, description, ordre"},
        @{Name="membres"; Description="Membres de la bibliothèque"; Columns="id, nom, prenom, fonction, numero_membre, telephone"},
        @{Name="emprunts"; Description="Gestion des emprunts"; Columns="id, livre_id, membre_id, date_emprunt, statut"},
        @{Name="utilisateurs"; Description="Comptes administrateurs"; Columns="id, username, email, role, is_active"},
        @{Name="journaux_audit"; Description="Journaux d'audit"; Columns="id, user_id, action, resource, timestamp"}
    )
    
    foreach ($table in $tables) {
        Write-Host "📋 Table: $($table.Name)" -ForegroundColor Yellow
        Write-Host "   Description: $($table.Description)" -ForegroundColor White
        Write-Host "   Colonnes: $($table.Columns)" -ForegroundColor Gray
        Write-Host ""
    }
}

function Show-QuickStats {
    Write-Host "📊 STATISTIQUES RAPIDES" -ForegroundColor Cyan
    Write-Host ("=" * 30) -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "💡 Pour obtenir les statistiques réelles, utilisez:" -ForegroundColor Yellow
    Write-Host "   1. DB Browser for SQLite (recommandé)" -ForegroundColor White
    Write-Host "   2. Ouvrez le fichier: $dbPath" -ForegroundColor Gray
    Write-Host "   3. Exécutez ces requêtes:" -ForegroundColor White
    Write-Host ""
    
    $queries = @(
        "SELECT COUNT(*) as total_livres FROM livres;",
        "SELECT COUNT(*) as livres_disponibles FROM livres WHERE disponible = 1;",
        "SELECT COUNT(*) as membres_actifs FROM membres WHERE is_active = 1;",
        "SELECT COUNT(*) as emprunts_actifs FROM emprunts WHERE statut = 'actif';"
    )
    
    foreach ($query in $queries) {
        Write-Host "   $query" -ForegroundColor Cyan
    }
}

function Show-UsefulQueries {
    Write-Host "🔍 REQUÊTES SQL UTILES" -ForegroundColor Cyan
    Write-Host ("=" * 25) -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "📚 Livres par catégorie:" -ForegroundColor Yellow
    Write-Host "SELECT c.nom, COUNT(l.id) as nb_livres" -ForegroundColor Cyan
    Write-Host "FROM categories c LEFT JOIN livres l ON c.nom = l.categorie" -ForegroundColor Cyan
    Write-Host "GROUP BY c.nom ORDER BY nb_livres DESC;" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "📋 Emprunts en cours:" -ForegroundColor Yellow
    Write-Host "SELECT l.titre, m.prenom + ' ' + m.nom as emprunteur," -ForegroundColor Cyan
    Write-Host "       e.date_emprunt, e.date_retour_prevue" -ForegroundColor Cyan
    Write-Host "FROM emprunts e" -ForegroundColor Cyan
    Write-Host "JOIN livres l ON e.livre_id = l.id" -ForegroundColor Cyan
    Write-Host "JOIN membres m ON e.membre_id = m.id" -ForegroundColor Cyan
    Write-Host "WHERE e.statut = 'actif';" -ForegroundColor Cyan
    Write-Host ""

    Write-Host "📊 Statistiques générales:" -ForegroundColor Yellow
    Write-Host "SELECT" -ForegroundColor Cyan
    Write-Host "  (SELECT COUNT(*) FROM livres) as total_livres," -ForegroundColor Cyan
    Write-Host "  (SELECT COUNT(*) FROM membres) as total_membres," -ForegroundColor Cyan
    Write-Host "  (SELECT COUNT(*) FROM emprunts WHERE statut='actif') as emprunts_actifs;" -ForegroundColor Cyan
}

function Show-Tools {
    Write-Host "🛠️  OUTILS RECOMMANDÉS" -ForegroundColor Cyan
    Write-Host ("=" * 25) -ForegroundColor Gray
    Write-Host ""
    
    Write-Host "1. 🎯 DB Browser for SQLite (Gratuit)" -ForegroundColor Yellow
    Write-Host "   Téléchargement: https://sqlitebrowser.org/dl/" -ForegroundColor White
    Write-Host "   ✅ Interface graphique intuitive" -ForegroundColor Green
    Write-Host "   ✅ Visualisation des données" -ForegroundColor Green
    Write-Host "   ✅ Exécution de requêtes SQL" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "2. 🌐 SQLite Viewer Online" -ForegroundColor Yellow
    Write-Host "   Site: https://sqliteviewer.app/" -ForegroundColor White
    Write-Host "   ⚠️  Attention: Ne pas télécharger de données sensibles!" -ForegroundColor Red
    Write-Host ""
    
    Write-Host "3. 💻 Ligne de commande SQLite" -ForegroundColor Yellow
    Write-Host "   Commande: sqlite3 `"$dbPath`"" -ForegroundColor White
    Write-Host "   (Nécessite l'installation de SQLite CLI)" -ForegroundColor Gray
}

# Exécution selon les paramètres
if ($ShowAll -or (-not $ShowTables -and -not $ShowStats)) {
    Show-DatabaseInfo
    Write-Host ""
    Show-QuickStats
    Write-Host ""
    Show-UsefulQueries
    Write-Host ""
    Show-Tools
} elseif ($ShowTables) {
    Show-DatabaseInfo
} elseif ($ShowStats) {
    Show-QuickStats
}

Write-Host ""
Write-Host "🎯 PROCHAINES ÉTAPES:" -ForegroundColor Cyan
Write-Host "1. Téléchargez DB Browser for SQLite" -ForegroundColor White
Write-Host "2. Ouvrez le fichier: $dbPath" -ForegroundColor White
Write-Host "3. Explorez vos données en toute sécurité!" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  IMPORTANT: Fermez BiblioTech avant d'ouvrir la base avec un autre outil!" -ForegroundColor Red

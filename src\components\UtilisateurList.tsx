// Composant pour afficher les profils d'utilisateurs (emprunteurs)
import React, { useState } from 'react';
import { UsersIcon, SearchIcon, BookIcon, PhoneIcon, IdCardIcon } from './Icons';
import UserProfile from './UserProfile';
import ProfileImage from './ProfileImage';
import type { MemberProfile } from '../types/member';

export type Utilisateur = {
  id: number;
  nom: string;
  photo: string; // URL de la photo de profil
};

interface Props {
  utilisateurs: Utilisateur[];
  memberProfiles: MemberProfile[];
  onSelect?: (utilisateur: Utilisateur) => void;
}

const UtilisateurList: React.FC<Props> = ({
  utilisateurs,
  memberProfiles,
  onSelect
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProfile, setSelectedProfile] = useState<MemberProfile | null>(null);

  const filteredUsers = utilisateurs.filter(user =>
    user.nom.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUserClick = (user: Utilisateur) => {
    // Find the corresponding member profile
    const profile = memberProfiles.find(p => p.id === user.id);
    if (profile) {
      setSelectedProfile(profile);
    }
    onSelect?.(user);
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="card-title flex items-center gap-2">
              <UsersIcon size={24} />
              Profils d'utilisateurs
            </h2>
            <p className="card-subtitle">{filteredUsers.length} utilisateur(s) enregistré(s)</p>
          </div>
          {/* User creation is handled automatically when adding members */}
        </div>
      </div>

      <div className="card-content">
        {/* Search Bar */}
        <div className="search-bar">
          <SearchIcon className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Rechercher un utilisateur..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* User Grid */}
        <div className="user-grid">
          {filteredUsers.map(user => {
            const profile = memberProfiles.find(p => p.id === user.id);
            return (
              <div
                key={user.id}
                className="user-card enhanced-user-card"
                onClick={() => handleUserClick(user)}
              >
                <ProfileImage
                  src={user.photo}
                  alt={user.nom}
                  className="user-avatar"
                  fallbackName={user.nom}
                  size={60}
                />
                <div className="user-name">{user.nom}</div>
                <div className="user-info">ID: {user.id}</div>

                {profile && (
                  <div className="user-stats">
                    <div className="stat-item">
                      <IdCardIcon size={14} />
                      <span>{profile.numeroMembre}</span>
                    </div>
                    <div className="stat-item">
                      <PhoneIcon size={14} />
                      <span>{profile.telephone}</span>
                    </div>
                    <div className="stat-item">
                      <BookIcon size={14} />
                      <span>{profile.nombreLivresEmpruntes} livre(s)</span>
                    </div>
                    {profile.nombreLivresEnRetard > 0 && (
                      <div className="stat-item text-error">
                        <span className="badge badge-error">
                          {profile.nombreLivresEnRetard} en retard
                        </span>
                      </div>
                    )}
                  </div>
                )}

                <div className="user-action-hint">
                  Cliquer pour voir le profil détaillé
                </div>
              </div>
            );
          })}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-neutral">
            <UsersIcon size={48} className="mx-auto mb-4 opacity-50" />
            <p>Aucun utilisateur trouvé.</p>
          </div>
        )}
      </div>

      {/* User Profile Modal */}
      {selectedProfile && (
        <UserProfile
          member={selectedProfile}
          onClose={() => setSelectedProfile(null)}
        />
      )}
    </div>
  );
};

export default UtilisateurList;

# 📚 BiblioTech - Guide Complet de la Base de Données
## Documentation pour Utilisateurs Non Techniques

---

## 🎯 Table des Matières

1. [Qu'est-ce qu'une Base de Données ?](#quest-ce-quune-base-de-données-)
2. [Comment BiblioTech Stocke vos Données](#comment-bibliotech-stocke-vos-données)
3. [Structure de la Base de Données](#structure-de-la-base-de-données)
4. [Accéder à vos Données](#accéder-à-vos-données)
5. [Requêtes SQL Essentielles](#requêtes-sql-essentielles)
6. [Requêtes Avancées](#requêtes-avancées)
7. [Maintenance et Sécurité](#maintenance-et-sécurité)
8. [Résolution de Problèmes](#résolution-de-problèmes)

---

## 🤔 Qu'est-ce qu'une Base de Données ?

### Analogie Simple
Imaginez votre bibliothèque physique avec :
- **Des étagères** pour organiser les livres par catégories
- **Un registre** pour noter qui emprunte quoi
- **Des fiches** pour chaque membre de la bibliothèque
- **Un carnet** pour noter toutes les activités

Une **base de données** est exactement la même chose, mais en version électronique ! C'est un système ultra-organisé qui stocke et organise toutes vos informations de manière intelligente.

### Pourquoi une Base de Données ?

#### ✅ **Avantages :**
- **Organisation parfaite** : Chaque information a sa place
- **Recherche instantanée** : Trouvez n'importe quoi en quelques secondes
- **Sécurité** : Vos données sont protégées et sauvegardées
- **Fiabilité** : Aucune perte d'information
- **Rapidité** : Accès immédiat à des milliers d'enregistrements

#### ❌ **Sans base de données, vous auriez :**
- Des fichiers Excel éparpillés
- Des risques de perte de données
- Des doublons et des erreurs
- Des recherches longues et fastidieuses
- Aucun historique des actions

---

## 💾 Comment BiblioTech Stocke vos Données

### 📍 Emplacement Physique
Vos données sont stockées dans un fichier spécial sur votre ordinateur :

**📁 Chemin complet :**
```
C:\Users\<USER>\AppData\Roaming\bibliotech\bibliotech.db
```

**🔍 Comment y accéder rapidement :**
1. Appuyez sur `Windows + R`
2. Tapez : `%APPDATA%\bibliotech`
3. Appuyez sur `Entrée`
4. Vous verrez le fichier `bibliotech.db`

### 🛡️ Sécurité et Confidentialité

#### ✅ **Ce qui est sécurisé :**
- **Stockage local** : Vos données restent sur VOTRE ordinateur
- **Aucun envoi sur internet** : Rien n'est transmis en ligne
- **Accès protégé** : Seuls les administrateurs autorisés peuvent accéder
- **Sauvegarde automatique** : Chaque action est immédiatement enregistrée

#### 🔒 **Protection des mots de passe :**
- Les mots de passe sont **chiffrés** (cryptés)
- Impossible de les lire même en ouvrant la base de données
- Seul le système peut les vérifier

### ⚡ Fonctionnement en Temps Réel

Chaque fois que vous :
- ✏️ **Ajoutez un livre** → Sauvegardé instantanément
- 👤 **Créez un membre** → Enregistré immédiatement
- 📋 **Faites un emprunt** → Tracé en temps réel
- 🔄 **Modifiez une information** → Mis à jour automatiquement

**💡 Résultat :** Vous n'avez JAMAIS besoin de cliquer sur "Sauvegarder" !

---

## 🏗️ Structure de la Base de Données

La base de données BiblioTech est organisée en **6 sections principales** (appelées "tables") :

### 📚 1. Table LIVRES
**Rôle :** Catalogue complet de tous vos ouvrages

**Informations stockées :**
- 🆔 **Identifiant unique** (numéro automatique)
- 📖 **Titre** du livre
- ✍️ **Auteur** principal
- 🏷️ **Catégorie** (Fiction, Science, etc.)
- 🎭 **Genre** littéraire (Roman, Essai, etc.)
- 📊 **ISBN** (code international du livre)
- 📅 **Année de publication**
- 📝 **Description** ou résumé
- ✅ **Disponibilité** (Disponible/Emprunté)
- 📅 **Dates** de création et modification

**Exemple d'enregistrement :**
```
ID: 1
Titre: "Le Petit Prince"
Auteur: "Antoine de Saint-Exupéry"
Catégorie: "Fiction"
Genre: "Roman"
ISBN: "978-2-07-040850-1"
Année: 1943
Disponible: Oui
```

### 🏷️ 2. Table CATÉGORIES
**Rôle :** Classification et organisation des livres

**Informations stockées :**
- 🆔 **Identifiant unique**
- 📛 **Nom** de la catégorie (unique)
- 📝 **Description** détaillée
- 🔢 **Ordre** d'affichage
- 📅 **Dates** de création et modification

**Exemples de catégories :**
```
1. Fiction - Romans et nouvelles
2. Science - Ouvrages scientifiques
3. Histoire - Livres d'histoire
4. Jeunesse - Littérature pour enfants
```

### 👥 3. Table MEMBRES
**Rôle :** Base de données des emprunteurs

**Informations stockées :**
- 🆔 **Identifiant unique**
- 👤 **Nom** et **Prénom**
- 💼 **Fonction** ou profession
- 🎫 **Numéro de membre** (unique)
- 📞 **Téléphone**
- 🏠 **Adresse** complète
- 📧 **Email** (optionnel)
- 📸 **Photo** de profil
- 📅 **Date d'inscription**
- ✅ **Statut** (Actif/Inactif)

**Exemple d'enregistrement :**
```
ID: 1
Nom: "Dupont"
Prénom: "Marie"
Fonction: "Enseignante"
Numéro: "BIB-2024-001"
Téléphone: "01 23 45 67 89"
Email: "<EMAIL>"
Statut: Actif
```

### 📋 4. Table EMPRUNTS
**Rôle :** Suivi complet des prêts de livres

**Informations stockées :**
- 🆔 **Identifiant unique** de l'emprunt
- 📚 **ID du livre** emprunté
- 👤 **ID du membre** emprunteur
- 📅 **Date d'emprunt**
- 📅 **Date de retour prévue**
- 📅 **Date de retour effective** (si rendu)
- 🚦 **Statut** (Actif, Retourné, En retard)
- 📅 **Date de création** de l'enregistrement

**Statuts possibles :**
- 🟢 **Actif** : Livre actuellement emprunté
- ✅ **Retourné** : Livre rendu à temps
- 🔴 **En retard** : Dépassement de la date prévue

**Exemple d'emprunt :**
```
ID: 1
Livre: "Le Petit Prince" (ID: 1)
Membre: "Marie Dupont" (ID: 1)
Date emprunt: 15/12/2024
Date retour prévue: 29/12/2024
Statut: Actif
```

### 👤 5. Table UTILISATEURS
**Rôle :** Comptes administrateurs du système

**Informations stockées :**
- 🆔 **Identifiant unique**
- 👤 **Nom d'utilisateur** (unique)
- 📧 **Email** de contact
- 🔐 **Mot de passe chiffré**
- 🛡️ **Rôle** (Super Admin / Administrateur)
- ✅ **Statut** (Actif/Inactif)
- 📅 **Dernière connexion**
- 📅 **Dates** de création et modification

**Types de rôles :**
- 🔴 **Super Admin** : Accès complet (gestion utilisateurs, etc.)
- 🟡 **Administrateur** : Gestion quotidienne (livres, membres, emprunts)

### 📊 6. Table JOURNAUX_AUDIT
**Rôle :** Historique complet de toutes les actions

**Informations stockées :**
- 🆔 **Identifiant unique**
- 👤 **ID utilisateur** qui a fait l'action
- 👤 **Nom d'utilisateur**
- ⚡ **Type d'action** (CREATE, UPDATE, DELETE, etc.)
- 📦 **Type de ressource** (BOOK, MEMBER, CATEGORY, etc.)
- 🆔 **ID de la ressource** concernée
- 📝 **Détails** de l'action
- ⏰ **Horodatage** précis

**Exemples d'actions tracées :**
```
Action: CREATE
Ressource: BOOK
Utilisateur: admin1
Détails: "Livre créé: Le Petit Prince"
Date: 15/12/2024 14:30:25
```

---

## 🔧 Accéder à vos Données

### 🎯 Méthode 1 : DB Browser for SQLite (RECOMMANDÉE)

#### **Étape 1 : Téléchargement**
1. Allez sur : https://sqlitebrowser.org/dl/
2. Cliquez sur **"DB.Browser.for.SQLite-v3.13.1-win32.msi"**
3. Téléchargez le fichier (environ 20 MB)

#### **Étape 2 : Installation**
1. Double-cliquez sur le fichier téléchargé
2. Suivez l'assistant d'installation
3. Acceptez les paramètres par défaut
4. Cliquez sur **"Installer"**

#### **Étape 3 : Ouverture de votre base**
1. **IMPORTANT** : Fermez complètement BiblioTech
2. Lancez **"DB Browser for SQLite"**
3. Cliquez sur **"Open Database"** (Ouvrir base de données)
4. Naviguez vers : `%APPDATA%\bibliotech\bibliotech.db`
5. Sélectionnez le fichier et cliquez **"Ouvrir"**

#### **Étape 4 : Explorer vos données**
1. **Onglet "Database Structure"** : Voir la structure des tables
2. **Onglet "Browse Data"** : Consulter le contenu des tables
3. **Onglet "Execute SQL"** : Exécuter des requêtes personnalisées

### 🌐 Méthode 2 : Visualiseur en Ligne (Pour Petites Bases)

**Site :** https://sqliteviewer.app/

**⚠️ ATTENTION SÉCURITÉ :**
- Ne jamais télécharger de données sensibles sur des sites tiers
- Utilisez uniquement pour des tests ou des copies anonymisées
- Préférez toujours la méthode locale (DB Browser)

### 💻 Méthode 3 : Ligne de Commande SQLite

**Pour les utilisateurs avancés :**
1. Téléchargez SQLite CLI : https://sqlite.org/download.html
2. Ouvrez l'invite de commande
3. Tapez : `sqlite3 "%APPDATA%\bibliotech\bibliotech.db"`
4. Utilisez les commandes SQL directement

---

## 📊 Requêtes SQL Essentielles

### 🎓 Introduction au SQL

**SQL** (Structured Query Language) est le langage universel pour interroger les bases de données. C'est comme poser des questions à votre base de données en anglais simplifié.

**Structure de base :**
```sql
SELECT [ce_que_vous_voulez_voir]
FROM [dans_quelle_table]
WHERE [avec_quelles_conditions]
```

### 📈 1. Statistiques Générales

#### **Nombre total de livres**
```sql
-- English: Count all books
-- Français: Compter tous les livres
SELECT COUNT(*) as total_livres FROM livres;
```
**Résultat attendu :** Un nombre (ex: 150 livres)

#### **Livres disponibles vs empruntés**
```sql
-- English: Count available and borrowed books
-- Français: Compter les livres disponibles et empruntés
SELECT
    SUM(CASE WHEN disponible = 1 THEN 1 ELSE 0 END) as livres_disponibles,
    SUM(CASE WHEN disponible = 0 THEN 1 ELSE 0 END) as livres_empruntes
FROM livres;
```
**Résultat attendu :** Deux colonnes avec les nombres respectifs

#### **Nombre de membres actifs**
```sql
-- English: Count active members
-- Français: Compter les membres actifs
SELECT COUNT(*) as membres_actifs
FROM membres
WHERE is_active = 1;
```

#### **Emprunts en cours**
```sql
-- English: Count active loans
-- Français: Compter les emprunts actifs
SELECT COUNT(*) as emprunts_actifs
FROM emprunts
WHERE statut = 'actif';
```

### 📚 2. Recherches sur les Livres

#### **Tous les livres d'un auteur**
```sql
-- English: Find all books by a specific author
-- Français: Trouver tous les livres d'un auteur spécifique
SELECT titre, categorie, annee_publication, disponible
FROM livres
WHERE auteur LIKE '%Saint-Exupéry%';
```
**💡 Astuce :** Remplacez "Saint-Exupéry" par l'auteur recherché

#### **Livres d'une catégorie spécifique**
```sql
-- English: Find all books in a specific category
-- Français: Trouver tous les livres d'une catégorie
SELECT titre, auteur, genre, disponible
FROM livres
WHERE categorie = 'Fiction'
ORDER BY titre;
```

#### **Livres publiés après une certaine année**
```sql
-- English: Find books published after a specific year
-- Français: Trouver les livres publiés après une année donnée
SELECT titre, auteur, annee_publication
FROM livres
WHERE annee_publication > 2000
ORDER BY annee_publication DESC;
```

#### **Recherche par titre (partielle)**
```sql
-- English: Search books by partial title
-- Français: Rechercher des livres par titre partiel
SELECT titre, auteur, categorie
FROM livres
WHERE titre LIKE '%Prince%';
```

### 👥 3. Gestion des Membres

#### **Liste complète des membres actifs**
```sql
-- English: List all active members
-- Français: Lister tous les membres actifs
SELECT nom, prenom, fonction, numero_membre, telephone
FROM membres
WHERE is_active = 1
ORDER BY nom, prenom;
```

#### **Membres par fonction**
```sql
-- English: Group members by their function/profession
-- Français: Grouper les membres par fonction/profession
SELECT fonction, COUNT(*) as nombre_membres
FROM membres
WHERE is_active = 1
GROUP BY fonction
ORDER BY nombre_membres DESC;
```

#### **Rechercher un membre**
```sql
-- English: Search for a specific member
-- Français: Rechercher un membre spécifique
SELECT nom, prenom, numero_membre, telephone, email
FROM membres
WHERE nom LIKE '%Dupont%' OR prenom LIKE '%Marie%';
```

### 📋 4. Suivi des Emprunts

#### **Emprunts actuellement actifs**
```sql
-- English: Show all active loans with details
-- Français: Afficher tous les emprunts actifs avec détails
SELECT
    l.titre as livre,
    m.prenom || ' ' || m.nom as emprunteur,
    e.date_emprunt,
    e.date_retour_prevue,
    CASE
        WHEN date('now') > e.date_retour_prevue THEN 'EN RETARD'
        ELSE 'DANS LES TEMPS'
    END as statut_retard
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif'
ORDER BY e.date_retour_prevue;
```

#### **Livres en retard**
```sql
-- English: Find overdue books
-- Français: Trouver les livres en retard
SELECT
    l.titre,
    m.prenom || ' ' || m.nom as emprunteur,
    m.telephone,
    e.date_retour_prevue,
    (julianday('now') - julianday(e.date_retour_prevue)) as jours_retard
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif'
AND date('now') > e.date_retour_prevue
ORDER BY jours_retard DESC;
```

#### **Historique des emprunts d'un membre**
```sql
-- English: Show loan history for a specific member
-- Français: Afficher l'historique des emprunts d'un membre
SELECT
    l.titre,
    e.date_emprunt,
    e.date_retour_prevue,
    e.date_retour_effective,
    e.statut
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE m.nom = 'Dupont' AND m.prenom = 'Marie'
ORDER BY e.date_emprunt DESC;
```

### 📊 5. Statistiques Avancées

#### **Livres les plus empruntés**
```sql
-- English: Find most borrowed books
-- Français: Trouver les livres les plus empruntés
SELECT
    l.titre,
    l.auteur,
    COUNT(e.id) as nombre_emprunts
FROM livres l
LEFT JOIN emprunts e ON l.id = e.livre_id
GROUP BY l.id, l.titre, l.auteur
HAVING COUNT(e.id) > 0
ORDER BY nombre_emprunts DESC
LIMIT 10;
```

#### **Membres les plus actifs**
```sql
-- English: Find most active members
-- Français: Trouver les membres les plus actifs
SELECT
    m.prenom || ' ' || m.nom as membre,
    m.fonction,
    COUNT(e.id) as nombre_emprunts
FROM membres m
LEFT JOIN emprunts e ON m.id = e.membre_id
GROUP BY m.id, m.nom, m.prenom, m.fonction
HAVING COUNT(e.id) > 0
ORDER BY nombre_emprunts DESC
LIMIT 10;
```

#### **Répartition des livres par catégorie**
```sql
-- English: Distribution of books by category
-- Français: Répartition des livres par catégorie
SELECT
    c.nom as categorie,
    c.description,
    COUNT(l.id) as nombre_livres,
    ROUND(COUNT(l.id) * 100.0 / (SELECT COUNT(*) FROM livres), 2) as pourcentage
FROM categories c
LEFT JOIN livres l ON c.nom = l.categorie
GROUP BY c.id, c.nom, c.description
ORDER BY nombre_livres DESC;
```

---

## 🔍 Requêtes Avancées

### 📈 6. Analyses Temporelles

#### **Emprunts par mois**
```sql
-- English: Loans per month
-- Français: Emprunts par mois
SELECT
    strftime('%Y-%m', date_emprunt) as mois,
    COUNT(*) as nombre_emprunts
FROM emprunts
GROUP BY strftime('%Y-%m', date_emprunt)
ORDER BY mois DESC;
```

#### **Évolution des inscriptions de membres**
```sql
-- English: Member registration evolution
-- Français: Évolution des inscriptions de membres
SELECT
    strftime('%Y-%m', date_inscription) as mois,
    COUNT(*) as nouveaux_membres
FROM membres
GROUP BY strftime('%Y-%m', date_inscription)
ORDER BY mois DESC;
```

### 🔄 7. Requêtes de Maintenance

#### **Livres jamais empruntés**
```sql
-- English: Books never borrowed
-- Français: Livres jamais empruntés
SELECT
    l.titre,
    l.auteur,
    l.categorie,
    l.created_at as date_ajout
FROM livres l
LEFT JOIN emprunts e ON l.id = e.livre_id
WHERE e.id IS NULL
ORDER BY l.created_at DESC;
```

#### **Membres inactifs (sans emprunts récents)**
```sql
-- English: Inactive members (no recent loans)
-- Français: Membres inactifs (sans emprunts récents)
SELECT
    m.nom,
    m.prenom,
    m.telephone,
    MAX(e.date_emprunt) as dernier_emprunt
FROM membres m
LEFT JOIN emprunts e ON m.id = e.membre_id
WHERE m.is_active = 1
GROUP BY m.id, m.nom, m.prenom, m.telephone
HAVING MAX(e.date_emprunt) < date('now', '-6 months')
   OR MAX(e.date_emprunt) IS NULL
ORDER BY dernier_emprunt ASC;
```

### 📊 8. Tableaux de Bord

#### **Résumé complet de la bibliothèque**
```sql
-- English: Complete library summary
-- Français: Résumé complet de la bibliothèque
SELECT
    'Livres' as type,
    (SELECT COUNT(*) FROM livres) as total,
    (SELECT COUNT(*) FROM livres WHERE disponible = 1) as disponibles,
    (SELECT COUNT(*) FROM livres WHERE disponible = 0) as empruntes
UNION ALL
SELECT
    'Membres' as type,
    (SELECT COUNT(*) FROM membres WHERE is_active = 1) as total,
    (SELECT COUNT(DISTINCT membre_id) FROM emprunts WHERE statut = 'actif') as actifs,
    (SELECT COUNT(*) FROM membres WHERE is_active = 1) -
    (SELECT COUNT(DISTINCT membre_id) FROM emprunts WHERE statut = 'actif') as inactifs
UNION ALL
SELECT
    'Emprunts' as type,
    (SELECT COUNT(*) FROM emprunts) as total,
    (SELECT COUNT(*) FROM emprunts WHERE statut = 'actif') as actifs,
    (SELECT COUNT(*) FROM emprunts WHERE statut = 'retourne') as termines;
```

#### **Alertes et notifications**
```sql
-- English: Alerts and notifications
-- Français: Alertes et notifications
SELECT
    'RETARD' as type_alerte,
    l.titre as livre,
    m.prenom || ' ' || m.nom as membre,
    m.telephone,
    e.date_retour_prevue,
    (julianday('now') - julianday(e.date_retour_prevue)) as jours_retard
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif'
AND date('now') > e.date_retour_prevue

UNION ALL

SELECT
    'BIENTOT_DU' as type_alerte,
    l.titre as livre,
    m.prenom || ' ' || m.nom as membre,
    m.telephone,
    e.date_retour_prevue,
    (julianday(e.date_retour_prevue) - julianday('now')) as jours_restants
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif'
AND date('now') <= e.date_retour_prevue
AND date('now', '+3 days') >= e.date_retour_prevue

ORDER BY type_alerte, jours_retard DESC;
```

---

## 🛡️ Maintenance et Sécurité

### 💾 Sauvegarde de la Base de Données

#### **Sauvegarde Manuelle**
1. **Fermez BiblioTech complètement**
2. Naviguez vers : `%APPDATA%\bibliotech\`
3. **Copiez** le fichier `bibliotech.db`
4. **Collez-le** dans un dossier de sauvegarde avec la date :
   ```
   Sauvegarde_BiblioTech_2024-12-15.db
   ```

#### **Script de Sauvegarde Automatique**
Créez un fichier `sauvegarde.bat` :
```batch
@echo off
set date=%date:~-4,4%-%date:~-10,2%-%date:~-7,2%
copy "%APPDATA%\bibliotech\bibliotech.db" "C:\Sauvegardes\BiblioTech_%date%.db"
echo Sauvegarde terminée : BiblioTech_%date%.db
pause
```

### 🔧 Optimisation des Performances

#### **Nettoyage de la base (à exécuter périodiquement)**
```sql
-- English: Database cleanup and optimization
-- Français: Nettoyage et optimisation de la base
VACUUM;
ANALYZE;
```

#### **Vérification de l'intégrité**
```sql
-- English: Check database integrity
-- Français: Vérifier l'intégrité de la base
PRAGMA integrity_check;
```

### 🔒 Sécurité et Bonnes Pratiques

#### ✅ **À FAIRE :**
- **Sauvegarde régulière** (hebdomadaire minimum)
- **Fermer BiblioTech** avant d'ouvrir la base avec d'autres outils
- **Mode lecture seule** pour les consultations
- **Vérifier les permissions** d'accès au fichier
- **Utiliser des mots de passe forts** pour les comptes administrateurs

#### ❌ **À ÉVITER :**
- Modifier directement la base pendant que BiblioTech fonctionne
- Supprimer ou déplacer le fichier `bibliotech.db`
- Partager la base sans chiffrement
- Ouvrir avec plusieurs outils simultanément
- Donner accès à des utilisateurs non autorisés

---

## 🚨 Résolution de Problèmes

### ⚠️ Problèmes Courants

#### **"Base de données verrouillée"**
**Cause :** BiblioTech est encore ouvert ou s'est fermé incorrectement
**Solution :**
1. Fermez complètement BiblioTech
2. Redémarrez votre ordinateur si nécessaire
3. Réessayez d'ouvrir la base

#### **"Fichier introuvable"**
**Cause :** La base de données n'existe pas ou a été déplacée
**Solution :**
1. Vérifiez le chemin : `%APPDATA%\bibliotech\bibliotech.db`
2. Lancez BiblioTech une fois pour créer la base
3. Vérifiez les permissions du dossier

#### **"Accès refusé"**
**Cause :** Permissions insuffisantes
**Solution :**
1. Clic droit sur DB Browser → "Exécuter en tant qu'administrateur"
2. Vérifiez les permissions du fichier `bibliotech.db`
3. Contactez votre administrateur système

#### **"Données corrompues"**
**Cause :** Arrêt brutal ou problème matériel
**Solution :**
1. Restaurez depuis votre dernière sauvegarde
2. Utilisez la commande : `PRAGMA integrity_check;`
3. Contactez le support technique

### 📞 Support et Assistance

#### **Niveaux de Support :**
1. **Auto-assistance** : Consultez cette documentation
2. **Support interne** : Contactez votre administrateur BiblioTech
3. **Support technique** : Contactez l'équipe de développement

#### **Informations à fournir en cas de problème :**
- Version de BiblioTech
- Système d'exploitation
- Message d'erreur exact
- Actions effectuées avant le problème
- Capture d'écran si possible

---

## 📚 Annexes

### 🎓 Glossaire des Termes

**Base de données :** Système organisé de stockage d'informations
**Table :** Section de la base contenant un type de données
**Enregistrement :** Une ligne de données dans une table
**Champ :** Une colonne spécifique dans une table
**Clé primaire :** Identifiant unique d'un enregistrement
**SQL :** Langage de requête pour bases de données
**Sauvegarde :** Copie de sécurité des données
**Intégrité :** Cohérence et validité des données

### 📋 Aide-Mémoire des Commandes SQL

```sql
-- Sélection de base
SELECT * FROM table_name;

-- Avec conditions
SELECT * FROM table_name WHERE condition;

-- Tri des résultats
SELECT * FROM table_name ORDER BY column_name;

-- Groupement
SELECT column, COUNT(*) FROM table_name GROUP BY column;

-- Jointures
SELECT * FROM table1 JOIN table2 ON table1.id = table2.foreign_id;

-- Recherche partielle
SELECT * FROM table_name WHERE column LIKE '%texte%';
```

---

**📅 Dernière mise à jour :** Décembre 2024
**🔧 Version BiblioTech :** 1.0.2
**👥 Destiné aux :** Utilisateurs non techniques
**📖 Niveau :** Débutant à Intermédiaire
```
import React, { useState } from 'react';
import type { LibraryMember } from '../types/member';
import type { Book } from '../types/library';
import ModalPortal from './ModalPortal';
import ExtendLoanModal from './ExtendLoanModal';
import ConfirmReturnModal from './ConfirmReturnModal';
import ProfileImage from './ProfileImage';

interface MemberProfileProps {
  member: LibraryMember;
  borrowedBooks: Book[];
  isOpen: boolean;
  onClose: () => void;
  onReturnBook?: (bookId: string) => void;
  onExtendLoan?: (bookId: string, newDueDate: string) => void;
}

const MemberProfile: React.FC<MemberProfileProps> = ({
  member,
  borrowedBooks,
  isOpen,
  onClose,
  onReturnBook,
  onExtendLoan
}) => {
  const [extendLoanModal, setExtendLoanModal] = useState<{
    isOpen: boolean;
    bookId: string;
    bookTitle: string;
    currentDueDate: string;
  }>({
    isOpen: false,
    bookId: '',
    bookTitle: '',
    currentDueDate: ''
  });

  const [confirmReturnModal, setConfirmReturnModal] = useState<{
    isOpen: boolean;
    bookId: string;
    bookTitle: string;
    bookAuthor: string;
    loanStartDate: string;
    originalDueDate: string;
  }>({
    isOpen: false,
    bookId: '',
    bookTitle: '',
    bookAuthor: '',
    loanStartDate: '',
    originalDueDate: ''
  });

  if (!isOpen) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  const isOverdue = (dueDate: Date) => {
    return new Date() > dueDate;
  };

  const getDaysUntilDue = (dueDate: Date) => {
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const handleExtendLoanClick = (book: Book) => {
    setExtendLoanModal({
      isOpen: true,
      bookId: book.id.toString(),
      bookTitle: book.titre,
      currentDueDate: book.dateRetour || new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    });
  };

  const handleExtendLoanConfirm = (newDueDate: string) => {
    if (onExtendLoan) {
      // Call the loan extension function
      onExtendLoan(extendLoanModal.bookId, newDueDate);

      // Close the modal after a short delay to allow for state updates
      setTimeout(() => {
        setExtendLoanModal({
          isOpen: false,
          bookId: '',
          bookTitle: '',
          currentDueDate: ''
        });
      }, 100);
    } else {
      // If no onExtendLoan callback, just close the modal
      setExtendLoanModal({
        isOpen: false,
        bookId: '',
        bookTitle: '',
        currentDueDate: ''
      });
    }
  };

  const handleExtendLoanClose = () => {
    setExtendLoanModal({
      isOpen: false,
      bookId: '',
      bookTitle: '',
      currentDueDate: ''
    });
  };

  const handleReturnBookClick = (book: Book) => {
    setConfirmReturnModal({
      isOpen: true,
      bookId: book.id.toString(),
      bookTitle: book.titre,
      bookAuthor: book.auteur,
      loanStartDate: book.dateEmprunt || new Date().toISOString().split('T')[0],
      originalDueDate: book.dateRetour || new Date().toISOString().split('T')[0]
    });
  };

  const handleReturnBookConfirm = () => {
    if (onReturnBook) {
      // Call the return book function
      onReturnBook(confirmReturnModal.bookId);

      // Close the modal after a short delay to allow for state updates
      setTimeout(() => {
        setConfirmReturnModal({
          isOpen: false,
          bookId: '',
          bookTitle: '',
          bookAuthor: '',
          loanStartDate: '',
          originalDueDate: ''
        });
      }, 100);
    } else {
      // If no onReturnBook callback, just close the modal
      setConfirmReturnModal({
        isOpen: false,
        bookId: '',
        bookTitle: '',
        bookAuthor: '',
        loanStartDate: '',
        originalDueDate: ''
      });
    }
  };

  const handleReturnBookClose = () => {
    setConfirmReturnModal({
      isOpen: false,
      bookId: '',
      bookTitle: '',
      bookAuthor: '',
      loanStartDate: '',
      originalDueDate: ''
    });
  };

  return (
    <>
      <ModalPortal isOpen={isOpen} onClose={onClose}>
        <div className="modal-content user-profile-modal" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">Profil du membre</h2>
          <button
            type="button"
            className="modal-close"
            onClick={onClose}
            aria-label="Fermer"
          >
            ×
          </button>
        </div>

        <div className="modal-body">
          <div className="user-profile-grid">
            <div className="profile-section">
              <div className="profile-header">
                <ProfileImage
                  src={member.photo}
                  alt={`${member.prenom} ${member.nom}`}
                  className="profile-avatar"
                  fallbackName={`${member.prenom} ${member.nom}`}
                  size={120}
                />
                <div className="profile-info">
                  <h3 className="profile-name">
                    {member.prenom} {member.nom}
                  </h3>
                  <p className="profile-function">{member.fonction}</p>
                  <div className="profile-status">
                    <span className={`status-badge ${member.isActive ? 'active' : 'inactive'}`}>
                      {member.isActive ? 'Actif' : 'Inactif'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="profile-details">
                <div className="detail-item">
                  <span className="detail-label">Numéro de membre</span>
                  <span className="detail-value">{member.numeroMembre}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Téléphone</span>
                  <span className="detail-value">{member.telephone}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Email</span>
                  <span className="detail-value">{member.email}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Adresse</span>
                  <span className="detail-value">{member.adresse}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Date d'inscription</span>
                  <span className="detail-value">
                    {member.dateInscription ? formatDate(new Date(member.dateInscription)) : 'Non renseignée'}
                  </span>
                </div>
              </div>
            </div>

            <div className="profile-section">
              <div className="borrowed-books-section">
                <div className="section-header">
                  <h4 className="section-title">
                    Livres empruntés ({borrowedBooks.length})
                  </h4>
                </div>

                {borrowedBooks.length > 0 ? (
                  <div className="borrowed-books-list">
                    {borrowedBooks.map((book) => {
                      const dueDate = new Date(book.dateRetour || Date.now() + 14 * 24 * 60 * 60 * 1000);
                      const overdue = isOverdue(dueDate);
                      const daysUntilDue = getDaysUntilDue(dueDate);

                      return (
                        <div key={book.id} className="borrowed-book-card">
                          <div className="book-info">
                            <h5 className="book-title">{book.titre}</h5>
                            <p className="book-author">par {book.auteur}</p>
                            <div className="book-dates">
                              <span className="date-item">
                                Emprunté le: {book.dateEmprunt ? formatDate(new Date(book.dateEmprunt)) : 'Date inconnue'}
                              </span>
                              <span className="date-item">
                                À rendre le: {formatDate(dueDate)}
                              </span>
                              {overdue && (
                                <span className="date-item" style={{ color: 'var(--color-error-600)', fontWeight: 'var(--font-semibold)' }}>
                                  En retard de {Math.abs(daysUntilDue)} jour(s)
                                </span>
                              )}
                              {!overdue && daysUntilDue <= 3 && (
                                <span className="date-item" style={{ color: 'var(--color-warning-600)', fontWeight: 'var(--font-semibold)' }}>
                                  À rendre dans {daysUntilDue} jour(s)
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="book-status">
                            {overdue && (
                              <span className="status-badge overdue">En retard</span>
                            )}
                            {!overdue && daysUntilDue <= 3 && (
                              <span className="status-badge warning">Bientôt dû</span>
                            )}
                          </div>

                          <div className="book-actions">
                            {onExtendLoan && (
                              <button
                                className="action-btn action-btn--warning action-btn--md"
                                onClick={() => handleExtendLoanClick(book)}
                              >
                                <span className="action-icon">📅</span>
                                <span className="action-label">Prolonger</span>
                              </button>
                            )}
                            {onReturnBook && (
                              <button
                                className="action-btn action-btn--success action-btn--md"
                                onClick={() => handleReturnBookClick(book)}
                              >
                                <span className="action-icon">✅</span>
                                <span className="action-label">Retourner</span>
                              </button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="empty-state">
                    <div className="empty-icon">📚</div>
                    <p>Aucun livre emprunté actuellement</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button
            type="button"
            className="action-btn action-btn--secondary"
            onClick={onClose}
          >
            Fermer
          </button>
        </div>
        </div>
      </ModalPortal>

    {/* Nested modals outside the main ModalPortal */}
    <ExtendLoanModal
      isOpen={extendLoanModal.isOpen}
      onClose={handleExtendLoanClose}
      onConfirm={handleExtendLoanConfirm}
      bookTitle={extendLoanModal.bookTitle}
      memberName={`${member.prenom} ${member.nom}`}
      currentDueDate={extendLoanModal.currentDueDate}
    />

    <ConfirmReturnModal
      isOpen={confirmReturnModal.isOpen}
      onClose={handleReturnBookClose}
      onConfirm={handleReturnBookConfirm}
      bookTitle={confirmReturnModal.bookTitle}
      bookAuthor={confirmReturnModal.bookAuthor}
      memberName={`${member.prenom} ${member.nom}`}
      loanStartDate={confirmReturnModal.loanStartDate}
      originalDueDate={confirmReturnModal.originalDueDate}
    />
    </>
  );
};

export default MemberProfile;
// Fixed modal rendering with ModalPortal

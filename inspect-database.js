// Script pour inspecter la base de données BiblioTech
// Database inspection script for BiblioTech

import Database from 'better-sqlite3';
import path from 'path';
import os from 'os';

// Chemin vers la base de données
const dbPath = path.join(os.homedir(), 'AppData', 'Roaming', 'bibliotech', 'bibliotech.db');

console.log('🔍 Inspection de la base de données BiblioTech');
console.log('📁 Chemin:', dbPath);
console.log('=' * 60);

try {
  // Ouvrir la base de données
  const db = new Database(dbPath, { readonly: true });
  
  console.log('\n📊 STRUCTURE DE LA BASE DE DONNÉES');
  console.log('=' * 40);
  
  // Lister toutes les tables
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
    ORDER BY name
  `).all();
  
  console.log('\n🗂️  Tables disponibles:');
  tables.forEach(table => {
    console.log(`  - ${table.name}`);
  });
  
  // Examiner chaque table
  for (const table of tables) {
    console.log(`\n\n📋 TABLE: ${table.name.toUpperCase()}`);
    console.log('-' * 30);
    
    // Structure de la table
    const columns = db.prepare(`PRAGMA table_info(${table.name})`).all();
    console.log('\n🏗️  Structure:');
    columns.forEach(col => {
      const nullable = col.notnull ? 'NOT NULL' : 'NULL';
      const pk = col.pk ? ' (PRIMARY KEY)' : '';
      console.log(`  ${col.name}: ${col.type} ${nullable}${pk}`);
    });
    
    // Compter les enregistrements
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
    console.log(`\n📈 Nombre d'enregistrements: ${count.count}`);
    
    // Afficher quelques exemples (max 3)
    if (count.count > 0) {
      const samples = db.prepare(`SELECT * FROM ${table.name} LIMIT 3`).all();
      console.log('\n📝 Exemples de données:');
      samples.forEach((row, index) => {
        console.log(`  ${index + 1}. ${JSON.stringify(row, null, 2)}`);
      });
      
      if (count.count > 3) {
        console.log(`  ... et ${count.count - 3} autres enregistrements`);
      }
    }
  }
  
  console.log('\n\n🎯 STATISTIQUES RAPIDES');
  console.log('=' * 30);
  
  // Statistiques spécifiques à BiblioTech
  try {
    const stats = {
      livres: db.prepare('SELECT COUNT(*) as count FROM livres').get().count,
      categories: db.prepare('SELECT COUNT(*) as count FROM categories').get().count,
      membres: db.prepare('SELECT COUNT(*) as count FROM membres').get().count,
      utilisateurs: db.prepare('SELECT COUNT(*) as count FROM utilisateurs').get().count,
      emprunts_actifs: db.prepare("SELECT COUNT(*) as count FROM emprunts WHERE statut = 'actif'").get().count,
      livres_disponibles: db.prepare('SELECT COUNT(*) as count FROM livres WHERE disponible = 1').get().count
    };
    
    console.log(`📚 Livres total: ${stats.livres}`);
    console.log(`📖 Livres disponibles: ${stats.livres_disponibles}`);
    console.log(`📑 Livres empruntés: ${stats.livres - stats.livres_disponibles}`);
    console.log(`🏷️  Catégories: ${stats.categories}`);
    console.log(`👥 Membres: ${stats.membres}`);
    console.log(`👤 Utilisateurs système: ${stats.utilisateurs}`);
    console.log(`📋 Emprunts actifs: ${stats.emprunts_actifs}`);
    
  } catch (error) {
    console.log('⚠️  Impossible de calculer les statistiques:', error.message);
  }
  
  db.close();
  console.log('\n✅ Inspection terminée avec succès!');
  
} catch (error) {
  console.error('❌ Erreur lors de l\'inspection:', error.message);
  console.log('\n💡 Assurez-vous que:');
  console.log('  1. La base de données existe à l\'emplacement spécifié');
  console.log('  2. BiblioTech n\'est pas en cours d\'exécution (fermer l\'app)');
  console.log('  3. Vous avez les permissions de lecture sur le fichier');
}

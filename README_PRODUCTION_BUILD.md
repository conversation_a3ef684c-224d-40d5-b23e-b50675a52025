# BiblioTech - Production Windows Installer

## 🎯 Quick Start

### For Developers - Building the Installer

#### Option 1: Automated Build (Recommended)
```bash
# Windows PowerShell
.\build-production-installer.ps1

# Windows Command Prompt
build-installer.bat
```

#### Option 2: Manual Build
```bash
npm install
npm run clean
npm run build-installer
```

### For End Users - Installing BiblioTech

1. Download `BiblioTech-Setup-1.0.2.exe`
2. Double-click to run installer
3. If Windows shows security warning:
   - Click "More info"
   - Click "Run anyway"
4. Follow installation wizard
5. Launch from desktop shortcut

## 📋 System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Memory**: 4GB RAM minimum (8GB recommended)
- **Storage**: 500MB free disk space
- **Network**: None required (100% offline operation)

## 🔐 Default Login Accounts

- **Super Administrator**
  - Username: `superadmin`
  - Password: `admin123`

- **Administrator**
  - Username: `admin1`
  - Password: `admin123`

## ✨ Features

### 📚 Book Management
- Add, edit, delete books
- Category organization
- Advanced search and filtering
- ISBN and publication year tracking
- Availability status management

### 👥 Member Management
- Member registration with photos
- Automatic membership number generation
- Contact information management
- Membership card printing
- Borrowing history tracking

### 📊 Loan Tracking
- Book checkout and return
- Due date management
- Overdue notifications
- Loan extensions
- Complete borrowing history

### 📈 Dashboard & Analytics
- Real-time statistics
- Quick navigation cards
- Member and book summaries
- Loan status overview

### 🔧 Administration
- User account management
- Role-based access control
- Category management
- Audit logging
- Data backup and restore

## 🗂️ File Locations

### Application Installation
- **Default Location**: `C:\Program Files\BiblioTech\`
- **User Data**: `%APPDATA%\BiblioTech\`

### Database
- **Location**: `%APPDATA%\BiblioTech\bibliotech.db`
- **Type**: SQLite database
- **Backup**: Use built-in backup feature

### Member Photos
- **Location**: `%APPDATA%\BiblioTech\usersimages\`
- **Formats**: JPG, PNG, GIF
- **Size Limit**: 5MB per image

## 🛠️ Troubleshooting

### Installation Issues

#### "Windows protected your PC" Warning
**Cause**: Unsigned installer
**Solution**: Click "More info" → "Run anyway"

#### Installation Fails
**Cause**: Insufficient permissions
**Solution**: Right-click installer → "Run as administrator"

### Application Issues

#### Database Not Found
**Cause**: Corrupted or missing database
**Solution**: Delete `%APPDATA%\BiblioTech\bibliotech.db` and restart app

#### Member Photos Not Displaying
**Cause**: Missing image files
**Solution**: Check `%APPDATA%\BiblioTech\usersimages\` folder permissions

#### Login Issues
**Cause**: Incorrect credentials
**Solution**: Use default accounts or reset database

## 🔄 Updates and Maintenance

### Updating BiblioTech
1. Download new installer
2. Run installer (will update existing installation)
3. User data and database preserved automatically

### Backing Up Data
1. Open BiblioTech
2. Go to Administration → Data Management
3. Click "Create Backup"
4. Save backup file to secure location

### Restoring Data
1. Open BiblioTech
2. Go to Administration → Data Management
3. Click "Restore Backup"
4. Select backup file

## 📞 Support

### Common Questions

**Q: Does BiblioTech require internet connection?**
A: No, BiblioTech works completely offline.

**Q: Can multiple users access the same database?**
A: BiblioTech is designed for single-computer use. For multi-user access, install on a shared computer.

**Q: How do I add more user accounts?**
A: Login as Super Admin → Administration → User Management → Add User

**Q: Can I customize categories?**
A: Yes, go to Category Management to add, edit, or reorder categories.

**Q: How do I print membership cards?**
A: Go to Member Management → Select member → Print Card

### Technical Support

For technical issues:
1. Check this troubleshooting guide
2. Verify system requirements
3. Try restarting the application
4. Check Windows Event Viewer for errors

## 🔒 Security & Privacy

### Data Security
- All data stored locally on your computer
- No data transmitted over internet
- SQLite database with file-level security
- User passwords encrypted

### Privacy
- No telemetry or usage tracking
- No external connections
- Complete user control over data
- GDPR compliant

## 📄 License

BiblioTech is proprietary software. All rights reserved.

## 🏗️ Technical Details

### Built With
- **Frontend**: React 19.1.0 with TypeScript
- **Backend**: Electron 36.4.0
- **Database**: SQLite with better-sqlite3
- **UI Framework**: Custom CSS with responsive design
- **Build Tools**: Vite, electron-builder, NSIS

### Architecture
- **Main Process**: Electron main thread handling system integration
- **Renderer Process**: React application with secure IPC communication
- **Database Layer**: SQLite with automatic schema management
- **File System**: Local storage for user data and images

---

**Version**: 1.0.2  
**Build Date**: January 2025  
**Platform**: Windows 10/11 (64-bit)  
**Size**: ~320MB installed

import React, { useState, useCallback } from 'react';
import { EyeIcon, EyeOffIcon, ShieldIcon } from './Icons';
import { useDebouncedCallback } from '../hooks/useDebounce';
import type { CreateUserData } from '../types/auth';

interface FirstTimeSetupProps {
  onSetupComplete: (userData: CreateUserData) => Promise<boolean>;
}

const FirstTimeSetup: React.FC<FirstTimeSetupProps> = ({ onSetupComplete }) => {
  const [formData, setFormData] = useState<CreateUserData>({
    username: '',
    email: '',
    password: '',
    role: 'super_admin'
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = 'Le nom d\'utilisateur est requis';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Le nom d\'utilisateur doit contenir au moins 3 caractères';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Le nom d\'utilisateur ne peut contenir que des lettres, chiffres et underscores';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre';
    }

    // Confirm password validation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise';
    } else if (formData.password !== confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const success = await onSetupComplete(formData);
      
      if (!success) {
        setErrors({ general: 'Erreur lors de la création du compte Super Admin. Veuillez réessayer.' });
      }
    } catch (error) {
      console.error('Setup error:', error);
      setErrors({ general: 'Une erreur inattendue s\'est produite. Veuillez réessayer.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Optimized input change handler with immediate UI updates and debounced validation
  const handleInputChange = useCallback((field: keyof CreateUserData, value: string) => {
    // Immediate UI update for responsive feel
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error immediately when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  // Debounced validation to prevent excessive processing
  const debouncedValidation = useDebouncedCallback((field: string, value: string) => {
    // Perform field-specific validation if needed
    // This runs 300ms after user stops typing
    if (field === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      setErrors(prev => ({ ...prev, [field]: 'Format d\'email invalide' }));
    }
    if (field === 'confirmPassword' && value && value !== formData.password) {
      setErrors(prev => ({ ...prev, confirmPassword: 'Les mots de passe ne correspondent pas' }));
    }
  }, 300);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md border border-gray-100">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
            <ShieldIcon size={40} className="text-white" />
          </div>
          <h1 className="text-3xl font-bold mb-2" style={{ color: '#1e293b' }}>Configuration initiale</h1>
          <p className="text-center leading-relaxed text-sm" style={{ color: '#475569' }}>
            Bienvenue dans BiblioTech ! Pour commencer, créez votre compte Super Administrateur qui vous permettra de gérer le système.
          </p>
        </div>

        {/* Setup Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Error */}
          {errors.general && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600 text-sm">{errors.general}</p>
            </div>
          )}

          {/* Username Field */}
          <div>
            <label htmlFor="username" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
              Nom d'utilisateur *
            </label>
            <input
              type="text"
              id="username"
              value={formData.username}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange('username', value);
                debouncedValidation('username', value);
              }}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                errors.username ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
              }`}
              placeholder="Choisissez votre nom d'utilisateur"
              disabled={isLoading}
              autoComplete="username"
              style={{ fontSize: '16px' }}
            />
            {errors.username && <p className="text-red-600 text-sm mt-1">{errors.username}</p>}
          </div>

          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
              Adresse email *
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => {
                const value = e.target.value;
                handleInputChange('email', value);
                debouncedValidation('email', value);
              }}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                errors.email ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
              }`}
              placeholder="<EMAIL>"
              disabled={isLoading}
              autoComplete="email"
              style={{ fontSize: '16px' }}
            />
            {errors.email && <p className="text-red-600 text-sm mt-1">{errors.email}</p>}
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
              Mot de passe *
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={formData.password}
                onChange={(e) => {
                  const value = e.target.value;
                  handleInputChange('password', value);
                  debouncedValidation('password', value);
                }}
                className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                  errors.password ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                placeholder="Créez un mot de passe sécurisé"
                disabled={isLoading}
                autoComplete="new-password"
                style={{ fontSize: '16px' }}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                disabled={isLoading}
              >
                {showPassword ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
              </button>
            </div>
            {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password}</p>}
            <p className="text-xs mt-1" style={{ color: '#64748b' }}>
              Minimum 8 caractères avec au moins une minuscule, une majuscule et un chiffre
            </p>
          </div>

          {/* Confirm Password Field */}
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-semibold mb-2" style={{ color: '#1e293b' }}>
              Confirmer le mot de passe *
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => {
                  const value = e.target.value;
                  setConfirmPassword(value);
                  // Clear error immediately for responsive feel
                  if (errors.confirmPassword) {
                    setErrors(prev => ({ ...prev, confirmPassword: '' }));
                  }
                  // Debounced validation for password match
                  debouncedValidation('confirmPassword', value);
                }}
                className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-blue-600 transition-all duration-200 ${
                  errors.confirmPassword ? 'border-red-400 bg-red-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                placeholder="Confirmez votre mot de passe"
                disabled={isLoading}
                autoComplete="new-password"
                style={{ fontSize: '16px' }}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                disabled={isLoading}
              >
                {showConfirmPassword ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
              </button>
            </div>
            {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-3 px-4 rounded-lg font-semibold focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg border-0"
            style={{
              backgroundColor: '#1e40af', // Dark blue background
              color: '#ffffff', // White text for contrast
              border: 'none',
              fontSize: '16px',
              fontWeight: '600'
            }}
            onMouseEnter={(e) => {
              if (!isLoading) {
                e.currentTarget.style.backgroundColor = '#1d4ed8'; // Slightly lighter blue on hover
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading) {
                e.currentTarget.style.backgroundColor = '#1e40af'; // Back to original
              }
            }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                <span style={{ color: '#ffffff', fontWeight: '600' }}>Configuration en cours...</span>
              </div>
            ) : (
              <span style={{ color: '#ffffff', fontWeight: '600' }}>Créer le compte Super Admin</span>
            )}
          </button>
        </form>

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <ShieldIcon size={20} className="text-blue-700 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-semibold" style={{ color: '#1e40af' }}>Sécurité</p>
              <p className="text-xs mt-1" style={{ color: '#1e40af' }}>
                Ce compte aura tous les privilèges administrateur. Choisissez un mot de passe fort et gardez-le en sécurité.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FirstTimeSetup;

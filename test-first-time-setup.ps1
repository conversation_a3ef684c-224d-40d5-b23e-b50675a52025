# Test script for BiblioTech first-time setup
Write-Host "🧪 Testing BiblioTech First-Time Setup" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Define paths
$dataDir = "$env:APPDATA\bibliotech"
$dbPath = "$dataDir\bibliotech.db"
$backupPath = "$dataDir\bibliotech.db.backup"

Write-Host "📁 Data directory: $dataDir" -ForegroundColor Yellow
Write-Host "🗄️ Database path: $dbPath" -ForegroundColor Yellow

# Check if database exists
if (Test-Path $dbPath) {
    Write-Host "💾 Existing database found - creating backup..." -ForegroundColor Green
    
    # Create backup
    Copy-Item $dbPath $backupPath -Force
    Write-Host "✅ Backup created: $backupPath" -ForegroundColor Green
    
    # Remove original database to trigger first-time setup
    Remove-Item $dbPath -Force
    Write-Host "🗑️ Original database removed" -ForegroundColor Yellow
} else {
    Write-Host "ℹ️ No existing database found - perfect for first-time setup test" -ForegroundColor Blue
}

Write-Host ""
Write-Host "🚀 Starting BiblioTech for first-time setup test..." -ForegroundColor Cyan
Write-Host "Expected behavior:" -ForegroundColor White
Write-Host "  1. First-time setup screen should appear" -ForegroundColor Gray
Write-Host "  2. Create Super Admin account form" -ForegroundColor Gray
Write-Host "  3. No demo credentials should be visible" -ForegroundColor Gray
Write-Host "  4. After setup, normal login screen should appear" -ForegroundColor Gray
Write-Host ""

# Build and run the application
Write-Host "🔨 Building application..." -ForegroundColor Yellow
npm run build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build successful" -ForegroundColor Green
    Write-Host "🎯 Launching application..." -ForegroundColor Yellow
    
    # Start the application
    Start-Process -FilePath "npm" -ArgumentList "run", "electron" -NoNewWindow
    
    Write-Host ""
    Write-Host "📋 Test Checklist:" -ForegroundColor Cyan
    Write-Host "  [ ] First-time setup screen appears" -ForegroundColor White
    Write-Host "  [ ] No demo credentials visible" -ForegroundColor White
    Write-Host "  [ ] Super Admin creation form works" -ForegroundColor White
    Write-Host "  [ ] Password visibility toggle works" -ForegroundColor White
    Write-Host "  [ ] Form validation works properly" -ForegroundColor White
    Write-Host "  [ ] After setup, login screen appears" -ForegroundColor White
    Write-Host "  [ ] Can login with created Super Admin" -ForegroundColor White
    Write-Host "  [ ] Audit logs are accessible (Super Admin only)" -ForegroundColor White
    Write-Host "  [ ] Admin creation form has improved UI" -ForegroundColor White
    Write-Host ""
    
    Write-Host "⏳ Close the application when testing is complete..." -ForegroundColor Yellow
    Write-Host "Press any key to restore the original database..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
    # Restore original database if backup exists
    if (Test-Path $backupPath) {
        Write-Host "🔄 Restoring original database..." -ForegroundColor Yellow
        Copy-Item $backupPath $dbPath -Force
        Remove-Item $backupPath -Force
        Write-Host "✅ Original database restored" -ForegroundColor Green
    }
} else {
    Write-Host "❌ Build failed - check errors above" -ForegroundColor Red
}

Write-Host ""
Write-Host "🏁 Test completed!" -ForegroundColor Cyan

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BiblioTech - Guide Interactif SQL</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header h1 {
            color: #2563eb;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            color: #666;
        }
        
        .query-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            margin-bottom: 25px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .query-section:hover {
            transform: translateY(-5px);
        }
        
        .section-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-header h2 {
            font-size: 1.4em;
        }
        
        .toggle-icon {
            font-size: 1.5em;
            transition: transform 0.3s ease;
        }
        
        .section-content {
            padding: 25px;
            display: none;
        }
        
        .section-content.active {
            display: block;
        }
        
        .query-item {
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .query-title {
            background: #f8fafc;
            padding: 15px;
            font-weight: bold;
            color: #1f2937;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .query-description {
            padding: 15px;
            background: #fefefe;
            font-style: italic;
            color: #6b7280;
        }
        
        .query-code {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }
        
        .copy-btn:hover {
            background: #2563eb;
        }
        
        .sql-comment {
            color: #94a3b8;
        }
        
        .sql-keyword {
            color: #60a5fa;
            font-weight: bold;
        }
        
        .sql-string {
            color: #34d399;
        }
        
        .sql-function {
            color: #fbbf24;
        }
        
        .tips-box {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .nav-item {
            display: block;
            padding: 8px 12px;
            color: #4f46e5;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 5px;
            transition: background 0.3s ease;
            font-size: 14px;
        }
        
        .nav-item:hover {
            background: #4f46e5;
            color: white;
        }
        
        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Guide Interactif SQL BiblioTech</h1>
            <p>Maîtrisez votre base de données en quelques clics</p>
        </div>

        <div class="navigation">
            <a href="#stats" class="nav-item">📊 Statistiques</a>
            <a href="#recherches" class="nav-item">🔍 Recherches</a>
            <a href="#emprunts" class="nav-item">📋 Emprunts</a>
            <a href="#analyses" class="nav-item">📈 Analyses</a>
            <a href="#maintenance" class="nav-item">🛠️ Maintenance</a>
        </div>

        <div class="query-section" id="stats">
            <div class="section-header" onclick="toggleSection('stats')">
                <h2>📊 Statistiques Essentielles</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="tips-box">
                    <strong>💡 Conseil :</strong> Ces requêtes vous donnent un aperçu rapide de l'état de votre bibliothèque. Exécutez-les régulièrement pour suivre l'évolution.
                </div>

                <div class="query-item">
                    <div class="query-title">Nombre total de livres</div>
                    <div class="query-description">Comptabilise tous les livres présents dans votre catalogue</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Count all books
-- Français: Compter tous les livres</span>
<span class="sql-keyword">SELECT</span> <span class="sql-function">COUNT</span>(*) <span class="sql-keyword">as</span> total_livres <span class="sql-keyword">FROM</span> livres;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Répartition disponible/emprunté</div>
                    <div class="query-description">Affiche combien de livres sont disponibles vs empruntés</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Available vs borrowed books
-- Français: Livres disponibles vs empruntés</span>
<span class="sql-keyword">SELECT</span> 
    <span class="sql-function">SUM</span>(<span class="sql-keyword">CASE WHEN</span> disponible = 1 <span class="sql-keyword">THEN</span> 1 <span class="sql-keyword">ELSE</span> 0 <span class="sql-keyword">END</span>) <span class="sql-keyword">as</span> disponibles,
    <span class="sql-function">SUM</span>(<span class="sql-keyword">CASE WHEN</span> disponible = 0 <span class="sql-keyword">THEN</span> 1 <span class="sql-keyword">ELSE</span> 0 <span class="sql-keyword">END</span>) <span class="sql-keyword">as</span> empruntes
<span class="sql-keyword">FROM</span> livres;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Membres actifs</div>
                    <div class="query-description">Compte le nombre de membres ayant un statut actif</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Count active members
-- Français: Compter les membres actifs</span>
<span class="sql-keyword">SELECT</span> <span class="sql-function">COUNT</span>(*) <span class="sql-keyword">as</span> membres_actifs 
<span class="sql-keyword">FROM</span> membres 
<span class="sql-keyword">WHERE</span> is_active = 1;
                    </div>
                </div>
            </div>
        </div>

        <div class="query-section" id="recherches">
            <div class="section-header" onclick="toggleSection('recherches')">
                <h2>🔍 Recherches Courantes</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="warning-box">
                    <strong>⚠️ Important :</strong> Remplacez les valeurs entre [CROCHETS] par vos propres termes de recherche.
                </div>

                <div class="query-item">
                    <div class="query-title">Rechercher un livre par titre</div>
                    <div class="query-description">Trouve tous les livres contenant un mot dans le titre</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Search book by title
-- Français: Rechercher un livre par titre</span>
<span class="sql-keyword">SELECT</span> titre, auteur, categorie, disponible 
<span class="sql-keyword">FROM</span> livres 
<span class="sql-keyword">WHERE</span> titre <span class="sql-keyword">LIKE</span> <span class="sql-string">'%[TITRE]%'</span>;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Livres d'un auteur spécifique</div>
                    <div class="query-description">Liste tous les ouvrages d'un auteur donné</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Books by specific author
-- Français: Livres d'un auteur spécifique</span>
<span class="sql-keyword">SELECT</span> titre, categorie, annee_publication, disponible
<span class="sql-keyword">FROM</span> livres 
<span class="sql-keyword">WHERE</span> auteur <span class="sql-keyword">LIKE</span> <span class="sql-string">'%[AUTEUR]%'</span>
<span class="sql-keyword">ORDER BY</span> titre;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Rechercher un membre</div>
                    <div class="query-description">Trouve un membre par nom ou prénom</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Search member by name
-- Français: Rechercher un membre par nom</span>
<span class="sql-keyword">SELECT</span> nom, prenom, numero_membre, telephone, email
<span class="sql-keyword">FROM</span> membres 
<span class="sql-keyword">WHERE</span> nom <span class="sql-keyword">LIKE</span> <span class="sql-string">'%[NOM]%'</span> <span class="sql-keyword">OR</span> prenom <span class="sql-keyword">LIKE</span> <span class="sql-string">'%[PRENOM]%'</span>;
                    </div>
                </div>
            </div>
        </div>

        <div class="query-section" id="emprunts">
            <div class="section-header" onclick="toggleSection('emprunts')">
                <h2>📋 Gestion des Emprunts</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="tips-box">
                    <strong>💡 Astuce :</strong> Ces requêtes sont parfaites pour le suivi quotidien des emprunts et la gestion des retards.
                </div>

                <div class="query-item">
                    <div class="query-title">Emprunts actuellement actifs</div>
                    <div class="query-description">Liste complète des livres actuellement empruntés avec les détails des emprunteurs</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Currently active loans
-- Français: Emprunts actuellement actifs</span>
<span class="sql-keyword">SELECT</span> 
    l.titre <span class="sql-keyword">as</span> livre,
    m.prenom || <span class="sql-string">' '</span> || m.nom <span class="sql-keyword">as</span> emprunteur,
    m.telephone,
    e.date_emprunt,
    e.date_retour_prevue
<span class="sql-keyword">FROM</span> emprunts e
<span class="sql-keyword">JOIN</span> livres l <span class="sql-keyword">ON</span> e.livre_id = l.id
<span class="sql-keyword">JOIN</span> membres m <span class="sql-keyword">ON</span> e.membre_id = m.id
<span class="sql-keyword">WHERE</span> e.statut = <span class="sql-string">'actif'</span>
<span class="sql-keyword">ORDER BY</span> e.date_retour_prevue;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Livres en retard</div>
                    <div class="query-description">Identifie tous les livres non rendus après la date prévue</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Overdue books
-- Français: Livres en retard</span>
<span class="sql-keyword">SELECT</span> 
    l.titre,
    m.prenom || <span class="sql-string">' '</span> || m.nom <span class="sql-keyword">as</span> emprunteur,
    m.telephone,
    e.date_retour_prevue,
    (<span class="sql-function">julianday</span>(<span class="sql-string">'now'</span>) - <span class="sql-function">julianday</span>(e.date_retour_prevue)) <span class="sql-keyword">as</span> jours_retard
<span class="sql-keyword">FROM</span> emprunts e
<span class="sql-keyword">JOIN</span> livres l <span class="sql-keyword">ON</span> e.livre_id = l.id
<span class="sql-keyword">JOIN</span> membres m <span class="sql-keyword">ON</span> e.membre_id = m.id
<span class="sql-keyword">WHERE</span> e.statut = <span class="sql-string">'actif'</span> 
<span class="sql-keyword">AND</span> <span class="sql-function">date</span>(<span class="sql-string">'now'</span>) > e.date_retour_prevue
<span class="sql-keyword">ORDER BY</span> jours_retard <span class="sql-keyword">DESC</span>;
                    </div>
                </div>
            </div>
        </div>

        <div class="query-section" id="analyses">
            <div class="section-header" onclick="toggleSection('analyses')">
                <h2>📈 Analyses et Rapports</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="query-item">
                    <div class="query-title">Top 10 des livres les plus empruntés</div>
                    <div class="query-description">Identifie les ouvrages les plus populaires de votre bibliothèque</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Top 10 most borrowed books
-- Français: Top 10 des livres les plus empruntés</span>
<span class="sql-keyword">SELECT</span> 
    l.titre,
    l.auteur,
    <span class="sql-function">COUNT</span>(e.id) <span class="sql-keyword">as</span> nombre_emprunts
<span class="sql-keyword">FROM</span> livres l
<span class="sql-keyword">LEFT JOIN</span> emprunts e <span class="sql-keyword">ON</span> l.id = e.livre_id
<span class="sql-keyword">GROUP BY</span> l.id, l.titre, l.auteur
<span class="sql-keyword">HAVING</span> <span class="sql-function">COUNT</span>(e.id) > 0
<span class="sql-keyword">ORDER BY</span> nombre_emprunts <span class="sql-keyword">DESC</span>
<span class="sql-keyword">LIMIT</span> 10;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Répartition des livres par catégorie</div>
                    <div class="query-description">Analyse la distribution de votre collection par catégorie</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Book distribution by category
-- Français: Répartition des livres par catégorie</span>
<span class="sql-keyword">SELECT</span> 
    c.nom <span class="sql-keyword">as</span> categorie,
    <span class="sql-function">COUNT</span>(l.id) <span class="sql-keyword">as</span> nombre_livres,
    <span class="sql-function">ROUND</span>(<span class="sql-function">COUNT</span>(l.id) * 100.0 / (<span class="sql-keyword">SELECT</span> <span class="sql-function">COUNT</span>(*) <span class="sql-keyword">FROM</span> livres), 1) <span class="sql-keyword">as</span> pourcentage
<span class="sql-keyword">FROM</span> categories c
<span class="sql-keyword">LEFT JOIN</span> livres l <span class="sql-keyword">ON</span> c.nom = l.categorie
<span class="sql-keyword">GROUP BY</span> c.nom
<span class="sql-keyword">ORDER BY</span> nombre_livres <span class="sql-keyword">DESC</span>;
                    </div>
                </div>
            </div>
        </div>

        <div class="query-section" id="maintenance">
            <div class="section-header" onclick="toggleSection('maintenance')">
                <h2>🛠️ Maintenance et Optimisation</h2>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="warning-box">
                    <strong>⚠️ Attention :</strong> Ces requêtes modifient la base de données. Assurez-vous d'avoir une sauvegarde avant de les exécuter.
                </div>

                <div class="query-item">
                    <div class="query-title">Optimiser la base de données</div>
                    <div class="query-description">Nettoie et optimise la base pour de meilleures performances</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Optimize database
-- Français: Optimiser la base de données</span>
<span class="sql-keyword">VACUUM</span>;
<span class="sql-keyword">ANALYZE</span>;
                    </div>
                </div>

                <div class="query-item">
                    <div class="query-title">Vérifier l'intégrité</div>
                    <div class="query-description">Contrôle la cohérence et l'intégrité de vos données</div>
                    <div class="query-code">
                        <button class="copy-btn" onclick="copyQuery(this)">Copier</button>
<span class="sql-comment">-- English: Check database integrity
-- Français: Vérifier l'intégrité de la base</span>
<span class="sql-keyword">PRAGMA</span> integrity_check;
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSection(sectionId) {
            const content = document.querySelector(`#${sectionId} .section-content`);
            const icon = document.querySelector(`#${sectionId} .toggle-icon`);
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.textContent = '▼';
            } else {
                content.classList.add('active');
                icon.textContent = '▲';
            }
        }

        function copyQuery(button) {
            const codeBlock = button.parentElement;
            const text = codeBlock.textContent.replace('Copier', '').trim();
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '✓ Copié!';
                button.style.background = '#10b981';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#3b82f6';
                }, 2000);
            });
        }

        // Smooth scrolling for navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = item.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                targetElement.scrollIntoView({ behavior: 'smooth' });
            });
        });

        // Auto-open first section
        document.addEventListener('DOMContentLoaded', () => {
            toggleSection('stats');
        });
    </script>
</body>
</html>

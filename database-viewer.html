<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BiblioTech - Visualiseur de Base de Données</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 10px;
        }
        .info-box {
            background: #e0f2fe;
            border: 1px solid #0288d1;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .warning-box {
            background: #fff3e0;
            border: 1px solid #f57c00;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #1976d2;
            border-bottom: 2px solid #e3f2fd;
            padding-bottom: 10px;
        }
        .table-structure {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .table-name {
            font-size: 18px;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .column {
            background: white;
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
        }
        .primary-key {
            border-left-color: #ff9800;
            background: #fff8e1;
        }
        .code {
            background: #263238;
            color: #ffffff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .download-link {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        .download-link:hover {
            background: #1d4ed8;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ BiblioTech - Guide de la Base de Données</h1>
        
        <div class="info-box">
            <h3>📍 Emplacement de la Base de Données</h3>
            <p><strong>Chemin:</strong> <code>C:\Users\<USER>\AppData\Roaming\bibliotech\bibliotech.db</code></p>
            <p><strong>Type:</strong> SQLite Database (.db)</p>
            <p><strong>Taille approximative:</strong> 50-500 KB (selon les données)</p>
        </div>

        <div class="section">
            <h2>🛠️ Comment Ouvrir et Examiner la Base de Données</h2>
            
            <div class="step">
                <h3>Méthode 1: DB Browser for SQLite (Recommandée)</h3>
                <p>Outil graphique gratuit et facile à utiliser:</p>
                <a href="https://sqlitebrowser.org/dl/" class="download-link" target="_blank">
                    📥 Télécharger DB Browser for SQLite
                </a>
                <ol>
                    <li>Téléchargez et installez DB Browser for SQLite</li>
                    <li>Ouvrez l'application</li>
                    <li>Cliquez sur "Open Database"</li>
                    <li>Naviguez vers: <code>C:\Users\<USER>\AppData\Roaming\bibliotech\bibliotech.db</code></li>
                    <li>Explorez les tables et données</li>
                </ol>
            </div>

            <div class="step">
                <h3>Méthode 2: SQLite Online Viewer</h3>
                <p>Visualiseur en ligne (pour des fichiers de petite taille):</p>
                <a href="https://sqliteviewer.app/" class="download-link" target="_blank">
                    🌐 SQLite Viewer Online
                </a>
                <p><strong>⚠️ Attention:</strong> Ne téléchargez jamais de données sensibles sur des sites tiers!</p>
            </div>
        </div>

        <div class="section">
            <h2>🏗️ Structure de la Base de Données BiblioTech</h2>
            
            <div class="table-structure">
                <div class="table-name">📚 Table: livres</div>
                <div class="column primary-key">id - INTEGER (Clé primaire)</div>
                <div class="column">titre - TEXT (Titre du livre)</div>
                <div class="column">auteur - TEXT (Auteur du livre)</div>
                <div class="column">categorie - TEXT (Catégorie)</div>
                <div class="column">genre - TEXT (Genre littéraire)</div>
                <div class="column">isbn - TEXT (Code ISBN)</div>
                <div class="column">annee_publication - INTEGER (Année)</div>
                <div class="column">description - TEXT (Description)</div>
                <div class="column">disponible - BOOLEAN (1=disponible, 0=emprunté)</div>
                <div class="column">created_at - TEXT (Date de création)</div>
                <div class="column">updated_at - TEXT (Date de modification)</div>
            </div>

            <div class="table-structure">
                <div class="table-name">🏷️ Table: categories</div>
                <div class="column primary-key">id - INTEGER (Clé primaire)</div>
                <div class="column">nom - TEXT UNIQUE (Nom de la catégorie)</div>
                <div class="column">description - TEXT (Description)</div>
                <div class="column">ordre - INTEGER (Ordre d'affichage)</div>
                <div class="column">created_at - TEXT (Date de création)</div>
                <div class="column">updated_at - TEXT (Date de modification)</div>
            </div>

            <div class="table-structure">
                <div class="table-name">👥 Table: membres</div>
                <div class="column primary-key">id - INTEGER (Clé primaire)</div>
                <div class="column">nom - TEXT (Nom de famille)</div>
                <div class="column">prenom - TEXT (Prénom)</div>
                <div class="column">fonction - TEXT (Fonction/Profession)</div>
                <div class="column">numero_membre - TEXT UNIQUE (Numéro de membre)</div>
                <div class="column">telephone - TEXT (Téléphone)</div>
                <div class="column">adresse - TEXT (Adresse)</div>
                <div class="column">email - TEXT (Email)</div>
                <div class="column">photo - TEXT (URL de la photo)</div>
                <div class="column">date_inscription - TEXT (Date d'inscription)</div>
                <div class="column">is_active - BOOLEAN (Membre actif)</div>
                <div class="column">created_at - TEXT (Date de création)</div>
                <div class="column">updated_at - TEXT (Date de modification)</div>
            </div>

            <div class="table-structure">
                <div class="table-name">📋 Table: emprunts</div>
                <div class="column primary-key">id - INTEGER (Clé primaire)</div>
                <div class="column">livre_id - INTEGER (ID du livre)</div>
                <div class="column">membre_id - INTEGER (ID du membre)</div>
                <div class="column">date_emprunt - TEXT (Date d'emprunt)</div>
                <div class="column">date_retour_prevue - TEXT (Date de retour prévue)</div>
                <div class="column">date_retour_effective - TEXT (Date de retour effective)</div>
                <div class="column">statut - TEXT (actif, retourne, en_retard)</div>
                <div class="column">created_at - TEXT (Date de création)</div>
            </div>

            <div class="table-structure">
                <div class="table-name">👤 Table: utilisateurs</div>
                <div class="column primary-key">id - INTEGER (Clé primaire)</div>
                <div class="column">username - TEXT UNIQUE (Nom d'utilisateur)</div>
                <div class="column">email - TEXT (Email)</div>
                <div class="column">password_hash - TEXT (Mot de passe chiffré)</div>
                <div class="column">role - TEXT (super_admin, administrator)</div>
                <div class="column">is_active - BOOLEAN (Utilisateur actif)</div>
                <div class="column">last_login - TEXT (Dernière connexion)</div>
                <div class="column">created_at - TEXT (Date de création)</div>
                <div class="column">updated_at - TEXT (Date de modification)</div>
            </div>

            <div class="table-structure">
                <div class="table-name">📊 Table: journaux_audit</div>
                <div class="column primary-key">id - INTEGER (Clé primaire)</div>
                <div class="column">user_id - INTEGER (ID utilisateur)</div>
                <div class="column">username - TEXT (Nom d'utilisateur)</div>
                <div class="column">action - TEXT (CREATE, UPDATE, DELETE, etc.)</div>
                <div class="column">resource - TEXT (BOOK, CATEGORY, MEMBER, etc.)</div>
                <div class="column">resource_id - INTEGER (ID de la ressource)</div>
                <div class="column">details - TEXT (Détails de l'action)</div>
                <div class="column">timestamp - TEXT (Horodatage)</div>
            </div>
        </div>

        <div class="section">
            <h2>🔍 Requêtes SQL Utiles</h2>
            
            <h3>Statistiques générales:</h3>
            <div class="code">
-- Nombre total de livres
SELECT COUNT(*) as total_livres FROM livres;

-- Livres disponibles vs empruntés
SELECT 
    SUM(CASE WHEN disponible = 1 THEN 1 ELSE 0 END) as disponibles,
    SUM(CASE WHEN disponible = 0 THEN 1 ELSE 0 END) as empruntes
FROM livres;

-- Nombre de membres actifs
SELECT COUNT(*) as membres_actifs FROM membres WHERE is_active = 1;
            </div>

            <h3>Livres par catégorie:</h3>
            <div class="code">
SELECT c.nom as categorie, COUNT(l.id) as nombre_livres
FROM categories c
LEFT JOIN livres l ON c.nom = l.categorie
GROUP BY c.nom
ORDER BY nombre_livres DESC;
            </div>

            <h3>Emprunts en cours:</h3>
            <div class="code">
SELECT 
    l.titre,
    m.prenom || ' ' || m.nom as emprunteur,
    e.date_emprunt,
    e.date_retour_prevue,
    e.statut
FROM emprunts e
JOIN livres l ON e.livre_id = l.id
JOIN membres m ON e.membre_id = m.id
WHERE e.statut = 'actif'
ORDER BY e.date_retour_prevue;
            </div>
        </div>

        <div class="warning-box">
            <h3>⚠️ Précautions Importantes</h3>
            <ul>
                <li><strong>Sauvegarde:</strong> Toujours faire une copie de la base avant modification</li>
                <li><strong>Fermer l'app:</strong> Fermez BiblioTech avant d'ouvrir la base avec un autre outil</li>
                <li><strong>Lecture seule:</strong> Utilisez le mode lecture seule pour éviter la corruption</li>
                <li><strong>Permissions:</strong> Assurez-vous d'avoir les droits d'accès au fichier</li>
            </ul>
        </div>

        <div class="section">
            <h2>📖 Explication pour les Utilisateurs</h2>
            <p>Voici comment expliquer le fonctionnement de la base de données à vos utilisateurs:</p>
            
            <div class="step">
                <h3>🎯 Concept Simple</h3>
                <p>"BiblioTech stocke toutes vos données dans un fichier spécial appelé 'base de données'. 
                C'est comme un classeur électronique très organisé qui contient tous vos livres, membres, 
                et historiques d'emprunts."</p>
            </div>

            <div class="step">
                <h3>📁 Où sont stockées les données</h3>
                <p>"Toutes vos données sont sauvegardées automatiquement sur votre ordinateur dans un dossier 
                sécurisé. Même si vous fermez l'application, vos données restent en sécurité."</p>
            </div>

            <div class="step">
                <h3>🔄 Sauvegarde automatique</h3>
                <p>"Chaque fois que vous ajoutez un livre, créez un membre, ou enregistrez un emprunt, 
                l'information est immédiatement sauvegardée. Vous n'avez rien à faire de spécial."</p>
            </div>

            <div class="step">
                <h3>🛡️ Sécurité des données</h3>
                <p>"Vos données sont stockées localement sur votre ordinateur, pas sur internet. 
                Seuls les utilisateurs autorisés avec un compte administrateur peuvent y accéder."</p>
            </div>
        </div>
    </div>
</body>
</html>
